{% extends "base.html" %}

{% block title %}权重配置 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-weight-hanging text-warning me-2"></i>权重配置管理</h2>
                    <p class="text-muted mb-0">管理风险评估指标的权重配置方案</p>
                </div>
                <div>
                    <a href="{{ url_for('risk_assessment.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回主页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 当前配置卡片 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-star me-2"></i>当前使用的权重配置</h5>
                </div>
                <div class="card-body">
                    <div id="currentConfigContainer">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载权重配置...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- 权重调节器 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-sliders-h me-2"></i>权重调节器
                        <span id="weightSliderStatus" class="badge bg-secondary ms-2">默认权重</span>
                    </h5>
                    <div>
                        <button class="btn btn-warning btn-sm me-2" onclick="resetToDefault()">
                            <i class="fas fa-undo me-1"></i>重置默认
                        </button>
                        <button class="btn btn-danger btn-sm me-2" id="clearAppliedBtn" onclick="clearAppliedWeights()" style="display: none;">
                            <i class="fas fa-times me-1"></i>清除应用
                        </button>
                        <button class="btn btn-primary btn-sm me-2" id="applyWeightBtn" onclick="applyCurrentWeights()">
                            <i class="fas fa-check me-1"></i>应用权重
                        </button>
                        <button class="btn btn-success btn-sm" onclick="saveCurrentConfig()">
                            <i class="fas fa-save me-1"></i>保存配置
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 发生概率权重 -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3"><i class="fas fa-cloud-rain text-primary me-2"></i>发生概率权重 (P)</h6>

                            <div class="mb-3">
                                <label class="form-label d-flex justify-content-between">
                                    <span>全年降水量 (w_R)</span>
                                    <span class="badge bg-primary" id="rainfallWeightValue">63.3%</span>
                                </label>
                                <input type="range" class="form-range" id="rainfallWeight"
                                       min="0" max="1" step="0.001" value="0.633"
                                       oninput="updateWeight('rainfall', this.value)">
                            </div>

                            <div class="mb-3">
                                <label class="form-label d-flex justify-content-between">
                                    <span>排水管网密度 (w_D)</span>
                                    <span class="badge bg-success" id="drainageWeightValue">26.0%</span>
                                </label>
                                <input type="range" class="form-range" id="drainageWeight"
                                       min="0" max="1" step="0.001" value="0.260"
                                       oninput="updateWeight('drainage', this.value)">
                            </div>

                            <div class="mb-3">
                                <label class="form-label d-flex justify-content-between">
                                    <span>平均高程值 (w_T)</span>
                                    <span class="badge bg-warning" id="elevationWeightValue">10.7%</span>
                                </label>
                                <input type="range" class="form-range" id="elevationWeight"
                                       min="0" max="1" step="0.001" value="0.107"
                                       oninput="updateWeight('elevation', this.value)">
                            </div>

                            <div class="alert alert-info">
                                <small><strong>权重总和:</strong> <span id="probabilityWeightSum">100.0%</span></small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6 class="mb-3"><i class="fas fa-users text-warning me-2"></i>影响程度权重 (I)</h6>

                            <div class="mb-3">
                                <label class="form-label d-flex justify-content-between">
                                    <span>GDP密度 (ω₄)</span>
                                    <span class="badge bg-info" id="gdpWeightValue">60.0%</span>
                                </label>
                                <input type="range" class="form-range" id="gdpWeight"
                                       min="0" max="1" step="0.001" value="0.6"
                                       oninput="updateWeight('gdp', this.value)">
                            </div>

                            <div class="mb-3">
                                <label class="form-label d-flex justify-content-between">
                                    <span>人口密度 (ω₅)</span>
                                    <span class="badge bg-secondary" id="populationWeightValue">40.0%</span>
                                </label>
                                <input type="range" class="form-range" id="populationWeight"
                                       min="0" max="1" step="0.001" value="0.4"
                                       oninput="updateWeight('population', this.value)">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">权重类型快速设置</label>
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setImpactWeightType('economic')">经济优先</button>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="setImpactWeightType('balanced')">均衡型</button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="setImpactWeightType('population')">人口优先</button>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <small><strong>权重总和:</strong> <span id="impactWeightSum">100.0%</span></small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6 class="mb-3"><i class="fas fa-users text-warning me-2"></i>影响程度权重 (I)</h6>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-info-circle me-2"></i>影响程度权重配置</h6>
                                <p class="mb-2">影响程度计算支持三种权重配置类型：</p>
                                <ul class="mb-2">
                                    <li><strong>经济优先型 (ω₄ = 0.6, ω₅ = 0.4)：</strong><br>
                                        <small>适用于经济发达地区（如产业园区），强调内涝对经济系统的冲击，适合经济损失敏感区域。</small></li>
                                    <li><strong>均衡型 (ω₄ = 0.5, ω₅ = 0.5)：</strong><br>
                                        <small>适用于经济与人口并重的区域，平衡考虑经济损失，适合一般城市内涝管理。</small></li>
                                    <li><strong>人口优先型 (ω₄ = 0.4, ω₅ = 0.6)：</strong><br>
                                        <small>适用于人口密集但经济相对落后的地区，强调对人员安全的保护。</small></li>
                                </ul>
                            </div>

                            <h6 class="mb-3 mt-4"><i class="fas fa-formula text-info me-2"></i>计算公式</h6>
                            <div class="bg-light p-3 rounded">
                                <p class="mb-2"><strong>发生概率:</strong><br>
                                <code>P = w_R × X_降水量 + w_D × X_排水密度 + w_T × X_高程</code></p>

                                <p class="mb-2"><strong>影响程度:</strong><br>
                                <code>I = ω₄ × X_GDP密度 + ω₅ × X_人口密度</code></p>

                                <p class="mb-0"><strong>综合风险指数:</strong><br>
                                <code>RI = P × I</code></p>
                            </div>
                        </div>
                    </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="mb-3"><i class="fas fa-chart-pie text-success me-2"></i>权重可视化</h6>
                            <canvas id="weightVisualizationChart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 配置历史 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>配置历史</h5>
                </div>
                <div class="card-body">
                    <div id="configHistoryContainer">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载配置历史...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let currentWeights = {
    rainfall: 0.633,
    drainage: 0.260,
    elevation: 0.107,
    gdp: 0.6,
    population: 0.4
};

let appliedWeights = null; // 当前应用的权重配置
let weightChart = null;
let configHistory = [];

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 先加载默认配置和历史
    loadCurrentConfig();
    loadConfigHistory();
    initializeWeightChart();

    // 然后加载应用的权重（会覆盖默认配置显示）
    setTimeout(() => {
        loadAppliedWeights();
    }, 100); // 稍微延迟确保默认配置先加载完成
});

// 加载当前配置
function loadCurrentConfig() {
    fetch('/risk-assessment/api/weight-configs')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.configs.length > 0) {
                const defaultConfig = data.configs.find(c => c.is_default) || data.configs[0];
                updateWeightSliders(defaultConfig);
                // 先显示默认配置，稍后会被应用权重覆盖（如果有的话）
                displayCurrentActiveConfig();
            } else {
                showError('加载权重配置失败');
            }
        })
        .catch(error => {
            console.error('Error loading config:', error);
            showError('加载权重配置时发生错误');
        });
}

// 显示当前配置
function displayCurrentConfig(config) {
    const container = document.getElementById('currentConfigContainer');
    
    const html = `
        <div class="row">
            <div class="col-md-8">
                <h6>${config.config_name} ${config.is_default ? '<span class="badge bg-primary ms-2">默认</span>' : ''}</h6>
                <p class="text-muted mb-3">${config.description || '无描述'}</p>
                
                <div class="row">
                    <div class="col-6">
                        <div class="mb-2">
                            <span class="badge bg-primary me-2">w_R</span>
                            降雨权重: <strong>${(config.rainfall_weight * 100).toFixed(1)}%</strong>
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-success me-2">w_D</span>
                            排水权重: <strong>${(config.drainage_weight * 100).toFixed(1)}%</strong>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="mb-2">
                            <span class="badge bg-warning me-2">w_T</span>
                            地形权重: <strong>${(config.elevation_weight * 100).toFixed(1)}%</strong>
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-info me-2">I</span>
                            影响程度: GDP(60%) + 人口(40%)
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <div class="mb-2">
                        <small class="text-muted">创建时间</small><br>
                        <strong>${new Date(config.created_at).toLocaleString()}</strong>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">权重总和</small><br>
                        <strong class="text-success">100.0%</strong>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

// 显示当前实际使用的权重配置
function displayCurrentActiveConfig() {
    const container = document.getElementById('currentConfigContainer');

    // 确定当前使用的权重配置
    let activeWeights, configName, configDescription, configSource, configTime;

    if (appliedWeights) {
        // 如果有应用的权重，使用应用的权重
        activeWeights = appliedWeights;
        configName = '用户应用的权重配置';
        configDescription = '当前应用的自定义权重配置';
        configSource = '应用权重';
        configTime = appliedWeights.appliedAt;
    } else {
        // 否则使用当前权重调节器中的权重
        activeWeights = currentWeights;
        const statusElement = document.getElementById('weightSliderStatus');
        const status = statusElement ? statusElement.textContent : '默认权重';

        switch(status) {
            case '已加载':
                configName = '已加载的权重配置';
                configDescription = '从配置历史中加载的权重配置';
                configSource = '已加载';
                break;
            case '已修改':
                configName = '修改后的权重配置';
                configDescription = '用户手动调整的权重配置';
                configSource = '已修改';
                break;
            default:
                configName = '系统默认权重配置';
                configDescription = '基于新风险评估模型的默认权重配置方案';
                configSource = '默认配置';
                break;
        }
        configTime = new Date().toLocaleString();
    }

    const html = `
        <div class="row">
            <div class="col-md-8">
                <h6>${configName} <span class="badge bg-info ms-2">${configSource}</span></h6>
                <p class="text-muted mb-3">${configDescription}</p>

                <div class="row">
                    <div class="col-6">
                        <div class="mb-2">
                            <span class="badge bg-primary me-2">w_R</span>
                            降雨权重: <strong>${(activeWeights.rainfall * 100).toFixed(1)}%</strong>
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-success me-2">w_D</span>
                            排水权重: <strong>${(activeWeights.drainage * 100).toFixed(1)}%</strong>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="mb-2">
                            <span class="badge bg-warning me-2">w_T</span>
                            地形权重: <strong>${(activeWeights.elevation * 100).toFixed(1)}%</strong>
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-info me-2">ω₄</span>
                            GDP权重: <strong>${((activeWeights.gdp || 0.6) * 100).toFixed(1)}%</strong>
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-secondary me-2">ω₅</span>
                            人口权重: <strong>${((activeWeights.population || 0.4) * 100).toFixed(1)}%</strong>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <div class="mb-2">
                        <small class="text-muted">更新时间</small><br>
                        <strong>${configTime}</strong>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">权重总和</small><br>
                        <strong class="text-success">${((activeWeights.rainfall + activeWeights.drainage + activeWeights.elevation) * 100).toFixed(1)}%</strong>
                    </div>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

// 更新权重滑块
function updateWeightSliders(config) {
    currentWeights = {
        rainfall: config.rainfall_weight,
        drainage: config.drainage_weight,
        elevation: config.elevation_weight,
        gdp: config.gdp_weight || 0.6,
        population: config.population_weight || 0.4
    };

    document.getElementById('rainfallWeight').value = config.rainfall_weight;
    document.getElementById('drainageWeight').value = config.drainage_weight;
    document.getElementById('elevationWeight').value = config.elevation_weight;
    document.getElementById('gdpWeight').value = config.gdp_weight || 0.6;
    document.getElementById('populationWeight').value = config.population_weight || 0.4;

    // 更新影响程度选择器
    const impactTypeSelect = document.getElementById('impactTypeSelect');
    if (impactTypeSelect) {
        const gdpWeight = config.gdp_weight || 0.6;
        const populationWeight = config.population_weight || 0.4;

        if (gdpWeight === 0.6 && populationWeight === 0.4) {
            impactTypeSelect.value = 'economic';
        } else if (gdpWeight === 0.5 && populationWeight === 0.5) {
            impactTypeSelect.value = 'balanced';
        } else if (gdpWeight === 0.4 && populationWeight === 0.6) {
            impactTypeSelect.value = 'population';
        } else {
            impactTypeSelect.value = 'custom';
        }
    }

    updateWeightDisplays();
    updateWeightChart();
}

// 更新权重显示
function updateWeightDisplays() {
    // 更新发生概率权重显示
    document.getElementById('rainfallWeightValue').textContent = (currentWeights.rainfall * 100).toFixed(1) + '%';
    document.getElementById('drainageWeightValue').textContent = (currentWeights.drainage * 100).toFixed(1) + '%';
    document.getElementById('elevationWeightValue').textContent = (currentWeights.elevation * 100).toFixed(1) + '%';

    // 更新影响程度权重显示
    document.getElementById('gdpWeightValue').textContent = (currentWeights.gdp * 100).toFixed(1) + '%';
    document.getElementById('populationWeightValue').textContent = (currentWeights.population * 100).toFixed(1) + '%';

    // 计算发生概率权重总和
    const probabilityTotal = currentWeights.rainfall + currentWeights.drainage + currentWeights.elevation;
    document.getElementById('probabilityWeightSum').textContent = (probabilityTotal * 100).toFixed(1) + '%';

    // 计算影响程度权重总和
    const impactTotal = currentWeights.gdp + currentWeights.population;
    document.getElementById('impactWeightSum').textContent = (impactTotal * 100).toFixed(1) + '%';

    // 检查发生概率权重总和是否为1
    const probabilitySumElement = document.getElementById('probabilityWeightSum');
    if (Math.abs(probabilityTotal - 1.0) > 0.001) {
        probabilitySumElement.className = 'text-danger fw-bold';
    } else {
        probabilitySumElement.className = 'text-success fw-bold';
    }

    // 检查影响程度权重总和是否为1
    const impactSumElement = document.getElementById('impactWeightSum');
    if (Math.abs(impactTotal - 1.0) > 0.001) {
        impactSumElement.className = 'text-danger fw-bold';
    } else {
        impactSumElement.className = 'text-success fw-bold';
    }
}

// 更新权重值
function updateWeight(type, value) {
    currentWeights[type] = parseFloat(value);
    updateWeightDisplays();
    updateWeightChart();

    // 如果当前显示的是应用权重或已加载状态，用户手动调整后标记为已修改
    const statusElement = document.getElementById('weightSliderStatus');
    const currentStatus = statusElement.textContent;
    if (currentStatus === '应用权重' || currentStatus === '已加载') {
        updateWeightSliderStatus('modified');
    }

    // 更新蓝色卡片显示
    displayCurrentActiveConfig();
}

// 设置影响程度权重类型
function setImpactWeightType(type) {
    switch(type) {
        case 'economic':
            currentWeights.gdp = 0.6;
            currentWeights.population = 0.4;
            break;
        case 'balanced':
            currentWeights.gdp = 0.5;
            currentWeights.population = 0.5;
            break;
        case 'population':
            currentWeights.gdp = 0.4;
            currentWeights.population = 0.6;
            break;
    }

    // 更新滑块位置
    document.getElementById('gdpWeight').value = currentWeights.gdp;
    document.getElementById('populationWeight').value = currentWeights.population;

    // 更新显示
    updateWeightDisplays();
    updateWeightChart();

    // 标记为已修改
    const statusElement = document.getElementById('weightSliderStatus');
    const currentStatus = statusElement.textContent;
    if (currentStatus === '应用权重' || currentStatus === '已加载') {
        updateWeightSliderStatus('modified');
    }

    // 更新蓝色卡片显示
    displayCurrentActiveConfig();
}

// 初始化权重图表
function initializeWeightChart() {
    const ctx = document.getElementById('weightVisualizationChart').getContext('2d');

    weightChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['全年降水量', '排水管网密度', '平均高程值'],
            datasets: [{
                data: [currentWeights.rainfall, currentWeights.drainage, currentWeights.elevation],
                backgroundColor: ['#007bff', '#28a745', '#ffc107'],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const percentage = (context.parsed * 100).toFixed(1);
                            return `${context.label}: ${percentage}%`;
                        }
                    }
                }
            }
        }
    });
}

// 更新权重图表
function updateWeightChart() {
    if (weightChart) {
        weightChart.data.datasets[0].data = [
            currentWeights.rainfall,
            currentWeights.drainage,
            currentWeights.elevation
        ];
        weightChart.update();
    }
}

// 应用当前权重配置
function applyCurrentWeights() {
    // 验证发生概率权重总和
    const probabilityTotal = currentWeights.rainfall + currentWeights.drainage + currentWeights.elevation;
    if (Math.abs(probabilityTotal - 1.0) > 0.001) {
        alert('发生概率权重总和必须等于100%，请调整权重值后再应用');
        return;
    }

    // 验证影响程度权重总和
    const impactTotal = currentWeights.gdp + currentWeights.population;
    if (Math.abs(impactTotal - 1.0) > 0.001) {
        alert('影响程度权重总和必须等于100%，请调整权重值后再应用');
        return;
    }

    if (confirm('确定要应用当前权重配置吗？\n\n应用后，所有风险评估计算都将使用这个权重配置。')) {
        // 禁用按钮并显示加载状态
        const applyBtn = document.getElementById('applyWeightBtn');
        const originalText = applyBtn.innerHTML;
        applyBtn.disabled = true;
        applyBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>应用中...';

        // 发送到后端保存
        const data = {
            rainfall_weight: currentWeights.rainfall,
            drainage_weight: currentWeights.drainage,
            elevation_weight: currentWeights.elevation,
            gdp_weight: currentWeights.gdp,
            population_weight: currentWeights.population
        };

        fetch('/risk-assessment/api/applied-weight', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // 保存应用的权重配置到前端
                appliedWeights = {
                    rainfall: currentWeights.rainfall,
                    drainage: currentWeights.drainage,
                    elevation: currentWeights.elevation,
                    gdp: currentWeights.gdp,
                    population: currentWeights.population,
                    appliedAt: new Date().toLocaleString()
                };

                // 更新权重调节器状态
                updateWeightSliderStatus('applied'); // 更新为应用权重状态

                // 更新蓝色卡片显示
                displayCurrentActiveConfig();

                // 显示成功消息
                alert('权重配置已应用！\n\n系统现在将使用这个权重配置进行所有风险评估计算。');
            } else {
                alert('应用权重配置失败: ' + result.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('应用权重配置时发生错误');
        })
        .finally(() => {
            // 恢复按钮状态
            applyBtn.disabled = false;
            applyBtn.innerHTML = originalText;
        });
    }
}


// 清除应用的权重配置
function clearAppliedWeights() {
    if (confirm('确定要清除当前应用的权重配置吗？\n\n清除后系统将使用默认权重配置进行计算。')) {
        fetch('/risk-assessment/api/applied-weight', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                appliedWeights = null;

                // 清除应用权重后，权重调节器回到默认配置
                resetToDefaultWeights();

                // 更新蓝色卡片显示
                displayCurrentActiveConfig();

                alert('已清除应用的权重配置，系统将使用默认权重。');
            } else {
                alert('清除权重配置失败: ' + result.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('清除权重配置时发生错误');
        });
    }
}

// 加载当前应用的权重配置
function loadAppliedWeights() {
    fetch('/risk-assessment/api/applied-weight')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.applied_weights) {
                appliedWeights = {
                    rainfall: data.applied_weights.rainfall_weight,
                    drainage: data.applied_weights.drainage_weight,
                    elevation: data.applied_weights.elevation_weight,
                    gdp: data.applied_weights.gdp_weight || 0.6,
                    population: data.applied_weights.population_weight || 0.4,
                    appliedAt: new Date(data.applied_weights.applied_at).toLocaleString()
                };

                // 如果有应用的权重，将其显示在权重调节器中
                currentWeights = {
                    rainfall: appliedWeights.rainfall,
                    drainage: appliedWeights.drainage,
                    elevation: appliedWeights.elevation,
                    gdp: appliedWeights.gdp,
                    population: appliedWeights.population
                };

                // 更新权重调节器的滑块和显示
                document.getElementById('rainfallWeight').value = appliedWeights.rainfall;
                document.getElementById('drainageWeight').value = appliedWeights.drainage;
                document.getElementById('elevationWeight').value = appliedWeights.elevation;
                document.getElementById('gdpWeight').value = appliedWeights.gdp;
                document.getElementById('populationWeight').value = appliedWeights.population;

                // 更新影响程度选择器
                const impactTypeSelect = document.getElementById('impactTypeSelect');
                if (impactTypeSelect) {
                    if (appliedWeights.gdp === 0.6 && appliedWeights.population === 0.4) {
                        impactTypeSelect.value = 'economic';
                    } else if (appliedWeights.gdp === 0.5 && appliedWeights.population === 0.5) {
                        impactTypeSelect.value = 'balanced';
                    } else if (appliedWeights.gdp === 0.4 && appliedWeights.population === 0.6) {
                        impactTypeSelect.value = 'population';
                    } else {
                        impactTypeSelect.value = 'custom';
                    }
                }

                updateWeightDisplays();
                updateWeightChart();
                updateWeightSliderStatus('applied'); // 显示为应用权重
            } else {
                updateWeightSliderStatus('default'); // 显示为默认权重
            }

            // 更新蓝色卡片显示
            displayCurrentActiveConfig();
        })
        .catch(error => {
            console.error('Error loading applied weights:', error);
        });
}

// 更新权重调节器状态指示器
function updateWeightSliderStatus(status = 'default') {
    const statusElement = document.getElementById('weightSliderStatus');
    const clearBtn = document.getElementById('clearAppliedBtn');

    switch(status) {
        case 'applied':
            statusElement.textContent = '应用权重';
            statusElement.className = 'badge bg-success ms-2';
            clearBtn.style.display = 'inline-block'; // 显示清除按钮
            break;
        case 'modified':
            statusElement.textContent = '已修改';
            statusElement.className = 'badge bg-warning ms-2';
            clearBtn.style.display = 'none'; // 隐藏清除按钮
            break;
        case 'loaded':
            statusElement.textContent = '已加载';
            statusElement.className = 'badge bg-info ms-2';
            clearBtn.style.display = 'none'; // 隐藏清除按钮
            break;
        default:
            statusElement.textContent = '默认权重';
            statusElement.className = 'badge bg-secondary ms-2';
            clearBtn.style.display = 'none'; // 隐藏清除按钮
            break;
    }
}

// 重置权重调节器到默认值（内部使用，无确认对话框）
function resetToDefaultWeights() {
    currentWeights = {
        rainfall: 0.633,
        drainage: 0.260,
        elevation: 0.107,
        gdp: 0.6,
        population: 0.4
    };

    document.getElementById('rainfallWeight').value = 0.633;
    document.getElementById('drainageWeight').value = 0.260;
    document.getElementById('elevationWeight').value = 0.107;

    // 重置影响程度权重选择器
    const impactTypeSelect = document.getElementById('impactTypeSelect');
    if (impactTypeSelect) {
        impactTypeSelect.value = 'economic'; // 默认为经济优先型
        updateImpactWeights(); // 更新影响程度权重
    }

    updateWeightDisplays();
    updateWeightChart();
    updateWeightSliderStatus('default'); // 显示为默认权重

    // 更新蓝色卡片显示
    displayCurrentActiveConfig();
}

// 重置为默认值（用户操作，有确认对话框）
function resetToDefault() {
    if (confirm('确定要重置为默认权重配置吗？')) {
        resetToDefaultWeights();
    }
}

// 保存当前配置
function saveCurrentConfig() {
    // 验证发生概率权重总和
    const probabilityTotal = currentWeights.rainfall + currentWeights.drainage + currentWeights.elevation;
    if (Math.abs(probabilityTotal - 1.0) > 0.001) {
        alert('发生概率权重总和必须等于100%，请调整权重值');
        return;
    }

    // 验证影响程度权重总和
    const impactTotal = currentWeights.gdp + currentWeights.population;
    if (Math.abs(impactTotal - 1.0) > 0.001) {
        alert('影响程度权重总和必须等于100%，请调整权重值');
        return;
    }

    // 直接提示用户输入配置名称
    const configName = prompt('请输入配置名称：');
    if (!configName || !configName.trim()) {
        return;
    }

    const description = prompt('请输入配置描述（可选）：') || '';

    const data = {
        config_name: configName.trim(),
        description: description.trim(),
        rainfall_weight: currentWeights.rainfall,
        drainage_weight: currentWeights.drainage,
        elevation_weight: currentWeights.elevation,
        gdp_weight: currentWeights.gdp,
        population_weight: currentWeights.population
    };

    fetch('/risk-assessment/api/weight-config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('配置保存成功');
            loadConfigHistory(); // 重新加载配置历史
        } else {
            alert('保存失败: ' + result.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('保存配置时发生错误');
    });
}

// 加载配置历史
function loadConfigHistory() {
    fetch('/risk-assessment/api/weight-configs')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                configHistory = data.configs;
                displayConfigHistory(data.configs);
            }
        })
        .catch(error => {
            console.error('Error loading history:', error);
        });
}

// 显示配置历史
function displayConfigHistory(configs) {
    const container = document.getElementById('configHistoryContainer');
    
    if (!configs || configs.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">暂无配置历史</p>';
        return;
    }
    
    let html = '<div class="table-responsive">';
    html += '<table class="table table-hover">';
    html += '<thead><tr><th>配置名称</th><th>描述</th><th>权重分布</th><th>创建时间</th><th>操作</th></tr></thead>';
    html += '<tbody>';
    
    configs.forEach(config => {
        html += `
            <tr>
                <td>
                    <strong>${config.config_name}</strong>
                    ${config.is_default ? '<span class="badge bg-primary ms-1">默认</span>' : ''}
                </td>
                <td>${config.description || '-'}</td>
                <td>
                    <small>
                        降雨:${(config.rainfall_weight * 100).toFixed(1)}%
                        排水:${(config.drainage_weight * 100).toFixed(1)}%
                        地形:${(config.elevation_weight * 100).toFixed(1)}%
                    </small>
                </td>
                <td><small>${new Date(config.created_at).toLocaleString()}</small></td>
                <td>
                    <button class="btn btn-outline-primary btn-sm me-1" onclick="loadConfig(${config.id})">
                        <i class="fas fa-upload"></i> 加载
                    </button>
                    ${!config.is_default ? `
                    <button class="btn btn-outline-danger btn-sm" onclick="deleteConfig(${config.id}, '${config.config_name}')">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                    ` : ''}
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// 加载指定配置
function loadConfig(configId) {
    const config = configHistory.find(c => c.id === configId);
    if (config) {
        updateWeightSliders(config);
        updateWeightSliderStatus('loaded'); // 加载配置时显示为已加载状态

        // 更新蓝色卡片显示
        displayCurrentActiveConfig();

        // 询问用户是否要立即应用这个配置
        if (confirm(`配置 "${config.config_name}" 已加载到权重调节器。\n\n是否要立即应用这个权重配置？\n\n点击"确定"立即应用，点击"取消"仅加载到调节器中。`)) {
            applyCurrentWeights();
        } else {
            alert('配置已加载到权重调节器，您可以进行调整后再点击"应用权重"按钮。');
        }
    }
}

// 删除配置
function deleteConfig(configId, configName) {
    if (!confirm(`确定要删除权重配置 "${configName}" 吗？\n\n此操作不可撤销！`)) {
        return;
    }

    fetch(`/risk-assessment/api/weight-config/${configId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(result.message);
            loadConfigHistory(); // 重新加载配置历史
        } else {
            alert('删除失败: ' + result.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('删除配置时发生错误');
    });
}

// 显示错误信息
function showError(message) {
    const alertHtml = `<div class="alert alert-danger alert-dismissible fade show" role="alert">
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>`;
    
    document.querySelector('.container-fluid').insertAdjacentHTML('afterbegin', alertHtml);
}
</script>

<style>
.form-range {
    height: 1.5rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.badge {
    font-size: 0.75em;
}

canvas {
    max-height: 300px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
}

.table td {
    font-size: 0.875rem;
    vertical-align: middle;
}

.alert {
    border: none;
    border-radius: 0.5rem;
}
</style>
{% endblock %}
