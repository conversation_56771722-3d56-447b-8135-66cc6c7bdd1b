"""
多工作表Excel文件导入器
支持导入包含多个年份数据的Excel文件
"""
import os
import re
from typing import Dict, List, Tuple, Optional
from datetime import datetime

# 尝试导入pandas，如果失败则设置标志
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
    DataFrame = pd.DataFrame
    ExcelFile = pd.ExcelFile
except ImportError:
    PANDAS_AVAILABLE = False
    pd = None
    # 创建虚拟类用于类型注解
    class DataFrame:
        pass
    class ExcelFile:
        pass


class MultiSheetExcelImporter:
    """多工作表Excel文件导入器"""
    
    # 列名映射
    COLUMN_MAPPING = {
        # 数值字段映射
        '排水管道密度（km/km²）': 'drainage_network_density',
        '排水管网密度（km/km²）': 'drainage_network_density',
        '排水管网密度': 'drainage_network_density',
        '全年降水量（mm）': 'annual_precipitation',
        '全年降水量': 'annual_precipitation',
        '平均高程（m）': 'elevation',
        '平均高程值': 'elevation',
        '平均高程': 'elevation',
        '人口密度（万人/km²）': 'population_density',
        '人口密度': 'population_density',
        'GDP密度（亿元/km²）': 'gdp_per_area',
        'GDP密度': 'gdp_per_area',

        # 地区名称列的各种可能名称
        'Unnamed: 0': 'location_name',
        '城市': 'location_name',
        '城市名称': 'location_name',
        '地区': 'location_name',
        '地区名称': 'location_name',
        '区域': 'location_name',
        '市': 'location_name',
        '地级市': 'location_name',
        '省份': 'location_name',
        '数据名称': 'data_name',
        '位置名称': 'location_name',
    }
    
    # 必需的数值字段
    REQUIRED_NUMERIC_FIELDS = [
        'annual_precipitation', 'drainage_network_density', 'elevation',
        'population_density', 'gdp_per_area'
    ]
    
    def __init__(self):
        self.supported_extensions = ['.xlsx', '.xls']
    
    def validate_file(self, file_path: str) -> Tuple[bool, str]:
        """验证文件是否有效"""
        if not os.path.exists(file_path):
            return False, "文件不存在"
        
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in self.supported_extensions:
            return False, f"不支持的文件格式: {file_ext}"
        
        try:
            # 尝试读取Excel文件
            excel_file = pd.ExcelFile(file_path)
            if len(excel_file.sheet_names) == 0:
                return False, "Excel文件中没有工作表"
            return True, "文件验证通过"
        except Exception as e:
            return False, f"文件读取失败: {str(e)}"
    
    def extract_year_from_sheet_name(self, sheet_name: str) -> Optional[int]:
        """从工作表名称中提取年份"""
        # 尝试直接转换为整数（如"2023"）
        try:
            year = int(sheet_name)
            if 1900 <= year <= 2100:  # 合理的年份范围
                return year
        except ValueError:
            pass
        
        # 使用正则表达式提取年份
        year_pattern = r'(\d{4})'
        match = re.search(year_pattern, sheet_name)
        if match:
            year = int(match.group(1))
            if 1900 <= year <= 2100:
                return year
        
        return None
    
    def read_sheet_data(self, excel_file: ExcelFile, sheet_name: str) -> Tuple[bool, Optional[DataFrame], Optional[int], str]:
        """读取单个工作表的数据"""
        try:
            print(f"\n=== 开始处理工作表: {sheet_name} ===")

            # 读取工作表
            df_raw = pd.read_excel(excel_file, sheet_name=sheet_name, header=None)
            print(f"原始数据形状: {df_raw.shape}")

            if df_raw.empty:
                print(f"工作表 {sheet_name} 为空")
                return False, None, None, f"工作表 {sheet_name} 为空"

            # 显示前几行数据用于调试
            print("前3行数据:")
            for i in range(min(3, len(df_raw))):
                print(f"  行{i}: {df_raw.iloc[i].tolist()}")

            # 提取年份 - 改进的提取逻辑
            year = self.extract_year_from_sheet_name(sheet_name)
            print(f"从工作表名 '{sheet_name}' 提取年份: {year}")

            if not year:
                # 尝试从前几行的各个位置提取年份
                for row_idx in range(min(3, len(df_raw))):
                    for col_idx in range(min(3, len(df_raw.columns))):
                        try:
                            cell_value = df_raw.iloc[row_idx, col_idx]
                            if pd.notna(cell_value):
                                # 尝试直接转换为年份
                                year_candidate = int(float(cell_value))
                                if 1900 <= year_candidate <= 2100:
                                    year = year_candidate
                                    print(f"从单元格 ({row_idx}, {col_idx}) 提取年份: {year}")
                                    break
                        except (ValueError, TypeError):
                            # 尝试从字符串中提取年份
                            try:
                                cell_str = str(cell_value)
                                year_match = re.search(r'(\d{4})', cell_str)
                                if year_match:
                                    year_candidate = int(year_match.group(1))
                                    if 1900 <= year_candidate <= 2100:
                                        year = year_candidate
                                        print(f"从字符串 '{cell_str}' 提取年份: {year}")
                                        break
                            except:
                                continue
                    if year:
                        break

            # 如果还是没有年份，使用默认年份
            if not year:
                year = 2023  # 使用默认年份
                print(f"无法提取年份，使用默认年份: {year}")
                # 不再返回错误，而是继续处理

            # 查找标题行 - 改进的识别逻辑
            header_row_idx = None
            for i in range(min(10, len(df_raw))):  # 检查前10行
                row = df_raw.iloc[i]
                # 检查是否包含标题关键词
                header_keywords = ['排水', '降水', '高程', '人口', 'GDP', '城市', '地区', '密度']
                keyword_count = 0

                for val in row:
                    if pd.notna(val) and isinstance(val, str):
                        val_str = str(val).strip()
                        if any(keyword in val_str for keyword in header_keywords):
                            keyword_count += 1

                # 如果找到2个或以上关键词，认为是标题行
                if keyword_count >= 2:
                    header_row_idx = i
                    break

                # 如果行中有5列或以上的非空值，也可能是标题行
                non_empty_count = sum(1 for val in row if pd.notna(val) and str(val).strip())
                if non_empty_count >= 5:
                    # 进一步检查是否包含至少一个关键词
                    if keyword_count >= 1:
                        header_row_idx = i
                        break

            # 如果还是没找到标题行，尝试使用第一行或第二行
            if header_row_idx is None:
                # 检查第一行是否有足够的数据
                if len(df_raw) > 0:
                    first_row_data_count = sum(1 for val in df_raw.iloc[0] if pd.notna(val) and str(val).strip())
                    if first_row_data_count >= 4:  # 至少4列有数据
                        header_row_idx = 0
                    elif len(df_raw) > 1:
                        second_row_data_count = sum(1 for val in df_raw.iloc[1] if pd.notna(val) and str(val).strip())
                        if second_row_data_count >= 4:
                            header_row_idx = 1

            if header_row_idx is None:
                return False, None, None, f"工作表 {sheet_name} 中未找到有效的标题行"

            # 读取数据，使用找到的标题行
            print(f"使用第 {header_row_idx} 行作为标题行")
            df = pd.read_excel(excel_file, sheet_name=sheet_name, header=header_row_idx)
            print(f"读取后数据形状: {df.shape}")
            print(f"列名: {list(df.columns)}")

            # 删除完全为空的行和列
            df = df.dropna(how='all').dropna(axis=1, how='all')
            print(f"清理后数据形状: {df.shape}")

            if df.empty:
                print(f"工作表 {sheet_name} 处理后为空")
                return False, None, None, f"工作表 {sheet_name} 处理后为空"

            print(f"工作表 {sheet_name} 读取成功，年份: {year}")
            return True, df, year, "读取成功"

        except Exception as e:
            print(f"读取工作表 {sheet_name} 异常: {str(e)}")
            return False, None, None, f"读取工作表 {sheet_name} 失败: {str(e)}"
    
    def map_columns(self, df: DataFrame) -> Tuple[bool, DataFrame, str]:
        """映射列名到标准字段名"""
        try:
            print(f"开始映射列名，原始列名: {list(df.columns)}")

            # 创建列名映射
            column_mapping = {}
            unmapped_columns = []

            for i, col in enumerate(df.columns):
                col_stripped = str(col).strip()
                mapped = False

                # 首先尝试精确匹配
                if col_stripped in self.COLUMN_MAPPING:
                    column_mapping[col] = self.COLUMN_MAPPING[col_stripped]
                    mapped = True
                    print(f"精确匹配: '{col_stripped}' -> '{self.COLUMN_MAPPING[col_stripped]}'")
                else:
                    # 尝试模糊匹配
                    for key, value in self.COLUMN_MAPPING.items():
                        if key in col_stripped or col_stripped in key:
                            column_mapping[col] = value
                            mapped = True
                            print(f"模糊匹配: '{col_stripped}' -> '{value}' (通过 '{key}')")
                            break

                # 如果还没有映射，检查是否是第一列（通常是地区名称）
                if not mapped and i == 0:
                    column_mapping[col] = 'location_name'
                    mapped = True
                    print(f"第一列映射: '{col_stripped}' -> 'location_name'")

                if not mapped:
                    unmapped_columns.append(col_stripped)
                    print(f"未映射列: '{col_stripped}'")

            # 检查是否有location_name
            mapped_fields = set(column_mapping.values())
            if 'location_name' not in mapped_fields:
                return False, None, "未找到地区名称列"

            # 检查是否有必需的数值字段
            missing_fields = set(self.REQUIRED_NUMERIC_FIELDS) - mapped_fields

            if missing_fields:
                print(f"缺少必需字段: {missing_fields}")
                print(f"已映射字段: {mapped_fields}")
                return False, None, f"缺少必需字段: {', '.join(missing_fields)}"

            # 应用映射
            df_mapped = df.rename(columns=column_mapping)

            # 只保留映射的列
            keep_columns = list(column_mapping.values())
            df_mapped = df_mapped[keep_columns]

            print(f"映射成功，最终列名: {list(df_mapped.columns)}")
            return True, df_mapped, "列名映射成功"

        except Exception as e:
            print(f"列名映射异常: {str(e)}")
            return False, None, f"列名映射失败: {str(e)}"
    
    def validate_and_clean_data(self, df: DataFrame) -> Tuple[bool, DataFrame, List[str]]:
        """验证和清理数据"""
        errors = []

        try:
            print(f"开始数据验证和清理，原始数据行数: {len(df)}")

            # 删除location_name为空的行
            if 'location_name' in df.columns:
                df = df.dropna(subset=['location_name'])
                print(f"删除location_name为空的行后，剩余行数: {len(df)}")

            # 过滤掉无效的数据行
            if 'location_name' in df.columns:
                # 定义无效数据的模式和关键词
                invalid_patterns = [
                    r'^\d{4}年.*省$',  # 匹配"2022年湖北省"这样的格式
                    r'^未命名.*',      # 匹配"未命名数据"、"未知地区"等
                    r'^Unnamed.*',     # 匹配"Unnamed: 0"等
                    r'^省份$',         # 匹配单独的"省份"
                    r'^地区$',         # 匹配单独的"地区"
                    r'^城市$',         # 匹配单独的"城市"
                    r'^数据名称$',     # 匹配单独的"数据名称"
                    r'^位置名称$',     # 匹配单独的"位置名称"
                ]

                # 定义无效关键词（不区分大小写）
                invalid_keywords = [
                    '未命名', '未知', 'unnamed', 'unknown', '数据名称', '位置名称',
                    '省份', '地区', '城市', '测试', 'test'
                ]

                # 创建过滤条件
                valid_mask = pd.Series([True] * len(df))

                for i, location_name in enumerate(df['location_name']):
                    if pd.notna(location_name):
                        location_str = str(location_name).strip()
                        location_lower = location_str.lower()

                        # 检查是否匹配任何无效模式
                        is_invalid = False
                        for pattern in invalid_patterns:
                            if re.match(pattern, location_str):
                                valid_mask.iloc[i] = False
                                print(f"过滤掉无效行(模式匹配): {location_str}")
                                is_invalid = True
                                break

                        # 如果没有被模式匹配过滤，检查关键词
                        if not is_invalid:
                            for keyword in invalid_keywords:
                                if keyword in location_lower:
                                    valid_mask.iloc[i] = False
                                    print(f"过滤掉无效行(关键词匹配): {location_str}")
                                    is_invalid = True
                                    break

                        # 额外检查：如果location_name包含年份信息，也过滤掉
                        if not is_invalid and re.search(r'\d{4}年', location_str):
                            valid_mask.iloc[i] = False
                            print(f"过滤掉包含年份的行: {location_str}")

                # 应用过滤
                df = df[valid_mask].reset_index(drop=True)
                print(f"过滤无效数据后，剩余行数: {len(df)}")

            # 进一步验证：确保至少有一个数值字段不为空
            if len(df) > 0:
                numeric_fields_available = [field for field in self.REQUIRED_NUMERIC_FIELDS if field in df.columns]
                if numeric_fields_available:
                    # 检查每行是否至少有一个数值字段有有效数据
                    valid_rows = []
                    for idx, row in df.iterrows():
                        has_valid_numeric = False
                        for field in numeric_fields_available:
                            try:
                                val = row[field]
                                if pd.notna(val) and str(val).strip() not in ['', '-', 'nan', 'NaN']:
                                    # 尝试转换为数值
                                    numeric_val = pd.to_numeric(str(val).replace('_', '.'), errors='coerce')
                                    if pd.notna(numeric_val):
                                        has_valid_numeric = True
                                        break
                            except:
                                continue

                        if has_valid_numeric:
                            valid_rows.append(idx)
                        else:
                            print(f"过滤掉没有有效数值数据的行: {row.get('location_name', 'Unknown')}")

                    # 只保留有有效数值数据的行
                    if valid_rows:
                        df = df.loc[valid_rows].reset_index(drop=True)
                        print(f"过滤无数值数据行后，剩余行数: {len(df)}")

            # 清理数值字段
            for field in self.REQUIRED_NUMERIC_FIELDS:
                if field in df.columns:
                    # 处理特殊值（如"658_0"）
                    df[field] = df[field].astype(str).str.replace('_', '.', regex=False)

                    # 转换为数值
                    df[field] = pd.to_numeric(df[field], errors='coerce')

                    # 填充NaN值为0
                    df[field] = df[field].fillna(0.0)

            # 检查是否有有效数据
            if df.empty:
                errors.append("处理后没有有效数据")
                return False, None, errors

            print(f"数据验证和清理完成，最终有效行数: {len(df)}")
            return True, df, errors

        except Exception as e:
            errors.append(f"数据验证失败: {str(e)}")
            print(f"数据验证异常: {str(e)}")
            return False, None, errors

    def convert_to_dict_list(self, df: DataFrame, year: int, batch_id: str = None) -> List[Dict]:
        """将DataFrame转换为字典列表"""
        data_list = []

        if not batch_id:
            batch_id = f"multi_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        for _, row in df.iterrows():
            data_item = {
                'data_source': 'file_import',
                'import_batch_id': batch_id,
                'assessment_year': year  # 添加年份信息
            }

            # 转换数据
            for col in df.columns:
                value = row[col]

                if col in self.REQUIRED_NUMERIC_FIELDS:
                    # 数值字段
                    data_item[col] = float(value) if pd.notna(value) else 0.0
                else:
                    # 文本字段
                    data_item[col] = str(value) if pd.notna(value) else None

                    # 为location_name生成data_name
                    if col == 'location_name' and data_item[col]:
                        data_item['data_name'] = f"{data_item[col]}{year}年数据"

            data_list.append(data_item)

        return data_list

    def process_multi_sheet_file(self, file_path: str, province_name: str = None) -> Tuple[bool, Dict, str]:
        """
        处理多工作表Excel文件的完整流程

        Args:
            file_path: 文件路径
            province_name: 省份名称（用于项目命名）

        Returns:
            (是否成功, 结果数据, 错误信息)
        """
        # 验证文件
        is_valid, error_msg = self.validate_file(file_path)
        if not is_valid:
            return False, {}, error_msg

        try:
            # 使用上下文管理器确保Excel文件正确关闭
            with pd.ExcelFile(file_path) as excel_file:
                sheet_names = excel_file.sheet_names

                results = {
                    'total_sheets': len(sheet_names),
                    'processed_sheets': 0,
                    'failed_sheets': 0,
                    'years_data': {},  # {year: data_list}
                    'errors': [],
                    'summary': {}
                }

                batch_id = f"multi_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                print(f"发现 {len(sheet_names)} 个工作表: {sheet_names}")

                # 处理每个工作表
                for sheet_name in sheet_names:
                    print(f"\n开始处理工作表: {sheet_name}")
                    try:
                        # 读取工作表数据
                        success, df, year, message = self.read_sheet_data(excel_file, sheet_name)
                        print(f"工作表 {sheet_name} 读取结果: success={success}, year={year}, message={message}")

                        if not success:
                            results['errors'].append(f"工作表 {sheet_name}: {message}")
                            results['failed_sheets'] += 1
                            print(f"工作表 {sheet_name} 读取失败，跳过")
                            continue

                        # 映射列名
                        success, df_mapped, message = self.map_columns(df)
                        print(f"工作表 {sheet_name} 列名映射结果: success={success}, message={message}")

                        if not success:
                            results['errors'].append(f"工作表 {sheet_name}: {message}")
                            results['failed_sheets'] += 1
                            print(f"工作表 {sheet_name} 列名映射失败，跳过")
                            continue

                        # 验证和清理数据
                        success, df_clean, errors = self.validate_and_clean_data(df_mapped)
                        print(f"工作表 {sheet_name} 数据验证结果: success={success}, errors={errors}")

                        if not success:
                            results['errors'].extend([f"工作表 {sheet_name}: {err}" for err in errors])
                            results['failed_sheets'] += 1
                            print(f"工作表 {sheet_name} 数据验证失败，跳过")
                            continue

                        # 转换为字典列表
                        data_list = self.convert_to_dict_list(df_clean, year, batch_id)
                        print(f"工作表 {sheet_name} 转换结果: {len(data_list)} 条记录")

                        if data_list:
                            results['years_data'][year] = data_list
                            results['processed_sheets'] += 1
                            print(f"工作表 {sheet_name} 处理成功，年份: {year}")
                        else:
                            results['errors'].append(f"工作表 {sheet_name}: 没有有效数据")
                            results['failed_sheets'] += 1
                            print(f"工作表 {sheet_name} 没有有效数据")

                    except Exception as e:
                        error_msg = f"处理工作表 {sheet_name} 失败: {str(e)}"
                        results['errors'].append(error_msg)
                        results['failed_sheets'] += 1
                        print(f"工作表 {sheet_name} 处理异常: {str(e)}")
                        import traceback
                        traceback.print_exc()

                # 生成汇总信息
                total_records = sum(len(data) for data in results['years_data'].values())
                results['summary'] = {
                    'total_records': total_records,
                    'years_processed': list(results['years_data'].keys()),
                    'batch_id': batch_id,
                    'province_name': province_name or '未指定'
                }

                if results['processed_sheets'] == 0:
                    return False, results, "没有成功处理任何工作表"

                return True, results, f"成功处理 {results['processed_sheets']} 个工作表，共 {total_records} 条记录"

        except Exception as e:
            return False, {}, f"处理文件失败: {str(e)}"
