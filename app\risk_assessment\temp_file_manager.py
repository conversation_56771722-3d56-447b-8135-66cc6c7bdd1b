"""
临时文件管理器
用于管理风险评估过程中的临时文件
"""

import os
import tempfile
import shutil
from datetime import datetime, timedelta
from pathlib import Path


class TempFileManager:
    """临时文件管理器"""
    
    def __init__(self, base_dir=None):
        """
        初始化临时文件管理器
        
        Args:
            base_dir: 临时文件基础目录，如果为None则使用系统临时目录
        """
        if base_dir is None:
            self.base_dir = Path(tempfile.gettempdir()) / "gis_flood_system"
        else:
            self.base_dir = Path(base_dir)
        
        # 确保目录存在
        self.base_dir.mkdir(parents=True, exist_ok=True)
    
    def create_temp_file(self, suffix=None, prefix=None, content=None):
        """
        创建临时文件
        
        Args:
            suffix: 文件后缀
            prefix: 文件前缀
            content: 文件内容
            
        Returns:
            临时文件路径
        """
        fd, temp_path = tempfile.mkstemp(
            suffix=suffix,
            prefix=prefix,
            dir=str(self.base_dir)
        )
        
        try:
            if content is not None:
                if isinstance(content, str):
                    content = content.encode('utf-8')
                os.write(fd, content)
        finally:
            os.close(fd)
        
        return temp_path
    
    def create_temp_dir(self, prefix=None):
        """
        创建临时目录
        
        Args:
            prefix: 目录前缀
            
        Returns:
            临时目录路径
        """
        return tempfile.mkdtemp(prefix=prefix, dir=str(self.base_dir))
    
    def cleanup_old_files(self, max_age_hours=24):
        """
        清理旧的临时文件
        
        Args:
            max_age_hours: 文件最大保留时间（小时）
        """
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        for item in self.base_dir.iterdir():
            try:
                # 获取文件修改时间
                mtime = datetime.fromtimestamp(item.stat().st_mtime)
                
                if mtime < cutoff_time:
                    if item.is_file():
                        item.unlink()
                    elif item.is_dir():
                        shutil.rmtree(item)
                    print(f"已清理临时文件: {item}")
            except Exception as e:
                print(f"清理文件失败 {item}: {e}")
    
    def get_temp_path(self, filename):
        """
        获取临时文件路径
        
        Args:
            filename: 文件名
            
        Returns:
            临时文件完整路径
        """
        return str(self.base_dir / filename)
    
    def remove_file(self, file_path):
        """
        删除指定文件
        
        Args:
            file_path: 文件路径
        """
        try:
            path = Path(file_path)
            if path.exists():
                if path.is_file():
                    path.unlink()
                elif path.is_dir():
                    shutil.rmtree(path)
                return True
        except Exception as e:
            print(f"删除文件失败 {file_path}: {e}")
        return False


# 全局临时文件管理器实例
temp_file_manager = TempFileManager()
