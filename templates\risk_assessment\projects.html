{% extends "base.html" %}

{% block title %}城市管理 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-map text-primary me-2"></i>省份管理</h2>
                    <p class="text-muted mb-0">管理省份内涝风险评估，预测不同省份内各城市的风险等级</p>
                </div>
                <div>
                    <a href="{{ url_for('risk_assessment.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>返回主页
                    </a>
                    <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#multiYearUploadModal">
                        <i class="fas fa-file-excel me-1"></i>导入多年份数据
                    </button>
                    <a href="{{ url_for('risk_assessment.create_project') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>新建省份评估
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目列表 -->
    <div class="row">
        <div class="col-12">
            {% if projects %}
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-list me-2"></i>城市评估列表 ({{ projects|length }})</h5>
                            <div id="batchOperations" style="display: none;">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-success btn-sm" onclick="batchCalculateRisks()">
                                        <i class="fas fa-calculator me-1"></i>批量计算风险
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="batchDeleteProjects()">
                                        <i class="fas fa-trash me-1"></i>批量删除
                                    </button>
                                </div>
                                <span class="ms-2 text-muted">已选择 <span id="selectedCount">0</span> 项</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                                <label class="form-check-label" for="selectAll"></label>
                                            </div>
                                        </th>
                                        <th>城市名称</th>
                                        <th>描述</th>
                                        <th>评估区域</th>
                                        <th>年份</th>
                                        <th>状态</th>
                                        <th>数据量</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for project in projects %}
                                    <tr data-project-id="{{ project.id }}" data-project-name="{{ project.project_name }}" data-project-location="{{ project.location or '' }}">
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input project-checkbox" type="checkbox"
                                                       value="{{ project.id }}" onchange="updateBatchOperations()">
                                            </div>
                                        </td>
                                        <td>
                                            <strong>{{ project.project_name }}</strong>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ project.description or '暂无描述' }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ project.location or '未指定' }}</span>
                                        </td>
                                        <td>
                                            {% if project.assessment_year %}
                                                <span class="badge bg-primary">{{ project.assessment_year }}年</span>
                                            {% else %}
                                                <span class="text-muted">未指定</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if project.status == 'completed' %}
                                                <span class="badge bg-success">已完成</span>
                                            {% elif project.status == 'calculating' %}
                                                <span class="badge bg-warning">计算中</span>
                                            {% elif project.status == 'draft' %}
                                                <span class="badge bg-secondary">草稿</span>
                                            {% else %}
                                                <span class="badge bg-light text-dark">{{ project.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="text-primary fw-bold">{{ project.assessment_data|length }}</span> 条
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ project.created_at.strftime('%Y-%m-%d %H:%M') if project.created_at else '未知' }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{{ url_for('risk_assessment.project_detail', project_id=project.id) }}"
                                                   class="btn btn-outline-primary" title="查看详情">
                                                    <i class="fas fa-eye"></i>
                                                </a>

                                                <button type="button" class="btn btn-outline-danger"
                                                        onclick="deleteProject({{ project.id }}, '{{ project.project_name }}')"
                                                        title="删除项目">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无风险评估项目</h5>
                        <p class="text-muted mb-4">您还没有创建任何风险评估项目，点击下方按钮开始创建。</p>
                        <div>
                            <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#multiYearUploadModal">
                                <i class="fas fa-file-excel me-1"></i>导入多年份数据
                            </button>
                            <a href="{{ url_for('risk_assessment.create_project') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>创建第一个项目
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteProjectModal" tabindex="-1" aria-labelledby="deleteProjectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle text-warning me-2"></i>确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除项目 "<span id="deleteProjectName" class="fw-bold"></span>" 吗？</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>警告：</strong>删除项目将同时删除所有相关的评估数据和计算结果，此操作不可恢复！
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 多年份数据上传模态框 -->
<div class="modal fade" id="multiYearUploadModal" tabindex="-1" aria-labelledby="multiYearUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="multiYearUploadModalLabel">
                    <i class="fas fa-file-excel text-success me-2"></i>导入多年份数据
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>文件格式要求：</strong>
                    <br>支持CSV、Excel格式文件
                    <br><br>
                    <strong>文件内容包含以下字段（可使用中文或英文列名）：</strong>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <ul class="list-unstyled small">
                                <li><strong>数据名称</strong> (data_name)</li>
                                <li><strong>位置名称</strong> (location_name)</li>
                                <li><strong>全年降水量</strong> (annual_precipitation)</li>
                                <li><strong>排水管网密度</strong> (drainage_network_density)</li>
                                <li><strong>平均高程值</strong> (elevation)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled small">
                                <li><strong>人口密度</strong> (population_density)</li>
                                <li><strong>GDP密度</strong> (gdp_per_area)</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <form id="multiYearUploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="provinceNameInput" class="form-label">省份名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="provinceNameInput" name="province_name" 
                               placeholder="请输入省份名称（如：河南省）" required>
                        <div class="form-text">请输入完整的省份名称，如"河南省"、"山东省"等</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="multiYearFileInput" class="form-label">选择Excel文件 <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="multiYearFileInput" name="file" 
                               accept=".xlsx,.xls" required>
                        <div class="form-text">请选择包含多个年份数据的Excel文件</div>
                    </div>
                    
                    <div id="uploadProgress" class="mb-3" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted mt-1">正在处理文件，请稍候...</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="uploadMultiYearBtn">
                    <i class="fas fa-upload me-1"></i>开始导入
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let deleteProjectId = null;

function deleteProject(projectId, projectName) {
    deleteProjectId = projectId;
    document.getElementById('deleteProjectName').textContent = projectName;

    const modal = new bootstrap.Modal(document.getElementById('deleteProjectModal'));
    modal.show();
}

document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (deleteProjectId) {
        // 显示加载状态
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';
        this.disabled = true;

        fetch(`/risk-assessment/api/project/${deleteProjectId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('deleteProjectModal'));
                modal.hide();

                // 显示成功消息
                alert(data.message || '项目删除成功');

                // 刷新页面
                window.location.reload();
            } else {
                alert('删除失败: ' + (data.error || '未知错误'));
                // 恢复按钮状态
                this.innerHTML = '<i class="fas fa-trash me-1"></i>确认删除';
                this.disabled = false;
            }
        })
        .catch(error => {
            console.error('删除项目时发生错误:', error);
            alert('删除时发生错误: ' + error.message);
            // 恢复按钮状态
            this.innerHTML = '<i class="fas fa-trash me-1"></i>确认删除';
            this.disabled = false;
        });
    }
});

// 多年份文件上传功能
document.getElementById('uploadMultiYearBtn').addEventListener('click', function() {
    const form = document.getElementById('multiYearUploadForm');
    const formData = new FormData(form);
    const progressDiv = document.getElementById('uploadProgress');
    const uploadBtn = document.getElementById('uploadMultiYearBtn');

    // 验证表单
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // 显示进度条
    progressDiv.style.display = 'block';
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';

    // 发送请求
    fetch('/risk-assessment/projects/upload-multi-year', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示成功信息
            showAlert('success', `成功导入 ${data.import_summary.total_records} 条记录，涉及 ${data.import_summary.years_processed.length} 个年份`);

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('multiYearUploadModal'));
            modal.hide();

            // 刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.error || '导入失败');
            if (data.details && data.details.length > 0) {
                console.error('详细错误信息:', data.details);
            }
        }
    })
    .catch(error => {
        console.error('上传错误:', error);
        showAlert('danger', '文件上传失败，请检查网络连接');
    })
    .finally(() => {
        // 隐藏进度条，恢复按钮
        progressDiv.style.display = 'none';
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="fas fa-upload me-1"></i>开始导入';
    });
});

// 显示提示信息
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 插入到页面顶部
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // 5秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// 批量操作功能
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const projectCheckboxes = document.querySelectorAll('.project-checkbox');

    projectCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateBatchOperations();
}

function updateBatchOperations() {
    const selectedCheckboxes = document.querySelectorAll('.project-checkbox:checked');
    const batchOperations = document.getElementById('batchOperations');
    const selectedCount = document.getElementById('selectedCount');
    const selectAllCheckbox = document.getElementById('selectAll');

    // 更新选中数量
    selectedCount.textContent = selectedCheckboxes.length;

    // 显示/隐藏批量操作按钮
    if (selectedCheckboxes.length > 0) {
        batchOperations.style.display = 'block';
    } else {
        batchOperations.style.display = 'none';
    }

    // 更新全选复选框状态
    const allCheckboxes = document.querySelectorAll('.project-checkbox');
    if (selectedCheckboxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (selectedCheckboxes.length === allCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

// 批量计算风险
function batchCalculateRisks() {
    const selectedCheckboxes = document.querySelectorAll('.project-checkbox:checked');
    const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (selectedIds.length === 0) {
        showAlert('warning', '请先选择要计算风险的项目');
        return;
    }

    if (!confirm(`确定要批量计算 ${selectedIds.length} 个项目的风险等级吗？这将覆盖之前的计算结果。`)) {
        return;
    }

    // 显示进度模态框
    showBatchProgressModal('批量计算风险', selectedIds.length);

    // 逐个计算风险
    batchProcessProjects(selectedIds, 'calculate', (projectId, index, total) => {
        return fetch(`/risk-assessment/api/project/${projectId}/calculate`, {
            method: 'POST'
        });
    });
}

// 批量查看地图
function batchViewMaps() {
    const selectedCheckboxes = document.querySelectorAll('.project-checkbox:checked');

    if (selectedCheckboxes.length === 0) {
        showAlert('warning', '请先选择要查看地图的项目');
        return;
    }

    // 获取选中项目的省份信息
    const selectedProjects = Array.from(selectedCheckboxes).map(cb => {
        const row = cb.closest('tr');
        return {
            id: cb.value,
            name: row.dataset.projectName,
            location: row.dataset.projectLocation
        };
    });

    // 检查是否有省份信息
    const projectsWithLocation = selectedProjects.filter(p => p.location && p.location.trim() !== '');

    if (projectsWithLocation.length === 0) {
        showAlert('warning', '选中的项目没有指定省份信息，无法查看地图');
        return;
    }

    // 按省份分组
    const provinceGroups = {};
    projectsWithLocation.forEach(project => {
        const province = project.location;
        if (!provinceGroups[province]) {
            provinceGroups[province] = [];
        }
        provinceGroups[province].push(project);
    });

    // 为每个省份打开一个地图页面
    const provinces = Object.keys(provinceGroups);

    if (provinces.length > 5) {
        if (!confirm(`将要打开 ${provinces.length} 个地图页面，确定继续吗？`)) {
            return;
        }
    }

    provinces.forEach(province => {
        const mapUrl = `/visualization/map?province=${encodeURIComponent(province)}`;
        window.open(mapUrl, `_blank_${province}`);
    });

    showAlert('success', `已为 ${provinces.length} 个省份打开地图页面`);
}

// 批量删除项目
function batchDeleteProjects() {
    const selectedCheckboxes = document.querySelectorAll('.project-checkbox:checked');
    const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (selectedIds.length === 0) {
        showAlert('warning', '请先选择要删除的项目');
        return;
    }

    // 获取选中项目的名称
    const selectedNames = Array.from(selectedCheckboxes).map(cb => {
        const row = cb.closest('tr');
        return row.dataset.projectName;
    });

    const confirmMessage = `确定要删除以下 ${selectedIds.length} 个项目吗？\n\n${selectedNames.join('\n')}\n\n此操作不可撤销！`;

    if (!confirm(confirmMessage)) {
        return;
    }

    // 显示进度模态框
    showBatchProgressModal('批量删除项目', selectedIds.length);

    // 逐个删除项目
    batchProcessProjects(selectedIds, 'delete', (projectId, index, total) => {
        return fetch(`/risk-assessment/api/project/${projectId}`, {
            method: 'DELETE'
        });
    });
}

// 批量处理项目的通用函数
async function batchProcessProjects(projectIds, operation, processFunction) {
    const total = projectIds.length;
    let completed = 0;
    let successful = 0;
    let failed = 0;
    const errors = [];

    for (let i = 0; i < projectIds.length; i++) {
        const projectId = projectIds[i];

        try {
            updateBatchProgress(i + 1, total, `正在处理第 ${i + 1} 个项目...`);

            const response = await processFunction(projectId, i, total);
            const result = await response.json();

            if (result.success) {
                successful++;
            } else {
                failed++;
                errors.push(`项目 ${projectId}: ${result.error}`);
            }
        } catch (error) {
            failed++;
            errors.push(`项目 ${projectId}: ${error.message}`);
        }

        completed++;
        updateBatchProgress(completed, total, `已处理 ${completed}/${total} 个项目`);

        // 添加小延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 200));
    }

    // 处理完成
    setTimeout(() => {
        hideBatchProgressModal();

        // 显示结果
        let message = `批量${operation === 'calculate' ? '计算' : '删除'}完成！\n`;
        message += `成功: ${successful} 个，失败: ${failed} 个`;

        if (errors.length > 0) {
            message += `\n\n错误详情:\n${errors.slice(0, 5).join('\n')}`;
            if (errors.length > 5) {
                message += `\n... 还有 ${errors.length - 5} 个错误`;
            }
        }

        // 如果是计算操作且有成功的项目，提示可以查看地图
        if (operation === 'calculate' && successful > 0) {
            message += `\n\n💡 提示：您现在可以在地图可视化页面查看风险区划图效果！`;
        }

        if (failed === 0) {
            showAlert('success', message);
        } else if (successful === 0) {
            showAlert('danger', message);
        } else {
            showAlert('warning', message);
        }

        // 刷新页面
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    }, 500);
}

// 显示批量进度模态框
function showBatchProgressModal(title, total) {
    const modalHtml = `
        <div class="modal fade" id="batchProgressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-cogs me-2"></i>${title}
                        </h5>
                    </div>
                    <div class="modal-body">
                        <div class="text-center mb-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">处理中...</span>
                            </div>
                        </div>
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 id="batchProgressBar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div class="text-center">
                            <div id="batchProgressText">准备开始处理...</div>
                            <small class="text-muted" id="batchProgressDetail">总共 ${total} 个项目</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('batchProgressModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchProgressModal'));
    modal.show();
}

// 更新批量进度
function updateBatchProgress(current, total, message) {
    const progressBar = document.getElementById('batchProgressBar');
    const progressText = document.getElementById('batchProgressText');
    const progressDetail = document.getElementById('batchProgressDetail');

    if (progressBar && progressText && progressDetail) {
        const percentage = Math.round((current / total) * 100);
        progressBar.style.width = percentage + '%';
        progressBar.setAttribute('aria-valuenow', percentage);
        progressText.textContent = message;
        progressDetail.textContent = `进度: ${current}/${total} (${percentage}%)`;
    }
}

// 隐藏批量进度模态框
function hideBatchProgressModal() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('batchProgressModal'));
    if (modal) {
        modal.hide();
    }

    // 延迟移除DOM元素
    setTimeout(() => {
        const modalElement = document.getElementById('batchProgressModal');
        if (modalElement) {
            modalElement.remove();
        }
    }, 500);
}
</script>

<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}
