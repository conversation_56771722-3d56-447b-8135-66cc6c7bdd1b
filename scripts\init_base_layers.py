#!/usr/bin/env python3
"""
基础图层初始化脚本
创建系统默认的基础图层，包括行政区划、土地利用等
"""
import os
import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app import create_app
from app.models import GISLayer, db
from app.services.layer_manager import LayerManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_base_layers_structure():
    """创建基础图层目录结构"""
    app = create_app()
    
    with app.app_context():
        try:
            # 创建基础图层目录
            layer_manager = LayerManager()
            base_layers_path = layer_manager.base_layers_path
            
            logger.info(f"基础图层目录: {base_layers_path}")
            
            # 定义基础图层信息
            base_layer_configs = [
                {
                    'name': 'china_provinces',
                    'display_name': '中国省级行政区划',
                    'description': '中国省级行政区划边界数据',
                    'layer_type': 'vector',
                    'data_category': 'administrative',
                    'coordinate_system': 'EPSG:4326',
                    'geometry_type': 'Polygon',
                    'is_visible': True,
                    'opacity': 0.6,
                    'z_index': 10
                },
                {
                    'name': 'china_cities',
                    'display_name': '中国地级市行政区划',
                    'description': '中国地级市行政区划边界数据',
                    'layer_type': 'vector',
                    'data_category': 'administrative',
                    'coordinate_system': 'EPSG:4326',
                    'geometry_type': 'Polygon',
                    'is_visible': False,
                    'opacity': 0.7,
                    'z_index': 11
                },
                {
                    'name': 'landuse_classification',
                    'display_name': '土地利用分类',
                    'description': '全国土地利用分类数据',
                    'layer_type': 'raster',
                    'data_category': 'landuse',
                    'coordinate_system': 'EPSG:4326',
                    'is_visible': False,
                    'opacity': 0.8,
                    'z_index': 5
                },
            ]
            
            # 创建基础图层记录
            created_layers = []
            for config in base_layer_configs:
                # 检查是否已存在
                existing_layer = GISLayer.query.filter_by(name=config['name']).first()
                if existing_layer:
                    logger.info(f"基础图层 {config['name']} 已存在，跳过创建")
                    continue
                
                # 创建图层目录
                layer_dir = os.path.join(base_layers_path, config['data_category'])
                os.makedirs(layer_dir, exist_ok=True)
                
                # 创建文件路径（实际部署时需要放置真实文件）
                if config['layer_type'] == 'vector':
                    file_path = os.path.join(layer_dir, f"{config['name']}.shp")
                else:
                    file_path = os.path.join(layer_dir, f"{config['name']}.tif")
                
                # 创建数据库记录
                layer = GISLayer(
                    name=config['name'],
                    display_name=config['display_name'],
                    description=config['description'],
                    layer_type=config['layer_type'],
                    data_category=config['data_category'],
                    file_path=file_path,
                    coordinate_system=config['coordinate_system'],
                    geometry_type=config.get('geometry_type'),
                    is_visible=config['is_visible'],
                    opacity=config['opacity'],
                    z_index=config['z_index'],
                    processing_status='uploaded',
                    created_by=1  # 假设管理员用户ID为1
                )
                
                # 设置默认样式
                if config['layer_type'] == 'vector':
                    if config['data_category'] == 'administrative':
                        default_style = {
                            'fillColor': '#3388ff',
                            'fillOpacity': 0.2,
                            'color': '#3388ff',
                            'weight': 2,
                            'opacity': 0.8
                        }
                    elif config['data_category'] == 'risk_zones':
                        default_style = {
                            'fillColor': '#ff6b6b',
                            'fillOpacity': 0.4,
                            'color': '#ff4757',
                            'weight': 2,
                            'opacity': 0.9
                        }
                    else:
                        default_style = {
                            'fillColor': '#2ecc71',
                            'fillOpacity': 0.3,
                            'color': '#27ae60',
                            'weight': 1,
                            'opacity': 0.7
                        }
                else:
                    # 栅格图层样式
                    default_style = {
                        'colormap': 'viridis',
                        'opacity': config['opacity'],
                        'nodata_transparent': True
                    }
                
                layer.default_style = json.dumps(default_style)
                
                db.session.add(layer)
                created_layers.append(config['name'])
            
            # 提交数据库更改
            db.session.commit()
            
            logger.info(f"成功创建 {len(created_layers)} 个基础图层:")
            for layer_name in created_layers:
                logger.info(f"  - {layer_name}")
            

            return True
            
        except Exception as e:
            logger.error(f"创建基础图层失败: {e}")
            db.session.rollback()
            return False


if __name__ == '__main__':
    logger.info("开始初始化基础图层...")
    success = create_base_layers_structure()
    
    if success:
        logger.info("✅ 基础图层初始化完成")
        print("\n基础图层初始化成功！")
        print("请将实际的基础图层文件放置在 data/base_layers/ 目录中")
    else:
        logger.error("❌ 基础图层初始化失败")
        print("\n基础图层初始化失败，请检查日志")
        sys.exit(1)
