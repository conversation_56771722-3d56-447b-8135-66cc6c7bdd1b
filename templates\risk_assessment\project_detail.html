{% extends "base.html" %}

{% block title %}{{ project.project_name }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 项目标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-city text-primary me-2"></i>{{ project.project_name }}</h2>
                    <p class="text-muted mb-0">
                        {% if project.location %}
                            <i class="fas fa-map-marker-alt me-1"></i>{{ project.location }}
                        {% endif %}
                        {% if project.description %}
                            | {{ project.description }}
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('risk_assessment.projects') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                    <div class="btn-group" role="group">
                        <button class="btn btn-success" onclick="showDataInputModal()">
                            <i class="fas fa-plus me-1"></i>添加数据
                        </button>
                        <button class="btn btn-info" onclick="showFileImportModal()">
                            <i class="fas fa-file-import me-1"></i>导入文件
                        </button>
                        <button class="btn btn-warning" onclick="showWeightConfigModal()">
                            <i class="fas fa-cog me-1"></i>权重配置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目状态卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ assessment_data|length }}</h4>
                            <p class="mb-0">评估数据</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-database fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ calculation_results|length }}</h4>
                            <p class="mb-0">计算结果</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calculator fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">
                                {% if project.status == 'draft' %}草稿
                                {% elif project.status == 'calculating' %}计算中
                                {% elif project.status == 'completed' %}已完成
                                {% elif project.status == 'archived' %}已归档
                                {% else %}{{ project.status }}
                                {% endif %}
                            </h4>
                            <p class="mb-0">项目状态</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-flag fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="riskLevelCount">-</h4>
                            <p class="mb-0">风险等级</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 数据列表 -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-table me-2"></i>评估数据</h5>
                    <div>
                        {% if assessment_data %}
                            <button class="btn btn-primary btn-sm me-2" onclick="calculateRisks()">
                                <i class="fas fa-play me-1"></i>计算风险
                            </button>
                        {% endif %}
                        {% if calculation_results %}
                            <a href="{{ url_for('risk_assessment.project_results', project_id=project.id) }}"
                               class="btn btn-success btn-sm">
                                <i class="fas fa-chart-line me-1"></i>查看结果
                            </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="dataTableContainer">
                        {% if assessment_data %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>数据名称</th>
                                            <th>位置</th>
                                            <th>全年降水量</th>
                                            <th>排水管网密度</th>
                                            <th>平均高程值</th>
                                            <th>人口密度</th>
                                            <th>GDP密度</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for data in assessment_data %}
                                        <tr>
                                            <td><strong>{{ data.data_name or '数据' + loop.index|string }}</strong></td>
                                            <td>{{ data.location_name or '-' }}</td>
                                            <td>{{ "%.2f"|format(data.annual_precipitation) if data.annual_precipitation else '-' }}</td>
                                            <td>{{ "%.2f"|format(data.drainage_network_density) if data.drainage_network_density else '-' }}</td>
                                            <td>{{ "%.2f"|format(data.elevation) if data.elevation else '-' }}</td>
                                            <td>{{ "%.2f"|format(data.population_density) if data.population_density else '-' }}</td>
                                            <td>{{ "%.2f"|format(data.gdp_per_area) if data.gdp_per_area else '-' }}</td>
                                            <td>
                                                <button class="btn btn-outline-danger btn-sm"
                                                        onclick="deleteData({{ data.id }})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-database fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">暂无评估数据</h5>
                                <p class="text-muted">请添加评估数据或导入数据文件</p>
                                <button class="btn btn-primary me-2" onclick="showDataInputModal()">
                                    <i class="fas fa-plus me-1"></i>添加数据
                                </button>
                                <button class="btn btn-info" onclick="showFileImportModal()">
                                    <i class="fas fa-file-import me-1"></i>导入文件
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <!-- 权重配置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-weight-hanging me-2"></i>当前权重配置</h6>
                </div>
                <div class="card-body">
                    {% if weight_config %}
                        <div class="small">
                            <div class="mb-2">
                                <strong>{{ weight_config.config_name }}</strong>
                                {% if weight_config.is_default %}
                                    <span class="badge bg-primary ms-1">默认</span>
                                {% endif %}
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-1">降雨权重: {{ "%.1f"|format(weight_config.rainfall_weight * 100) }}%</div>
                                    <div class="mb-1">排水权重: {{ "%.1f"|format(weight_config.drainage_weight * 100) }}%</div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-1">地形权重: {{ "%.1f"|format(weight_config.elevation_weight * 100) }}%</div>
                                    <div class="mb-1" id="impactWeightDisplay">影响程度: 加载中...</div>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <p class="text-muted mb-0">未配置权重</p>
                    {% endif %}
                </div>
            </div>

            <!-- 计算结果统计 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>风险等级分布</h6>
                </div>
                <div class="card-body">
                    <div id="riskDistributionContainer">
                        <div class="text-center text-muted">
                            <i class="fas fa-chart-pie fa-2x mb-2"></i>
                            <p class="mb-0">暂无计算结果</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据输入模态框 -->
<div class="modal fade" id="dataInputModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>添加评估数据</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="dataInputForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">数据名称</label>
                                <input type="text" class="form-control" name="data_name" placeholder="如：测点1">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">位置名称</label>
                                <select class="form-select" name="location_name" id="locationSelect" required>
                                    <option value="">请选择城市/区域</option>
                                </select>
                                <div class="form-text">选择 {{ project.location }} 内的具体城市或区域</div>
                            </div>
                        </div>
                    </div>
                    
                    <h6 class="mb-3"><i class="fas fa-cloud-rain text-primary me-2"></i>发生概率相关指标</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">全年降水量 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" name="annual_precipitation"
                                       step="0.01" min="0" required placeholder="单位：mm">
                                <div class="form-text">正向指标：降水量越多，风险越高</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">排水管网密度 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" name="drainage_network_density"
                                       step="0.01" min="0" required placeholder="单位：km/km²">
                                <div class="form-text">负向指标：排水密度越高，风险越低</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">平均高程值 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" name="elevation"
                                       step="0.01" min="0" required placeholder="单位：m">
                                <div class="form-text">负向指标：高程越高，风险越低</div>
                            </div>
                        </div>
                    </div>

                    <h6 class="mb-3"><i class="fas fa-users text-warning me-2"></i>影响程度相关指标</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">人口密度 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" name="population_density"
                                       step="0.01" min="0" required placeholder="单位：人/km²">
                                <div class="form-text">正向指标：人口密度越高，影响越大</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">GDP密度 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" name="gdp_per_area"
                                       step="0.01" min="0" required placeholder="单位：万元/km²">
                                <div class="form-text">正向指标：GDP密度越高，影响越大</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitDataInput()">
                    <i class="fas fa-save me-1"></i>保存数据
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 文件导入模态框 -->
<div class="modal fade" id="fileImportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-file-import me-2"></i>导入数据文件</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="fileImportForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label class="form-label">选择文件</label>
                        <input type="file" class="form-control" name="file" accept=".csv,.xlsx,.xls" required>
                        <div class="form-text">支持CSV、Excel格式文件</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>文件格式要求：</h6>
                        <p class="mb-2">文件应包含以下列（可使用中文或英文列名）：</p>
                        <ul class="small mb-2">
                            <li>数据名称 (data_name)</li>
                            <li>位置名称 (location_name)</li>
                            <li>全年降水量 (annual_precipitation)</li>
                            <li>排水管网密度 (drainage_network_density)</li>
                            <li>平均高程值 (elevation)</li>
                            <li>人口密度 (population_density)</li>
                            <li>GDP密度 (gdp_per_area)</li>
                        </ul>

                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitFileImport()">
                    <i class="fas fa-upload me-1"></i>导入文件
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const projectId = {{ project.id }};

// 显示数据输入模态框
function showDataInputModal() {
    const modal = new bootstrap.Modal(document.getElementById('dataInputModal'));
    modal.show();
}

// 显示文件导入模态框
function showFileImportModal() {
    const modal = new bootstrap.Modal(document.getElementById('fileImportModal'));
    modal.show();
}

// 显示权重配置模态框
function showWeightConfigModal() {
    // 跳转到权重配置页面
    window.open('/risk-assessment/weight-config', '_blank');
}

// 提交数据输入
function submitDataInput() {
    const form = document.getElementById('dataInputForm');
    const formData = new FormData(form);

    // 转换为JSON
    const data = {};
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            if (key === 'data_name' || key === 'location_name') {
                data[key] = value;
            } else {
                data[key] = parseFloat(value);
            }
        }
    }

    // 验证必需字段
    const requiredFields = ['annual_precipitation', 'drainage_network_density', 'elevation',
                           'population_density', 'gdp_per_area'];

    for (let field of requiredFields) {
        if (!(field in data) || isNaN(data[field])) {
            alert(`请填写${getFieldLabel(field)}`);
            return;
        }
    }

    // 发送请求
    fetch(`/risk-assessment/api/project/${projectId}/data`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('数据添加成功');
            location.reload();
        } else {
            alert('添加失败: ' + result.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('添加数据时发生错误');
    });
}

// 提交文件导入
function submitFileImport() {
    const form = document.getElementById('fileImportForm');
    const formData = new FormData(form);
    const fileInput = form.querySelector('input[type="file"]');
    const file = fileInput.files[0];

    if (!file) {
        alert('请选择文件');
        return;
    }

    // 验证文件类型
    const fileName = file.name.toLowerCase();
    const allowedExtensions = ['.csv', '.xlsx', '.xls'];
    const fileExtension = fileName.substring(fileName.lastIndexOf('.'));

    if (!allowedExtensions.includes(fileExtension)) {
        alert(`不支持的文件格式。请选择以下格式的文件：${allowedExtensions.join(', ')}`);
        return;
    }

    console.log('上传文件信息:', {
        name: file.name,
        size: file.size,
        type: file.type,
        extension: fileExtension
    });

    // 显示加载状态
    const submitBtn = event.target;
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>导入中...';
    submitBtn.disabled = true;

    fetch(`/risk-assessment/api/project/${projectId}/import`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(`导入成功，共导入 ${result.imported_count} 条数据`);
            location.reload();
        } else {
            alert('导入失败: ' + result.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('导入文件时发生错误');
    })
    .finally(() => {
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// 删除数据
function deleteData(dataId) {
    if (!confirm('确定要删除这条数据吗？')) {
        return;
    }

    fetch(`/risk-assessment/api/project/${projectId}/data/${dataId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('删除成功');
            location.reload();
        } else {
            alert('删除失败: ' + result.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('删除数据时发生错误');
    });
}

// 计算风险
function calculateRisks() {
    if (!confirm('确定要计算风险等级吗？这将覆盖之前的计算结果。')) {
        return;
    }

    // 显示加载状态
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>计算中...';
    btn.disabled = true;

    fetch(`/risk-assessment/api/project/${projectId}/calculate`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            // 显示成功提示
            showSuccessAlert(`✅ 风险等级计算完成！共计算 ${result.results_count} 条数据的风险等级。数据已同步到GIS热力图系统，您可以在可视化页面查看热力图效果。`);

            // 延迟刷新页面，让用户看到提示
            setTimeout(() => {
                location.reload();
            }, 3000);
        } else {
            showErrorAlert('❌ 计算失败: ' + result.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorAlert('❌ 计算风险时发生网络错误，请检查网络连接后重试');
    })
    .finally(() => {
        // 恢复按钮状态
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 获取字段标签
function getFieldLabel(field) {
    const labels = {
        'annual_precipitation': '全年降水量',
        'drainage_network_density': '排水管网密度',
        'elevation': '平均高程值',
        'population_density': '人口密度',
        'gdp_per_area': 'GDP密度'
    };
    return labels[field] || field;
}

// 显示成功提示
function showSuccessAlert(message) {
    // 移除现有的提示
    $('.custom-alert').remove();

    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show custom-alert" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('body').append(alertHtml);

    // 自动消失
    setTimeout(() => {
        $('.custom-alert').fadeOut();
    }, 8000);
}

// 显示错误提示
function showErrorAlert(message) {
    // 移除现有的提示
    $('.custom-alert').remove();

    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show custom-alert" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">
            <i class="fas fa-exclamation-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('body').append(alertHtml);

    // 自动消失
    setTimeout(() => {
        $('.custom-alert').fadeOut();
    }, 5000);
}

// 省份城市配置数据
const provinceConfig = {
    // 直辖市
    '北京市': {
        cities: ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '大兴区', '昌平区', '平谷区', '怀柔区', '密云区', '延庆区']
    },
    '天津市': {
        cities: ['和平区', '河东区', '河西区', '南开区', '河北区', '红桥区', '东丽区', '西青区', '津南区', '北辰区', '武清区', '宝坻区', '滨海新区', '宁河区', '静海区', '蓟州区']
    },
    '上海市': {
        cities: ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '闵行区', '宝山区', '嘉定区', '浦东新区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区']
    },
    '重庆市': {
        cities: ['万州区', '涪陵区', '渝中区', '大渡口区', '江北区', '沙坪坝区', '九龙坡区', '南岸区', '北碚区', '綦江区', '大足区', '渝北区', '巴南区', '黔江区', '长寿区', '江津区']
    },

    // 华北地区
    '河北省': {
        cities: ['石家庄市', '唐山市', '秦皇岛市', '邯郸市', '邢台市', '保定市', '张家口市', '承德市', '沧州市', '廊坊市', '衡水市']
    },
    '山西省': {
        cities: ['太原市', '大同市', '阳泉市', '长治市', '晋城市', '朔州市', '晋中市', '运城市', '忻州市', '临汾市', '吕梁市']
    },

    // 东北地区
    '辽宁省': {
        cities: ['沈阳市', '大连市', '鞍山市', '抚顺市', '本溪市', '丹东市', '锦州市', '营口市', '阜新市', '辽阳市', '盘锦市', '铁岭市', '朝阳市', '葫芦岛市']
    },
    '吉林省': {
        cities: ['长春市', '吉林市', '四平市', '辽源市', '通化市', '白山市', '松原市', '白城市', '延边朝鲜族自治州']
    },
    '黑龙江省': {
        cities: ['哈尔滨市', '齐齐哈尔市', '鸡西市', '鹤岗市', '双鸭山市', '大庆市', '伊春市', '佳木斯市', '七台河市', '牡丹江市', '黑河市', '绥化市', '大兴安岭地区']
    },

    // 华东地区
    '江苏省': {
        cities: ['南京市', '苏州市', '无锡市', '常州市', '镇江市', '南通市', '泰州市', '扬州市', '盐城市', '连云港市', '徐州市', '淮安市', '宿迁市']
    },
    '浙江省': {
        cities: ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市', '舟山市', '台州市', '丽水市']
    },
    '安徽省': {
        cities: ['合肥市', '芜湖市', '蚌埠市', '淮南市', '马鞍山市', '淮北市', '铜陵市', '安庆市', '黄山市', '滁州市', '阜阳市', '宿州市', '六安市', '亳州市', '池州市', '宣城市']
    },
    '福建省': {
        cities: ['福州市', '厦门市', '泉州市', '漳州市', '莆田市', '三明市', '南平市', '龙岩市', '宁德市']
    },
    '江西省': {
        cities: ['南昌市', '景德镇市', '萍乡市', '九江市', '新余市', '鹰潭市', '赣州市', '吉安市', '宜春市', '抚州市', '上饶市']
    },
    '山东省': {
        cities: ['济南市', '青岛市', '淄博市', '枣庄市', '东营市', '烟台市', '潍坊市', '济宁市', '泰安市', '威海市', '日照市', '临沂市', '德州市', '聊城市', '滨州市', '菏泽市']
    },
    '台湾省': {
        cities: ['台北市', '高雄市', '台中市', '台南市', '桃园市', '新竹市', '基隆市', '嘉义市', '新北市', '台中县', '台南县', '高雄县']
    },

    // 华中地区
    '河南省': {
        cities: ['郑州市', '洛阳市', '开封市', '南阳市', '安阳市', '商丘市', '新乡市', '平顶山市', '许昌市', '焦作市', '周口市', '信阳市', '驻马店市', '濮阳市', '三门峡市', '漯河市', '鹤壁市', '济源市']
    },
    '湖北省': {
        cities: ['武汉市', '黄石市', '十堰市', '宜昌市', '襄阳市', '鄂州市', '荆门市', '孝感市', '荆州市', '黄冈市', '咸宁市', '随州市', '恩施土家族苗族自治州', '仙桃市', '潜江市', '天门市', '神农架林区']
    },
    '湖南省': {
        cities: ['长沙市', '株洲市', '湘潭市', '衡阳市', '邵阳市', '岳阳市', '常德市', '张家界市', '益阳市', '郴州市', '永州市', '怀化市', '娄底市', '湘西土家族苗族自治州']
    },

    // 华南地区
    '广东省': {
        cities: ['广州市', '深圳市', '珠海市', '汕头市', '佛山市', '韶关市', '湛江市', '肇庆市', '江门市', '茂名市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市']
    },
    '海南省': {
        cities: ['海口市', '三亚市', '三沙市', '儋州市', '五指山市', '琼海市', '文昌市', '万宁市', '东方市', '定安县', '屯昌县', '澄迈县', '临高县', '白沙黎族自治县', '昌江黎族自治县', '乐东黎族自治县', '陵水黎族自治县', '保亭黎族苗族自治县', '琼中黎族苗族自治县']
    },

    // 西南地区
    '四川省': {
        cities: ['成都市', '自贡市', '攀枝花市', '泸州市', '德阳市', '绵阳市', '广元市', '遂宁市', '内江市', '乐山市', '南充市', '眉山市', '宜宾市', '广安市', '达州市', '雅安市', '巴中市', '资阳市', '阿坝藏族羌族自治州', '甘孜藏族自治州', '凉山彝族自治州']
    },
    '贵州省': {
        cities: ['贵阳市', '六盘水市', '遵义市', '安顺市', '毕节市', '铜仁市', '黔西南布依族苗族自治州', '黔东南苗族侗族自治州', '黔南布依族苗族自治州']
    },
    '云南省': {
        cities: ['昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市', '楚雄彝族自治州', '红河哈尼族彝族自治州', '文山壮族苗族自治州', '西双版纳傣族自治州', '大理白族自治州', '德宏傣族景颇族自治州', '怒江傈僳族自治州', '迪庆藏族自治州']
    },

    // 西北地区
    '陕西省': {
        cities: ['西安市', '宝鸡市', '咸阳市', '渭南市', '延安市', '汉中市', '榆林市', '安康市', '商洛市', '铜川市']
    },
    '甘肃省': {
        cities: ['兰州市', '嘉峪关市', '金昌市', '白银市', '天水市', '武威市', '张掖市', '平凉市', '酒泉市', '庆阳市', '定西市', '陇南市', '临夏回族自治州', '甘南藏族自治州']
    },
    '青海省': {
        cities: ['西宁市', '海东市', '海北藏族自治州', '黄南藏族自治州', '海南藏族自治州', '果洛藏族自治州', '玉树藏族自治州', '海西蒙古族藏族自治州']
    },

    // 自治区
    '内蒙古自治区': {
        cities: ['呼和浩特市', '包头市', '乌海市', '赤峰市', '通辽市', '鄂尔多斯市', '呼伦贝尔市', '巴彦淖尔市', '乌兰察布市', '兴安盟', '锡林郭勒盟', '阿拉善盟']
    },
    '广西壮族自治区': {
        cities: ['南宁市', '柳州市', '桂林市', '梧州市', '北海市', '防城港市', '钦州市', '贵港市', '玉林市', '百色市', '贺州市', '河池市', '来宾市', '崇左市']
    },
    '西藏自治区': {
        cities: ['拉萨市', '日喀则市', '昌都市', '林芝市', '山南市', '那曲市', '阿里地区']
    },
    '宁夏回族自治区': {
        cities: ['银川市', '石嘴山市', '吴忠市', '固原市', '中卫市']
    },
    '新疆维吾尔自治区': {
        cities: ['乌鲁木齐市', '克拉玛依市', '吐鲁番市', '哈密市', '昌吉回族自治州', '博尔塔拉蒙古自治州', '巴音郭楞蒙古自治州', '阿克苏地区', '克孜勒苏柯尔克孜自治州', '喀什地区', '和田地区', '伊犁哈萨克自治州', '塔城地区', '阿勒泰地区']
    },

    // 特别行政区
    '香港特别行政区': {
        cities: ['中西区', '湾仔区', '东区', '南区', '油尖旺区', '深水埗区', '九龙城区', '黄大仙区', '观塘区', '荃湾区', '屯门区', '元朗区', '北区', '大埔区', '沙田区', '西贡区', '离岛区', '葵青区']
    },
    '澳门特别行政区': {
        cities: ['澳门半岛', '氹仔', '路环', '花地玛堂区', '圣安多尼堂区', '大堂区', '望德堂区', '风顺堂区']
    }
};

// 初始化城市选择器
function initializeCitySelector() {
    const projectLocation = '{{ project.location }}';
    const locationSelect = document.getElementById('locationSelect');

    if (provinceConfig[projectLocation]) {
        const cities = provinceConfig[projectLocation].cities;

        // 清空现有选项（保留默认选项）
        locationSelect.innerHTML = '<option value="">请选择城市/区域</option>';

        // 添加城市选项
        cities.forEach(city => {
            const option = document.createElement('option');
            option.value = city;
            option.textContent = city;
            locationSelect.appendChild(option);
        });

        console.log(`已加载 ${projectLocation} 的 ${cities.length} 个城市选项`);
    } else {
        console.warn(`未找到 ${projectLocation} 的城市配置，使用文本输入`);
        // 如果没有配置，改回文本输入
        locationSelect.outerHTML = '<input type="text" class="form-control" name="location_name" placeholder="请输入具体位置名称" required>';
    }
}

// 加载当前权重配置
function loadCurrentWeightConfig() {
    // 首先尝试获取应用的权重配置
    fetch('/risk-assessment/api/applied-weight')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.applied_weights) {
                // 使用应用的权重配置
                updateImpactWeightDisplay({
                    gdp_weight: data.applied_weights.gdp_weight || 0.6,
                    population_weight: data.applied_weights.population_weight || 0.4,
                    source: '用户应用权重'
                });
            } else {
                // 获取默认权重配置
                fetch('/risk-assessment/api/weight-configs')
                    .then(response => response.json())
                    .then(configData => {
                        if (configData.success && configData.configs.length > 0) {
                            const defaultConfig = configData.configs.find(c => c.is_default) || configData.configs[0];
                            updateImpactWeightDisplay({
                                gdp_weight: defaultConfig.gdp_weight || 0.6,
                                population_weight: defaultConfig.population_weight || 0.4,
                                source: '默认权重配置'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error loading default config:', error);
                        updateImpactWeightDisplay({
                            gdp_weight: 0.6,
                            population_weight: 0.4,
                            source: '系统默认'
                        });
                    });
            }
        })
        .catch(error => {
            console.error('Error loading applied weights:', error);
            updateImpactWeightDisplay({
                gdp_weight: 0.6,
                population_weight: 0.4,
                source: '系统默认'
            });
        });
}

// 更新影响程度权重显示
function updateImpactWeightDisplay(config) {
    const gdpPercent = (config.gdp_weight * 100).toFixed(1);
    const populationPercent = (config.population_weight * 100).toFixed(1);

    const impactWeightElement = document.getElementById('impactWeightDisplay');
    if (impactWeightElement) {
        impactWeightElement.innerHTML = `影响程度: GDP(${gdpPercent}%) + 人口(${populationPercent}%)`;
    }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化城市选择器
    initializeCitySelector();

    // 加载当前权重配置
    loadCurrentWeightConfig();
});
</script>
{% endblock %}
