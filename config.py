"""
城市内涝风险评估系统 - 配置文件
"""
import os
from datetime import timedelta

# 获取项目根目录
basedir = os.path.abspath(os.path.dirname(__file__))


class Config:
    """基础配置类"""
    
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'gis-flood-warning-system-secret-key-2025'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        f'sqlite:///{os.path.join(basedir, "data", "gis_flood_system.db")}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = False
    # SQLite特定配置（不支持连接池参数）
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,  # 保留这个，SQLite也支持
        'connect_args': {
            'check_same_thread': False,  # SQLite特定：允许多线程访问
            'timeout': 20  # SQLite连接超时
        }
    }
    
    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(basedir, 'data', 'uploads')
    
    # Session配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False  # 开发环境设为False
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 安全配置
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # CSRF令牌有效期1小时
    

    
    # GIS数据处理配置
    GIS_DATA_PATH = os.path.join(basedir, 'data', 'spatial_data')
    GIS_CACHE_PATH = os.path.join(basedir, 'data', 'spatial_data', 'cache')
    
    # 日志配置
    LOG_FOLDER = os.path.join(basedir, 'data', 'logs')
    LOG_LEVEL = 'INFO'
    

    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 确保必要的目录存在
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        os.makedirs(app.config['GIS_DATA_PATH'], exist_ok=True)
        os.makedirs(app.config['GIS_CACHE_PATH'], exist_ok=True)
        os.makedirs(app.config['LOG_FOLDER'], exist_ok=True)


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False
    LOG_LEVEL = 'DEBUG'

    # 开发环境数据库配置
    SQLALCHEMY_ECHO = False  # 关闭SQL语句打印

    # 开发环境安全配置（较宽松）
    WTF_CSRF_ENABLED = False  # 开发时可以关闭CSRF保护

    # 禁用静态文件缓存（开发环境）
    SEND_FILE_MAX_AGE_DEFAULT = 0



class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False
    
    # 生产环境安全配置
    SESSION_COOKIE_SECURE = True
    WTF_CSRF_ENABLED = True
    
    # 生产环境日志配置
    LOG_LEVEL = 'WARNING'
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # 生产环境特殊配置
        import logging
        from logging.handlers import RotatingFileHandler
        
        # 配置日志轮转
        if not app.debug:
            file_handler = RotatingFileHandler(
                os.path.join(app.config['LOG_FOLDER'], 'app.log'),
                maxBytes=10240000,  # 10MB
                backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            app.logger.setLevel(logging.INFO)
            app.logger.info('GIS Flood Warning System startup')


# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
