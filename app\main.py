"""
城市内涝风险评估系统 - 主页蓝图
"""
from flask import Blueprint, render_template, flash, redirect, url_for
from flask_login import login_required, current_user

bp = Blueprint('main', __name__)


@bp.route('/')
def index():
    """首页"""
    return render_template('index.html')


@bp.route('/dashboard')
@login_required
def dashboard():
    """仪表板 - 根据用户角色显示不同页面"""
    if current_user.is_admin():
        # 管理员看到完整的系统仪表板
        return render_template('dashboard/index.html')
    else:
        # 普通用户看到简化的用户工作台
        return render_template('dashboard/user_dashboard.html')


@bp.route('/about')
def about():
    """关于页面"""
    return render_template('about.html')


@bp.route('/admin/users')
@login_required
def admin_users():
    """用户管理 - 仅管理员可访问"""
    if not current_user.is_admin():
        flash('您没有权限访问此页面', 'error')
        return redirect(url_for('main.dashboard'))

    # 这里可以添加用户管理逻辑
    return render_template('admin/users.html')





@bp.route('/maps')
@login_required
def maps():
    """地图展示页面"""
    return render_template('maps/index.html')


@bp.route('/data/charts')
@login_required
def data_charts():
    """数据图表页面"""
    return render_template('data/charts.html')





# 数据管理相关路由
@bp.route('/data/import')
@login_required
def data_import():
    """数据导入页面"""
    return render_template('data/import.html')


@bp.route('/data/list')
@login_required
def data_list():
    """数据列表页面"""
    return render_template('data/list.html')


@bp.route('/data/analysis')
@login_required
def data_analysis():
    """空间分析页面"""
    if not current_user.is_admin():
        flash('您没有权限访问此页面', 'error')
        return redirect(url_for('main.dashboard'))
    return render_template('data/analysis.html')


@bp.route('/data/processing')
@login_required
def data_processing():
    """数据处理页面"""
    return render_template('data/processing.html')


@bp.route('/data/storage')
@login_required
def data_storage():
    """存储管理页面"""
    if not current_user.is_admin():
        flash('您没有权限访问此页面', 'error')
        return redirect(url_for('main.dashboard'))
    return render_template('data/storage_management.html')








