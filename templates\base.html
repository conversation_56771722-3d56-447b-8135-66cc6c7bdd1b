<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ app_name }}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/main.css') }}" rel="stylesheet">
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-water me-2"></i>{{ app_name }}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.dashboard') }}">
                            {% if current_user.is_admin() %}
                            <i class="fas fa-tachometer-alt me-1"></i>系统仪表板
                            {% else %}
                            <i class="fas fa-user-circle me-1"></i>我的工作台
                            {% endif %}
                        </a>
                    </li>

                    <!-- 数据管理模块 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dataManagementDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-database me-1"></i>数据管理模块
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('main.data_import') }}"><i class="fas fa-upload me-1"></i>数据导入</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.data_processing') }}"><i class="fas fa-cogs me-1"></i>数据处理</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.data_storage') }}"><i class="fas fa-hdd me-1"></i>数据存储</a></li>
                        </ul>
                    </li>

                    <!-- 可视化模块 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="visualizationDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-line me-1"></i>可视化模块
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('visualization.index') }}"><i class="fas fa-home me-1"></i>可视化首页</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('visualization.map_view') }}"><i class="fas fa-map me-1"></i>地图展示</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('visualization.charts_view') }}"><i class="fas fa-chart-bar me-1"></i>图表分析</a></li>
                        </ul>
                    </li>

                    <!-- 风险评估模块 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="riskAssessmentDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-exclamation-triangle me-1"></i>风险评估模块
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('risk_assessment.index') }}"><i class="fas fa-layer-group me-1"></i>风险等级划分</a></li>
                        </ul>
                    </li>

                    <!-- 管理员专用菜单 -->
                    {% if current_user.is_admin() %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.admin_users') }}">
                            <i class="fas fa-users-cog me-1"></i>用户管理
                        </a>
                    </li>
                    {% endif %}
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ current_user.username }}
                            {% if current_user.is_admin() %}
                            <span class="badge bg-danger ms-1">管理员</span>
                            {% else %}
                            <span class="badge bg-primary ms-1">用户</span>
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-edit me-1"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-1"></i>设置</a></li>
                            {% if current_user.is_admin() %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="showComingSoon('系统监控')"><i class="fas fa-chart-line me-1"></i>系统监控</a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}"><i class="fas fa-sign-out-alt me-1"></i>退出登录</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.register') }}">
                            <i class="fas fa-user-plus me-1"></i>注册
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 消息闪现 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- 主要内容 -->
    <main class="container-fluid py-4">
        {% block content %}{% endblock %}
    </main>

    <!-- JavaScript -->
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <!-- ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- 功能提示脚本 -->
    <script>
    function showComingSoon(featureName) {
        alert('🚧 ' + featureName + ' 功能正在开发中，敬请期待！');
    }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
