/* 可视化模块JavaScript功能 */

// 全局变量
let map = null;
let currentProvince = null;
let currentYear = null;
let currentCity = null;
let currentLanduseStatistics = null; // 存储当前土地利用统计数据

let provinceData = {};
let provinceYears = {};
let mapLayers = {
    flood: null,
    risk: null,
    admin: null,
    terrain: null,
    heatmap: null,
    landuse: null,
    riskBoundaries: null,
    landuseBoundaries: null
};
let baseMapLayers = {};
let demTileLayers = {};
let currentBaseMap = 'gaode';

// 省份配置
const provinceConfig = {
    '北京市': { center: [39.9042, 116.4074], zoom: 8 },
    '天津市': { center: [39.3434, 117.3616], zoom: 8 },
    '上海市': { center: [31.2304, 121.4737], zoom: 8 },
    '重庆市': { center: [29.5647, 106.5507], zoom: 7 },
    '河北省': { center: [38.0428, 114.5149], zoom: 7 },
    '山西省': { center: [37.8570, 112.5489], zoom: 7 },
    '辽宁省': { center: [41.8057, 123.4315], zoom: 7 },
    '吉林省': { center: [43.8868, 125.3245], zoom: 7 },
    '黑龙江省': { center: [45.7732, 126.6614], zoom: 6 },
    '江苏省': { center: [32.0617, 118.7778], zoom: 7 },
    '浙江省': { center: [30.2741, 120.1551], zoom: 7 },
    '安徽省': { center: [31.8612, 117.2844], zoom: 7 },
    '福建省': { center: [26.0745, 119.2965], zoom: 7 },
    '江西省': { center: [28.6820, 115.8579], zoom: 7 },
    '山东省': { center: [36.6683, 117.0208], zoom: 7 },
    '台湾省': { center: [23.8000, 121.0000], zoom: 7 },
    '河南省': { center: [34.7667, 113.65], zoom: 7 },
    '湖北省': { center: [30.5928, 114.3055], zoom: 7 },
    '湖南省': { center: [28.2282, 112.9388], zoom: 7 },
    '广东省': { center: [23.1291, 113.2644], zoom: 7 },
    '海南省': { center: [20.0174, 110.3493], zoom: 8 },
    '四川省': { center: [30.6171, 104.0648], zoom: 7 },
    '贵州省': { center: [26.5783, 106.7135], zoom: 7 },
    '云南省': { center: [25.0389, 102.7183], zoom: 6 },
    '陕西省': { center: [34.2667, 108.95], zoom: 7 },
    '甘肃省': { center: [36.0611, 103.8343], zoom: 6 },
    '青海省': { center: [36.6171, 101.7782], zoom: 6 },
    '内蒙古自治区': { center: [40.8176, 111.7651], zoom: 5 },
    '广西壮族自治区': { center: [22.8150, 108.3669], zoom: 7 },
    '西藏自治区': { center: [29.6625, 91.1322], zoom: 5 },
    '宁夏回族自治区': { center: [38.4872, 106.2309], zoom: 8 },
    '新疆维吾尔自治区': { center: [43.7930, 87.6177], zoom: 4 },
    '香港特别行政区': { center: [22.3193, 114.1694], zoom: 10 },
    '澳门特别行政区': { center: [22.1987, 113.5439], zoom: 12 }
};

document.addEventListener('DOMContentLoaded', function() {
    initializeMap();
    bindEvents();

    const urlParams = new URLSearchParams(window.location.search);
    const provinceParam = urlParams.get('province');
    if (provinceParam) {
        $('#provinceSelect').val(provinceParam);
        // 注释掉自动加载省份数据，避免在交互地图上显示假数据
        // loadProvinceData(provinceParam);
    }
});

function initializeMap() {
    // 检查地图是否已经初始化，避免重复初始化
    if (map) {
        console.log('地图已经初始化，跳过重复初始化');
        return;
    }

    // 检查地图容器是否存在
    const mapContainer = document.getElementById('leafletMap');
    if (!mapContainer) {
        console.error('地图容器 #leafletMap 不存在');
        return;
    }

    // 检查容器是否已经被Leaflet初始化
    if (mapContainer._leaflet_id) {
        console.log('地图容器已被初始化，清理后重新初始化');
        mapContainer._leaflet_id = null;
        mapContainer.innerHTML = '';
    }

    try {
        map = L.map('leafletMap', {
            center: [35.0, 110.0],
            zoom: 5,
            zoomControl: true,
            attributionControl: true
        });
        console.log('地图初始化成功');

        // 添加指北针和比例尺控件
        if (typeof initMapControls === 'function') {
            initMapControls(map, {
                compass: true,
                scale: true,
                compassPosition: 'topright',
                scalePosition: 'bottomleft'
            });
        }
    } catch (error) {
        console.error('地图初始化失败:', error);
        return;
    }

    baseMapLayers = {
        gaode: L.tileLayer('https://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', {
            attribution: '© 高德地图'
        }),
        satellite: L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
            attribution: '© Esri'
        })
    };

    baseMapLayers[currentBaseMap].addTo(map);

    map.on('mousemove', function (e) {
        const lat = e.latlng.lat.toFixed(6);
        const lng = e.latlng.lng.toFixed(6);
        $('#mouseCoords').text(`${lat}, ${lng}`);
    });

    map.on('zoomend', function () {
        $('#zoomLevel').text(map.getZoom());
    });
}

function bindEvents() {
    $('#provinceSelect').on('change', function () {
        const province = $(this).val();
        if (province) {
            currentProvince = province;

            // 检查当前是否在行政区划图层模式
            const currentLayer = getCurrentActiveLayer();
            if (currentLayer === 'admin') {
                // 如果当前是行政区划图层，自动更新显示
                console.log(`省份选择变更，自动更新行政区划图: ${province}`);
                if (typeof generateAdminPyEchartsMap === 'function') {
                    generateAdminPyEchartsMap(province);
                }
            }

            // 只在非交互地图模式下自动加载数据
            // 交互地图模式下用户需要手动选择要显示的图层
            // loadProvinceData(province);
        }
    });

    $('#baseMapSelect').on('change', function () {
        switchBaseMap($(this).val());
    });
}

// 获取当前活跃的图层类型
function getCurrentActiveLayer() {
    // 检查哪个图层按钮是激活状态
    const activeButton = $('.layer-switch-btn.active');
    if (activeButton.length > 0) {
        return activeButton.data('layer');
    }

    // 检查pyecharts地图是否显示
    const pyechartsMap = $('#pyechartsMap');
    if (pyechartsMap.is(':visible')) {
        // 根据当前显示的内容判断图层类型
        const iframe = $('#pyechartsFrame');
        if (iframe.length > 0 && iframe.attr('src')) {
            const src = iframe.attr('src');
            if (src.includes('admin_map')) {
                return 'admin';
            } else if (src.includes('risk_map')) {
                return 'risk';
            } else if (src.includes('landuse')) {
                return 'landuse';
            }
        }
    }

    return null;
}

function loadProvinceData(province) {
    showMapLoading('加载省份数据...');
    clearDataLayers();

    // 1. 加载省份边界
    fetch(`/visualization/api/province-boundaries/${encodeURIComponent(province)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                const boundaryLayer = L.geoJSON(data.data, {
                    style: {
                        color: "#0d6efd",
                        weight: 2,
                        opacity: 0.8,
                        fillOpacity: 0.1
                    }
                }).addTo(map);
                map.fitBounds(boundaryLayer.getBounds());
                mapLayers.admin = boundaryLayer;

                // 2. 边界加载成功后，加载风险数据
                loadProvinceRiskData(province);
            } else {
                showAlert(`加载 ${province} 边界失败: ${data.error}`, 'danger');
            }
        })
        .catch(error => {
            console.error('加载省份边界失败:', error);
            showAlert(`加载 ${province} 边界失败`, 'danger');
        })
        .finally(() => {
            hideMapLoading();
        });
}

function loadProvinceRiskData(province) {
    let apiUrl = `/visualization/api/province-risk-boundaries/${encodeURIComponent(province)}`;

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data && data.data.features) {
                displayRiskBoundaries(data.data);
                updateStatistics(data.data.features);
                $('#riskLegendCard').show();
            } else {
                showAlert(`暂无 ${province} 的风险区划数据`, 'warning');
            }
        })
        .catch(error => {
            console.error('加载风险区划数据失败:', error);
            showAlert(`加载 ${province} 的风险区划数据失败`, 'danger');
        });
}

function displayRiskBoundaries(geoJsonData) {
    if (mapLayers.riskBoundaries) {
        map.removeLayer(mapLayers.riskBoundaries);
    }

    mapLayers.riskBoundaries = L.geoJSON(geoJsonData, {
        style: function(feature) {
            return {
                fillColor: feature.properties.fill_color || '#cccccc',
                weight: 1,
                opacity: 1,
                color: 'white',
                fillOpacity: feature.properties.fill_opacity || 0.7
            };
        },
        onEachFeature: function(feature, layer) {
            layer.bindPopup(`
                <h6>${feature.properties.name}</h6>
                <p><strong>风险等级:</strong> ${feature.properties.risk_level}</p>
                <p><strong>风险指数:</strong> ${feature.properties.risk_index ? feature.properties.risk_index.toFixed(3) : 'N/A'}</p>
            `);
        }
    }).addTo(map);
}

function clearDataLayers() {
    for (const layer in mapLayers) {
        if (mapLayers[layer]) {
            map.removeLayer(mapLayers[layer]);
            mapLayers[layer] = null;
        }
    }
}

function switchBaseMap(baseMapType) {
    map.removeLayer(baseMapLayers[currentBaseMap]);
    currentBaseMap = baseMapType;
    baseMapLayers[currentBaseMap].addTo(map);
}

function updateStatistics(features) {
    if (!features || features.length === 0) {
        $('#totalPoints, #highRiskCount, #mediumRiskCount').text('--');
        return;
    }

    $('#totalPoints').text(features.length);

    const riskCounts = features.reduce((acc, feature) => {
        const riskLevel = feature.properties.risk_level;
        if (riskLevel === '高风险' || riskLevel === '极高风险') {
            acc.high = (acc.high || 0) + 1;
        }
        if (riskLevel === '中风险') {
            acc.medium = (acc.medium || 0) + 1;
        }
        return acc;
    }, {});

    $('#highRiskCount').text(riskCounts.high || 0);
    $('#mediumRiskCount').text(riskCounts.medium || 0);
}

function showMapLoading(message) {
    $('#mapContainer').append(`<div class="map-loading">${message}</div>`);
}

function hideMapLoading() {
    $('.map-loading').remove();
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

// 图层切换功能
// 注意：switchToLayer函数已移除，完全使用map.html中的实现

// 测量工具相关函数
function clearMeasurement() {
    console.log('清除测量');
    showAlert('测量工具功能开发中', 'info');
}

// GIS图层相关函数
function toggleGISLayer(layerId, checked) {
    console.log('切换GIS图层:', layerId, checked);
    showAlert('GIS图层功能开发中', 'info');
}

function deleteGISLayer(layerId) {
    console.log('删除GIS图层:', layerId);
    showAlert('删除图层功能开发中', 'info');
}

// 风险区划相关函数
function loadProvinceRiskMap(provinceName, year = 'latest') {
    console.log(`加载 ${provinceName} ${year}年 的风险区划图...`);

    // 清除现有数据图层
    clearDataLayers();

    showMapLoading('正在生成省份风险区划图...');

    // 生成风险区划图
    let apiUrl = `/visualization/api/generate-pyecharts-map/${encodeURIComponent(provinceName)}`;
    const params = new URLSearchParams();
    params.append('year', year);
    params.append('type', 'province_map');

    apiUrl += '?' + params.toString();

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProvinceRiskMap(data.map_url, provinceName, data.year || year);
                showAlert(`${provinceName} ${year}年 风险区划图加载成功`, 'success');

                // 显示风险图例
                $('#riskLegendCard').show();

            } else {
                console.error('风险区划图生成失败:', data.error);
                showAlert(`风险区划图生成失败: ${data.error}`, 'danger');
            }
        })
        .catch(error => {
            console.error('生成风险区划图失败:', error);
            showAlert(`风险区划图生成失败: ${error.message}`, 'danger');
        })
        .finally(() => {
            hideMapLoading();
        });
}

function showRiskZoningControls() {
    // 显示年份选择器
    $('#yearSelectContainer').show();

    // 清除现有数据图层
    clearDataLayers();

    // 隐藏风险图例
    $('#riskLegendCard').hide();

    // 如果已选择省份，加载年份数据
    const selectedProvince = $('#provinceSelect').val();
    if (selectedProvince) {
        loadProvinceYears(selectedProvince);
    }
}

function hideRiskZoningControls() {
    $('#yearSelectContainer').hide();
    $('#riskLegendCard').hide();
}

function loadProvinceYears(provinceName) {
    console.log(`加载 ${provinceName} 的年份列表...`);

    fetch(`/api/provinces/${encodeURIComponent(provinceName)}/years`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = $('#yearSelect');

                if (data.years && data.years.length > 0) {
                    // 有年份数据
                    select.html('<option value="">请选择年份...</option>');
                    data.years.forEach(yearInfo => {
                        select.append(`<option value="${yearInfo.year}">${yearInfo.year}年 (${yearInfo.project_count}个项目)</option>`);
                    });
                    console.log(`成功加载 ${data.years.length} 个年份`);
                } else {
                    // 没有年份数据
                    select.html('<option value="">暂无年份数据</option>');
                    console.log(`省份 ${provinceName} 暂无年份数据`);

                    // 显示友好提示
                    if (data.message) {
                        showAlert(data.message, 'info');
                    } else {
                        showAlert(`暂无 ${provinceName} 的年份数据，请先导入相关数据`, 'info');
                    }
                }
            } else {
                const select = $('#yearSelect');
                select.html('<option value="">暂无年份数据</option>');
                showAlert(data.error || '加载年份列表失败', 'warning');
            }
        })
        .catch(error => {
            console.error('加载年份列表失败:', error);
            const select = $('#yearSelect');
            select.html('<option value="">暂无年份数据</option>');
            showAlert('网络错误，无法加载年份列表', 'warning');
        });
}

function generateProvinceRiskMap(provinceName, year) {
    console.log(`生成 ${provinceName} ${year}年 风险区划图...`);

    showMapLoading('正在生成省份风险区划图...');

    // 构建API URL
    let apiUrl = `/visualization/api/generate-pyecharts-map/${encodeURIComponent(provinceName)}`;
    const params = new URLSearchParams();
    params.append('year', year);
    params.append('type', 'province_map');

    apiUrl += '?' + params.toString();

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProvinceRiskMap(data.map_url, provinceName, year);
                showAlert(`${provinceName} ${year}年 风险区划图生成成功`, 'success');

                // 显示风险图例
                $('#riskLegendCard').show();

            } else {
                console.error('风险区划图生成失败:', data.error);
                showAlert(`风险区划图生成失败: ${data.error}`, 'danger');
            }
        })
        .catch(error => {
            console.error('生成风险区划图失败:', error);
            showAlert(`风险区划图生成失败: ${error.message}`, 'danger');
        })
        .finally(() => {
            hideMapLoading();
        });
}

function displayProvinceRiskMap(mapUrl, provinceName, year) {
    console.log('开始显示省份风险区划图:', mapUrl);

    // 隐藏Leaflet地图，显示PyEcharts地图
    $('#leafletMap').hide();
    $('#pyechartsMap').show();

    // 设置iframe源
    const fullUrl = `/static/${mapUrl}`;
    $('#pyechartsFrame').attr('src', fullUrl);

    console.log('省份风险区划图已显示:', fullUrl);
    console.log('Leaflet地图隐藏状态:', $('#leafletMap').is(':hidden'));
    console.log('PyEcharts地图显示状态:', $('#pyechartsMap').is(':visible'));
}

function switchBackToInteractiveMap() {
    // 显示Leaflet地图，隐藏PyEcharts地图
    $('#leafletMap').show();
    $('#pyechartsMap').hide();

    // 隐藏风险图例
    $('#riskLegendCard').hide();

    console.log('已切换回交互地图');
}

// 确保函数在全局作用域中可用（不包括switchToLayer，使用map.html中的完整实现）
window.clearMeasurement = clearMeasurement;
window.toggleGISLayer = toggleGISLayer;
window.deleteGISLayer = deleteGISLayer;
window.showRiskZoningControls = showRiskZoningControls;
window.hideRiskZoningControls = hideRiskZoningControls;
window.loadProvinceYears = loadProvinceYears;
window.loadProvinceRiskMap = loadProvinceRiskMap;
window.switchBackToInteractiveMap = switchBackToInteractiveMap;

// 调试信息
console.log('Visualization.js loaded successfully');
console.log('Available functions:', {
    switchToLayer: typeof switchToLayer,
    showRiskZoningControls: typeof showRiskZoningControls,
    loadProvinceYears: typeof loadProvinceYears,
    loadProvinceRiskMap: typeof loadProvinceRiskMap
});