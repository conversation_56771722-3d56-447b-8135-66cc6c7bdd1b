"""
在线用户跟踪系统
"""
from datetime import datetime, timedelta, timezone


def get_china_time():
    """获取中国本地时间（UTC+8）"""
    return datetime.now(timezone(timedelta(hours=8)))
from collections import defaultdict
import threading

class OnlineUserTracker:
    """在线用户跟踪器"""
    
    def __init__(self):
        self.users = {}  # {user_id: {'username': str, 'last_seen': datetime, 'ip': str}}
        self.anonymous_users = defaultdict(datetime)  # {ip: last_seen}
        self.lock = threading.Lock()
        self.timeout = timedelta(seconds=30)  # 30秒无活动视为离线
    
    def update_user_activity(self, user_id=None, username=None, ip_address=None):
        """更新用户活动时间"""
        with self.lock:
            now = get_china_time()
            
            if user_id and username:
                # 已登录用户
                self.users[user_id] = {
                    'username': username,
                    'last_seen': now,
                    'ip': ip_address
                }
                # 从匿名用户中移除（如果存在）
                if ip_address in self.anonymous_users:
                    del self.anonymous_users[ip_address]
            elif ip_address:
                # 匿名用户
                if not any(user['ip'] == ip_address for user in self.users.values()):
                    self.anonymous_users[ip_address] = now
    
    def remove_user(self, user_id):
        """移除用户（登出时调用）"""
        with self.lock:
            if user_id in self.users:
                username = self.users[user_id]['username']
                del self.users[user_id]
                print(f"在线用户跟踪器: 移除用户 {username} (ID: {user_id}), 当前在线用户数: {len(self.users)}")
            else:
                print(f"在线用户跟踪器: 用户 ID {user_id} 不在在线列表中")
    
    def cleanup_inactive_users(self):
        """清理不活跃的用户"""
        with self.lock:
            now = get_china_time()
            cutoff_time = now - self.timeout
            
            # 清理不活跃的已登录用户
            inactive_users = [
                user_id for user_id, data in self.users.items()
                if data['last_seen'] < cutoff_time
            ]
            for user_id in inactive_users:
                del self.users[user_id]
            
            # 清理不活跃的匿名用户
            inactive_ips = [
                ip for ip, last_seen in self.anonymous_users.items()
                if last_seen < cutoff_time
            ]
            for ip in inactive_ips:
                del self.anonymous_users[ip]
    
    def get_online_count(self):
        """获取在线用户总数"""
        self.cleanup_inactive_users()
        with self.lock:
            return len(self.users) + len(self.anonymous_users)
    
    def get_logged_in_count(self):
        """获取已登录用户数"""
        self.cleanup_inactive_users()
        with self.lock:
            return len(self.users)
    
    def get_anonymous_count(self):
        """获取匿名用户数"""
        self.cleanup_inactive_users()
        with self.lock:
            return len(self.anonymous_users)
    
    def get_online_users(self):
        """获取在线用户列表"""
        self.cleanup_inactive_users()
        with self.lock:
            return [
                {
                    'user_id': user_id,
                    'username': data['username'],
                    'last_seen': data['last_seen'],
                    'ip': data['ip']
                }
                for user_id, data in self.users.items()
            ]
    
    def get_user_stats(self):
        """获取用户统计信息"""
        self.cleanup_inactive_users()
        with self.lock:
            return {
                'total_online': len(self.users) + len(self.anonymous_users),
                'logged_in': len(self.users),
                'anonymous': len(self.anonymous_users),
                'users': [
                    {
                        'username': data['username'],
                        'last_seen': data['last_seen'].strftime('%H:%M:%S'),
                        'ip': data['ip']
                    }
                    for data in self.users.values()
                ]
            }

# 全局在线用户跟踪器实例
online_tracker = OnlineUserTracker()
