"""
中国省级行政区划统一配置文件
包含所有34个省级行政区的完整信息
"""

# 中国所有省级行政区配置
PROVINCES_CONFIG = {
    # 直辖市
    '北京市': {
        'english': 'beijing',
        'short_name': '北京',
        'map_name': '北京',
        'type': 'municipality',
        'bounds': {'min_lon': 115.42, 'max_lon': 117.51, 'min_lat': 39.44, 'max_lat': 41.06},
        'center': {'lat': 39.9042, 'lng': 116.4074},
        'keywords': ['beijing', '北京', 'bj']
    },
    '天津市': {
        'english': 'tianjin',
        'short_name': '天津',
        'map_name': '天津',
        'type': 'municipality',
        'bounds': {'min_lon': 116.43, 'max_lon': 118.04, 'min_lat': 38.34, 'max_lat': 40.25},
        'center': {'lat': 39.3434, 'lng': 117.3616},
        'keywords': ['tianjin', '天津', 'tj']
    },
    '上海市': {
        'english': 'shanghai',
        'short_name': '上海',
        'map_name': '上海',
        'type': 'municipality',
        'bounds': {'min_lon': 120.52, 'max_lon': 122.12, 'min_lat': 30.40, 'max_lat': 31.53},
        'center': {'lat': 31.2304, 'lng': 121.4737},
        'keywords': ['shanghai', '上海', 'sh']
    },
    '重庆市': {
        'english': 'chongqing',
        'short_name': '重庆',
        'map_name': '重庆',
        'type': 'municipality',
        'bounds': {'min_lon': 105.17, 'max_lon': 110.11, 'min_lat': 28.10, 'max_lat': 32.13},
        'center': {'lat': 29.5647, 'lng': 106.5507},
        'keywords': ['chongqing', '重庆', 'cq']
    },
    
    # 华北地区
    '河北省': {
        'english': 'hebei',
        'short_name': '河北',
        'map_name': '河北',
        'type': 'province',
        'bounds': {'min_lon': 113.27, 'max_lon': 119.92, 'min_lat': 36.05, 'max_lat': 42.40},
        'center': {'lat': 38.0428, 'lng': 114.5149},
        'keywords': ['hebei', '河北', 'hb']
    },
    '山西省': {
        'english': 'shanxi',
        'short_name': '山西',
        'map_name': '山西',
        'type': 'province',
        'bounds': {'min_lon': 110.14, 'max_lon': 114.33, 'min_lat': 34.34, 'max_lat': 40.44},
        'center': {'lat': 37.5358, 'lng': 112.5490},
        'keywords': ['shanxi', '山西', 'sx']
    },
    
    # 东北地区
    '辽宁省': {
        'english': 'liaoning',
        'short_name': '辽宁',
        'map_name': '辽宁',
        'type': 'province',
        'bounds': {'min_lon': 118.53, 'max_lon': 125.46, 'min_lat': 38.43, 'max_lat': 43.26},
        'center': {'lat': 41.2956, 'lng': 123.4315},
        'keywords': ['liaoning', '辽宁', 'ln']
    },
    '吉林省': {
        'english': 'jilin',
        'short_name': '吉林',
        'map_name': '吉林',
        'type': 'province',
        'bounds': {'min_lon': 121.38, 'max_lon': 131.19, 'min_lat': 40.52, 'max_lat': 46.18},
        'center': {'lat': 43.8868, 'lng': 125.3245},
        'keywords': ['jilin', '吉林', 'jl']
    },
    '黑龙江省': {
        'english': 'heilongjiang',
        'short_name': '黑龙江',
        'map_name': '黑龙江',
        'type': 'province',
        'bounds': {'min_lon': 121.11, 'max_lon': 135.05, 'min_lat': 43.26, 'max_lat': 53.33},
        'center': {'lat': 45.8038, 'lng': 126.5349},
        'keywords': ['heilongjiang', '黑龙江', 'hlj']
    },
    
    # 华东地区
    '江苏省': {
        'english': 'jiangsu',
        'short_name': '江苏',
        'map_name': '江苏',
        'type': 'province',
        'bounds': {'min_lon': 116.18, 'max_lon': 121.57, 'min_lat': 30.45, 'max_lat': 35.20},
        'center': {'lat': 32.0415, 'lng': 118.7633},
        'keywords': ['jiangsu', '江苏', 'js']
    },
    '浙江省': {
        'english': 'zhejiang',
        'short_name': '浙江',
        'map_name': '浙江',
        'type': 'province',
        'bounds': {'min_lon': 118.01, 'max_lon': 123.10, 'min_lat': 27.02, 'max_lat': 31.11},
        'center': {'lat': 30.2741, 'lng': 120.1551},
        'keywords': ['zhejiang', '浙江', 'zj']
    },
    '安徽省': {
        'english': 'anhui',
        'short_name': '安徽',
        'map_name': '安徽',
        'type': 'province',
        'bounds': {'min_lon': 114.54, 'max_lon': 119.37, 'min_lat': 29.41, 'max_lat': 34.38},
        'center': {'lat': 31.8612, 'lng': 117.2273},
        'keywords': ['anhui', '安徽', 'ah']
    },
    '福建省': {
        'english': 'fujian',
        'short_name': '福建',
        'map_name': '福建',
        'type': 'province',
        'bounds': {'min_lon': 115.50, 'max_lon': 120.40, 'min_lat': 23.50, 'max_lat': 28.20},
        'center': {'lat': 26.0745, 'lng': 119.2965},
        'keywords': ['fujian', '福建', 'fj']
    },
    '江西省': {
        'english': 'jiangxi',
        'short_name': '江西',
        'map_name': '江西',
        'type': 'province',
        'bounds': {'min_lon': 113.34, 'max_lon': 118.28, 'min_lat': 24.29, 'max_lat': 30.04},
        'center': {'lat': 28.6747, 'lng': 115.9092},
        'keywords': ['jiangxi', '江西', 'jx']
    },
    '山东省': {
        'english': 'shandong',
        'short_name': '山东',
        'map_name': '山东',
        'type': 'province',
        'bounds': {'min_lon': 114.47, 'max_lon': 122.43, 'min_lat': 34.22, 'max_lat': 38.24},
        'center': {'lat': 36.3427, 'lng': 118.1498},
        'keywords': ['shandong', '山东', 'sd']
    },
    
    # 华中地区
    '河南省': {
        'english': 'henan',
        'short_name': '河南',
        'map_name': '河南',
        'type': 'province',
        'bounds': {'min_lon': 110.21, 'max_lon': 116.39, 'min_lat': 31.23, 'max_lat': 36.22},
        'center': {'lat': 34.7667, 'lng': 113.65},
        'keywords': ['henan', '河南', 'hn']
    },
    '湖北省': {
        'english': 'hubei',
        'short_name': '湖北',
        'map_name': '湖北',
        'type': 'province',
        'bounds': {'min_lon': 108.21, 'max_lon': 116.07, 'min_lat': 29.01, 'max_lat': 33.20},
        'center': {'lat': 30.5928, 'lng': 114.2998},
        'keywords': ['hubei', '湖北', 'hb']
    },
    '湖南省': {
        'english': 'hunan',
        'short_name': '湖南',
        'map_name': '湖南',
        'type': 'province',
        'bounds': {'min_lon': 108.47, 'max_lon': 114.15, 'min_lat': 24.38, 'max_lat': 30.08},
        'center': {'lat': 28.2104, 'lng': 112.9836},
        'keywords': ['hunan', '湖南', 'hn']
    },
    
    # 华南地区
    '广东省': {
        'english': 'guangdong',
        'short_name': '广东',
        'map_name': '广东',
        'type': 'province',
        'bounds': {'min_lon': 109.39, 'max_lon': 117.19, 'min_lat': 20.13, 'max_lat': 25.31},
        'center': {'lat': 23.1291, 'lng': 113.2644},
        'keywords': ['guangdong', '广东', 'gd']
    },
    '海南省': {
        'english': 'hainan',
        'short_name': '海南',
        'map_name': '海南',
        'type': 'province',
        'bounds': {'min_lon': 108.37, 'max_lon': 111.03, 'min_lat': 18.10, 'max_lat': 20.10},
        'center': {'lat': 20.0178, 'lng': 110.3417},
        'keywords': ['hainan', '海南', 'hn']
    },
    
    # 西南地区
    '四川省': {
        'english': 'sichuan',
        'short_name': '四川',
        'map_name': '四川',
        'type': 'province',
        'bounds': {'min_lon': 97.21, 'max_lon': 108.12, 'min_lat': 26.03, 'max_lat': 34.19},
        'center': {'lat': 30.6171, 'lng': 104.0648},
        'keywords': ['sichuan', '四川', 'sc']
    },
    '贵州省': {
        'english': 'guizhou',
        'short_name': '贵州',
        'map_name': '贵州',
        'type': 'province',
        'bounds': {'min_lon': 103.36, 'max_lon': 109.35, 'min_lat': 24.37, 'max_lat': 29.13},
        'center': {'lat': 26.5783, 'lng': 106.7135},
        'keywords': ['guizhou', '贵州', 'gz']
    },
    '云南省': {
        'english': 'yunnan',
        'short_name': '云南',
        'map_name': '云南',
        'type': 'province',
        'bounds': {'min_lon': 97.31, 'max_lon': 106.11, 'min_lat': 21.08, 'max_lat': 29.15},
        'center': {'lat': 25.0438, 'lng': 102.7123},
        'keywords': ['yunnan', '云南', 'yn']
    },
    
    # 西北地区
    '陕西省': {
        'english': 'shaanxi',
        'short_name': '陕西',
        'map_name': '陕西',
        'type': 'province',
        'bounds': {'min_lon': 105.29, 'max_lon': 111.15, 'min_lat': 31.42, 'max_lat': 39.35},
        'center': {'lat': 34.3416, 'lng': 108.9398},
        'keywords': ['shaanxi', '陕西', 'sn']
    },
    '甘肃省': {
        'english': 'gansu',
        'short_name': '甘肃',
        'map_name': '甘肃',
        'type': 'province',
        'bounds': {'min_lon': 92.13, 'max_lon': 108.46, 'min_lat': 32.11, 'max_lat': 42.57},
        'center': {'lat': 36.0611, 'lng': 103.8343},
        'keywords': ['gansu', '甘肃', 'gs']
    },
    '青海省': {
        'english': 'qinghai',
        'short_name': '青海',
        'map_name': '青海',
        'type': 'province',
        'bounds': {'min_lon': 89.35, 'max_lon': 103.04, 'min_lat': 31.39, 'max_lat': 39.19},
        'center': {'lat': 36.5230, 'lng': 101.7804},
        'keywords': ['qinghai', '青海', 'qh']
    },
    
    # 自治区
    '内蒙古自治区': {
        'english': 'inner_mongolia',
        'short_name': '内蒙古',
        'map_name': '内蒙古',
        'type': 'autonomous_region',
        'bounds': {'min_lon': 97.12, 'max_lon': 126.04, 'min_lat': 37.24, 'max_lat': 53.23},
        'center': {'lat': 44.2930, 'lng': 111.6708},
        'keywords': ['inner_mongolia', 'neimenggu', '内蒙古', 'nmg']
    },
    '广西壮族自治区': {
        'english': 'guangxi',
        'short_name': '广西',
        'map_name': '广西',
        'type': 'autonomous_region',
        'bounds': {'min_lon': 104.26, 'max_lon': 112.04, 'min_lat': 20.54, 'max_lat': 26.24},
        'center': {'lat': 22.8150, 'lng': 108.3669},
        'keywords': ['guangxi', '广西', 'gx']
    },
    '西藏自治区': {
        'english': 'tibet',
        'short_name': '西藏',
        'map_name': '西藏',
        'type': 'autonomous_region',
        'bounds': {'min_lon': 78.25, 'max_lon': 99.06, 'min_lat': 26.50, 'max_lat': 36.32},
        'center': {'lat': 29.6544, 'lng': 91.1322},
        'keywords': ['tibet', 'xizang', '西藏', 'xz']
    },
    '宁夏回族自治区': {
        'english': 'ningxia',
        'short_name': '宁夏',
        'map_name': '宁夏',
        'type': 'autonomous_region',
        'bounds': {'min_lon': 104.17, 'max_lon': 107.39, 'min_lat': 35.14, 'max_lat': 39.23},
        'center': {'lat': 38.4717, 'lng': 106.2586},
        'keywords': ['ningxia', '宁夏', 'nx']
    },
    '新疆维吾尔自治区': {
        'english': 'xinjiang',
        'short_name': '新疆',
        'map_name': '新疆',
        'type': 'autonomous_region',
        'bounds': {'min_lon': 73.40, 'max_lon': 96.18, 'min_lat': 34.25, 'max_lat': 48.10},
        'center': {'lat': 43.7930, 'lng': 87.6177},
        'keywords': ['xinjiang', '新疆', 'xj']
    },
    
    # 特别行政区
    '香港特别行政区': {
        'english': 'hongkong',
        'short_name': '香港',
        'map_name': '香港',
        'type': 'special_administrative_region',
        'bounds': {'min_lon': 113.76, 'max_lon': 114.51, 'min_lat': 22.13, 'max_lat': 22.58},
        'center': {'lat': 22.3193, 'lng': 114.1694},
        'keywords': ['hongkong', 'xianggang', '香港', 'hk']
    },
    '澳门特别行政区': {
        'english': 'macau',
        'short_name': '澳门',
        'map_name': '澳门',
        'type': 'special_administrative_region',
        'bounds': {'min_lon': 113.52, 'max_lon': 113.60, 'min_lat': 22.10, 'max_lat': 22.22},
        'center': {'lat': 22.1987, 'lng': 113.5439},
        'keywords': ['macau', 'aomen', '澳门', 'am']
    },
    
    # 台湾省（暂时保留）
    '台湾省': {
        'english': 'taiwan',
        'short_name': '台湾',
        'map_name': '台湾',
        'type': 'province',
        'bounds': {'min_lon': 119.31, 'max_lon': 122.01, 'min_lat': 21.90, 'max_lat': 25.30},
        'center': {'lat': 23.6978, 'lng': 120.9605},
        'keywords': ['taiwan', '台湾', 'tw']
    }
}

# 省份名称映射（用于快速查找）
PROVINCE_NAME_MAPPING = {}
PROVINCE_KEYWORD_MAPPING = {}

# 构建映射表
for full_name, config in PROVINCES_CONFIG.items():
    # 名称映射
    PROVINCE_NAME_MAPPING[config['english']] = full_name
    PROVINCE_NAME_MAPPING[config['short_name']] = full_name
    PROVINCE_NAME_MAPPING[config['map_name']] = full_name
    
    # 关键词映射
    for keyword in config['keywords']:
        PROVINCE_KEYWORD_MAPPING[keyword.lower()] = full_name

# 获取所有省份列表
def get_all_provinces():
    """获取所有省份列表"""
    return list(PROVINCES_CONFIG.keys())

def get_province_by_keyword(keyword):
    """根据关键词获取省份名称"""
    return PROVINCE_KEYWORD_MAPPING.get(keyword.lower())

def get_province_config(province_name):
    """获取省份配置"""
    return PROVINCES_CONFIG.get(province_name)

def get_province_bounds(province_name):
    """获取省份边界"""
    config = PROVINCES_CONFIG.get(province_name)
    return config['bounds'] if config else None

def get_province_center(province_name):
    """获取省份中心点"""
    config = PROVINCES_CONFIG.get(province_name)
    return config['center'] if config else None
