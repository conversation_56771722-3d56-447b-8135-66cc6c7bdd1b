"""
系统监控工具
用于收集系统指标和记录日志
"""
import psutil
import os
import json
from datetime import datetime, timedelta, timezone
from flask import request, current_app
from flask_login import current_user
from app.extensions import db
from app.models import SystemLog, SystemMetrics, AnalysisHistory, Dataset, User


def get_china_time():
    """获取中国本地时间（UTC+8）"""
    return datetime.now(timezone(timedelta(hours=8)))


class SystemMonitor:
    """系统监控类"""
    
    @staticmethod
    def get_system_metrics():
        """获取当前系统指标"""
        try:
            # CPU使用率
            cpu_usage = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_usage = (disk.used / disk.total) * 100
            
            # 活跃用户数（模拟）
            active_users = User.query.filter(
                User.last_login >= get_china_time() - timedelta(hours=24)
            ).count()
            
            # 数据集总数
            total_datasets = Dataset.query.count()
            
            # 存储使用量
            storage_used = db.session.query(db.func.sum(Dataset.file_size)).scalar() or 0
            
            return {
                'cpu_usage': round(cpu_usage, 2),
                'memory_usage': round(memory_usage, 2),
                'disk_usage': round(disk_usage, 2),
                'active_users': active_users,
                'total_datasets': total_datasets,
                'storage_used': storage_used,
                'memory_total': memory.total,
                'memory_available': memory.available,
                'disk_total': disk.total,
                'disk_free': disk.free
            }
        except Exception as e:
            print(f"获取系统指标失败: {e}")
            return None
    
    @staticmethod
    def record_metrics():
        """记录系统指标到数据库"""
        try:
            metrics_data = SystemMonitor.get_system_metrics()
            if metrics_data:
                metrics = SystemMetrics(
                    cpu_usage=metrics_data['cpu_usage'],
                    memory_usage=metrics_data['memory_usage'],
                    disk_usage=metrics_data['disk_usage'],
                    active_users=metrics_data['active_users'],
                    total_datasets=metrics_data['total_datasets'],
                    storage_used=metrics_data['storage_used']
                )
                db.session.add(metrics)
                db.session.commit()
                return True
        except Exception as e:
            print(f"记录系统指标失败: {e}")
            db.session.rollback()
        return False
    
    @staticmethod
    def get_metrics_history(hours=24):
        """获取指定时间内的系统指标历史"""
        try:
            start_time = get_china_time() - timedelta(hours=hours)
            metrics = SystemMetrics.query.filter(
                SystemMetrics.created_at >= start_time
            ).order_by(SystemMetrics.created_at.asc()).all()
            
            return [metric.to_dict() for metric in metrics]
        except Exception as e:
            print(f"获取指标历史失败: {e}")
            return []
    
    @staticmethod
    def log_action(action, target=None, details=None):
        """记录用户操作日志"""
        try:
            # 安全地获取用户信息
            user_id = None
            ip_address = None
            user_agent = None

            try:
                if current_user and current_user.is_authenticated:
                    user_id = current_user.id
            except:
                pass

            try:
                if request:
                    ip_address = request.remote_addr
                    user_agent = request.headers.get('User-Agent')
            except:
                pass
            
            log = SystemLog(
                user_id=user_id,
                action=action,
                target=target,
                details=details,
                ip_address=ip_address,
                user_agent=user_agent
            )
            db.session.add(log)
            db.session.commit()
            return True
        except Exception as e:
            print(f"记录操作日志失败: {e}")
            db.session.rollback()
        return False
    
    @staticmethod
    def get_recent_logs(limit=50):
        """获取最近的操作日志"""
        try:
            # 使用joinedload预加载用户关系，避免N+1查询问题
            from sqlalchemy.orm import joinedload
            logs = SystemLog.query.options(
                joinedload(SystemLog.user)
            ).order_by(
                SystemLog.created_at.desc()
            ).limit(limit).all()

            return [log.to_dict() for log in logs]
        except Exception as e:
            print(f"获取操作日志失败: {e}")
            return []
    
    @staticmethod
    def record_analysis(analysis_type, parameters, input_datasets, results, execution_time, status='completed'):
        """记录分析历史"""
        try:
            # 安全地获取用户ID
            user_id = None
            try:
                if current_user and current_user.is_authenticated:
                    user_id = current_user.id
            except:
                # 如果没有用户上下文，使用第一个用户
                from app.models import User
                first_user = User.query.first()
                if first_user:
                    user_id = first_user.id
            
            if user_id:
                analysis = AnalysisHistory(
                    user_id=user_id,
                    analysis_type=analysis_type,
                    parameters=json.dumps(parameters, ensure_ascii=False),
                    input_datasets=json.dumps(input_datasets, ensure_ascii=False),
                    results=json.dumps(results, ensure_ascii=False),
                    status=status,
                    execution_time=execution_time
                )
                db.session.add(analysis)
                db.session.commit()

                # 同时记录操作日志
                SystemMonitor.log_action(
                    action=f'进行了{get_analysis_name(analysis_type)}',
                    target=f"分析ID: {analysis.id}",
                    details=f"执行时间: {execution_time:.2f}秒"
                )

                return analysis.id
            else:
                print("无法获取用户ID，跳过分析历史记录")
                return None
        except Exception as e:
            print(f"记录分析历史失败: {e}")
            db.session.rollback()
        return None
    
    @staticmethod
    def get_analysis_history(user_id=None, limit=50):
        """获取分析历史记录"""
        try:
            query = AnalysisHistory.query
            
            if user_id:
                query = query.filter(AnalysisHistory.user_id == user_id)
            
            analyses = query.order_by(
                AnalysisHistory.created_at.desc()
            ).limit(limit).all()
            
            return [analysis.to_dict() for analysis in analyses]
        except Exception as e:
            print(f"获取分析历史失败: {e}")
            return []
    
    @staticmethod
    def cleanup_old_data(days=30):
        """清理旧数据"""
        try:
            cutoff_date = get_china_time() - timedelta(days=days)
            
            # 清理旧的系统指标
            old_metrics = SystemMetrics.query.filter(
                SystemMetrics.created_at < cutoff_date
            ).delete()
            
            # 清理旧的日志（保留重要操作）
            important_actions = ['删除了数据集', '清理了无效数据', '系统维护']
            old_logs = SystemLog.query.filter(
                SystemLog.created_at < cutoff_date,
                ~SystemLog.action.in_(important_actions)
            ).delete()
            
            db.session.commit()
            
            return {
                'deleted_metrics': old_metrics,
                'deleted_logs': old_logs
            }
        except Exception as e:
            print(f"清理旧数据失败: {e}")
            db.session.rollback()
            return None


def get_analysis_name(analysis_type):
    """获取分析类型的中文名称"""
    names = {
        'buffer': '缓冲区分析',
        'clip': '裁剪分析',
        'intersect': '相交分析',
        'statistics': '统计分析'
    }
    return names.get(analysis_type, analysis_type)


def format_file_size(bytes_size):
    """格式化文件大小"""
    if bytes_size == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while bytes_size >= 1024 and i < len(size_names) - 1:
        bytes_size /= 1024.0
        i += 1
    
    return f"{bytes_size:.2f} {size_names[i]}"


def get_time_ago(dt):
    """获取相对时间"""
    if not dt:
        return "未知"

    # 将UTC时间转换为中国本地时间（UTC+8）
    from datetime import timezone, timedelta
    china_tz = timezone(timedelta(hours=8))

    # 如果dt是naive datetime，假设它是UTC时间
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)

    # 转换为中国时间
    dt_china = dt.astimezone(china_tz)
    now_china = datetime.now(china_tz)

    diff = now_china - dt_china

    if diff.days > 0:
        return f"{diff.days}天前"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours}小时前"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"{minutes}分钟前"
    else:
        return "刚刚"
