"""
自动图层服务
用户上传数据后自动处理并添加到对应图层
"""
import os
import logging
from typing import Dict, List, Optional
from datetime import datetime
from flask import current_app
from app.models import GISLayer, db
from app.services.gis_data_service import GISDataService

logger = logging.getLogger(__name__)

class AutoLayerService:
    """自动图层处理服务"""
    
    @classmethod
    def process_uploaded_files(cls, file_group: List[Dict], upload_dir: str, user_id: int) -> Dict:
        """
        处理上传的文件组，自动分类并添加到对应图层
        
        Args:
            file_group: 文件组信息
            upload_dir: 上传目录
            user_id: 用户ID
            
        Returns:
            处理结果字典
        """
        result = {
            'success': False,
            'processed_layers': [],
            'errors': [],
            'message': ''
        }
        
        try:
            # 分组处理文件
            shapefile_groups = cls._group_shapefiles(file_group)
            raster_files = cls._get_raster_files(file_group)
            
            # 处理Shapefile组
            for shp_group in shapefile_groups:
                try:
                    layer = GISDataService.process_shapefile(shp_group, upload_dir, user_id)
                    if layer:
                        # 自动分类和省份识别已在GISDataService中完成
                        db.session.add(layer)
                        db.session.commit()

                        # 如果是土地利用数据，自动运行数据导入脚本
                        if cls._is_landuse_file(shp_group[0]['name']):
                            cls._auto_process_landuse_data(layer)
                        # 如果是行政区划数据，自动处理
                        elif cls._is_admin_file(shp_group[0]['name']):
                            cls._auto_process_admin_data(layer)
                        
                        result['processed_layers'].append({
                            'id': layer.id,
                            'name': layer.name,
                            'type': layer.layer_type,
                            'category': layer.data_category,
                            'province': layer.province
                        })
                        
                        logger.info(f"成功处理Shapefile: {layer.name}")
                    else:
                        result['errors'].append(f"处理Shapefile失败: {shp_group[0]['name']}")
                        
                except Exception as e:
                    error_msg = f"处理Shapefile出错: {e}"
                    result['errors'].append(error_msg)
                    logger.error(error_msg)
                    db.session.rollback()
            
            # 处理栅格文件
            for raster_file in raster_files:
                try:
                    layer = GISDataService.process_raster_file(raster_file, upload_dir, user_id)
                    if layer:
                        # 自动分类和省份识别已在GISDataService中完成
                        db.session.add(layer)
                        db.session.commit()

                        # 如果是土地利用数据，自动运行数据导入脚本
                        if cls._is_landuse_file(raster_file['name']):
                            cls._auto_process_landuse_data(layer)

                        result['processed_layers'].append({
                            'id': layer.id,
                            'name': layer.name,
                            'type': layer.layer_type,
                            'category': layer.data_category,
                            'province': layer.province
                        })
                        
                        logger.info(f"成功处理栅格文件: {layer.name}")
                    else:
                        result['errors'].append(f"处理栅格文件失败: {raster_file['name']}")
                        
                except Exception as e:
                    error_msg = f"处理栅格文件出错: {e}"
                    result['errors'].append(error_msg)
                    logger.error(error_msg)
                    db.session.rollback()
            
            # 设置结果状态
            if result['processed_layers']:
                result['success'] = True
                layer_count = len(result['processed_layers'])
                result['message'] = f"成功处理 {layer_count} 个图层"
                
                # 按类别统计
                categories = {}
                provinces = set()
                for layer in result['processed_layers']:
                    category = layer['category']
                    province = layer['province']
                    
                    if category not in categories:
                        categories[category] = 0
                    categories[category] += 1
                    
                    if province:
                        provinces.add(province)
                
                # 生成详细消息
                category_msgs = []
                for category, count in categories.items():
                    category_name = cls._get_category_display_name(category)
                    category_msgs.append(f"{category_name}({count}个)")
                
                if category_msgs:
                    result['message'] += f": {', '.join(category_msgs)}"
                
                if provinces:
                    result['message'] += f"，涉及省份: {', '.join(provinces)}"

                # 自动运行完整的数据导入脚本
                cls._trigger_full_data_import()

            else:
                result['message'] = "没有成功处理任何文件"
                if result['errors']:
                    result['message'] += f"，错误: {'; '.join(result['errors'])}"

            return result
            
        except Exception as e:
            error_msg = f"自动处理文件失败: {e}"
            logger.error(error_msg)
            result['errors'].append(error_msg)
            result['message'] = error_msg
            return result
    
    @classmethod
    def _group_shapefiles(cls, file_group: List[Dict]) -> List[List[Dict]]:
        """将Shapefile相关文件分组"""
        shapefile_groups = []
        processed_bases = set()
        
        for file_info in file_group:
            filename = file_info['name']
            if filename.lower().endswith('.shp'):
                base_name = os.path.splitext(filename)[0]
                
                if base_name not in processed_bases:
                    # 查找相关文件
                    group = []
                    for f in file_group:
                        if f['name'].startswith(base_name + '.'):
                            group.append(f)
                    
                    if group:
                        shapefile_groups.append(group)
                        processed_bases.add(base_name)
        
        return shapefile_groups
    
    @classmethod
    def _get_raster_files(cls, file_group: List[Dict]) -> List[Dict]:
        """获取栅格文件"""
        raster_files = []
        
        for file_info in file_group:
            filename = file_info['name'].lower()
            if filename.endswith(('.tif', '.tiff', '.geotiff')):
                raster_files.append(file_info)
        
        return raster_files
    
    @classmethod
    def _get_category_display_name(cls, category: str) -> str:
        """获取数据类别的显示名称"""
        category_names = {
            'landuse': '土地利用',
            'administrative': '行政区划',
            'elevation': '高程数据',
            'risk': '风险数据',
            'other': '其他数据'
        }
        return category_names.get(category, category)
    
    @classmethod
    def get_available_layers_by_province(cls, province: str) -> Dict:
        """
        获取指定省份的可用图层
        
        Args:
            province: 省份名称
            
        Returns:
            按类别分组的图层信息
        """
        try:
            layers = GISLayer.query.filter(
                GISLayer.province == province,
                GISLayer.processing_status == 'processed'
            ).all()
            
            result = {
                'landuse': [],
                'administrative': [],
                'elevation': [],
                'risk': [],
                'other': []
            }
            
            for layer in layers:
                category = layer.data_category or 'other'
                if category not in result:
                    result[category] = []
                
                layer_info = {
                    'id': layer.id,
                    'name': layer.name,
                    'display_name': layer.display_name,
                    'description': layer.description,
                    'layer_type': layer.layer_type,
                    'file_path': layer.file_path,
                    'bounds': layer.get_bounds_list(),
                    'created_at': layer.created_at.isoformat() if layer.created_at else None
                }
                
                result[category].append(layer_info)
            
            return result
            
        except Exception as e:
            logger.error(f"获取省份图层失败: {e}")
            return {
                'landuse': [],
                'administrative': [],
                'elevation': [],
                'risk': [],
                'other': []
            }
    
    @classmethod
    def get_provinces_with_data(cls) -> List[str]:
        """获取有数据的省份列表"""
        try:
            provinces = db.session.query(GISLayer.province).filter(
                GISLayer.province.isnot(None),
                GISLayer.processing_status == 'processed'
            ).distinct().all()
            
            return [p[0] for p in provinces if p[0]]

        except Exception as e:
            logger.error(f"获取省份列表失败: {e}")
            return []

    @classmethod
    def _is_landuse_file(cls, filename: str) -> bool:
        """检测是否为土地利用数据文件"""
        filename_lower = filename.lower()

        # 土地利用数据文件的关键词
        landuse_keywords = [
            'clcd',           # 中国土地覆盖数据集
            'landuse',        # 土地利用
            'landcover',      # 土地覆盖
            'lulc',          # Land Use Land Cover
            'land_use',      # 土地利用（下划线）
            'land_cover',    # 土地覆盖（下划线）
            '土地利用',       # 中文
            '土地覆盖',       # 中文
            'lucc'           # Land Use and Cover Change
        ]

        # 检查文件名是否包含土地利用关键词
        for keyword in landuse_keywords:
            if keyword in filename_lower:
                return True

        return False

    @classmethod
    def _is_admin_file(cls, filename: str) -> bool:
        """检测是否为行政区划数据文件"""
        filename_lower = filename.lower()

        # 行政区划数据文件的关键词
        admin_keywords = [
            'admin',          # 行政区划
            'boundary',       # 边界
            'district',       # 区域
            'region',         # 区域
            'province',       # 省份
            'city',           # 城市
            'county',         # 县
            'administrative', # 行政的
            '行政区划',       # 中文
            '区划',           # 中文
            '边界',           # 中文
            '行政边界',       # 中文
            '省界',           # 中文
            '市界',           # 中文
            '县界'            # 中文
        ]

        # 检查文件名是否包含行政区划关键词
        for keyword in admin_keywords:
            if keyword in filename_lower:
                return True

        return False

    @classmethod
    def _auto_process_landuse_data(cls, layer: 'GISLayer'):
        """自动处理土地利用数据"""
        try:
            logger.info(f"开始自动处理土地利用数据: {layer.name}")

            # 确保数据分类正确
            if layer.data_category != 'landuse':
                layer.data_category = 'landuse'
                logger.info(f"已将 {layer.name} 分类为土地利用数据")

            # 根据文件名推断省份（如果还没有设置）
            if not layer.province:
                province = cls._extract_province_from_filename(layer.original_filename or layer.name)
                if province:
                    layer.province = province
                    logger.info(f"为 {layer.name} 设置省份: {province}")

            # 更新处理状态
            layer.processing_status = 'processed'

            # 添加处理日志
            processing_log = f"自动处理土地利用数据 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            if layer.processing_log:
                layer.processing_log += f"\n{processing_log}"
            else:
                layer.processing_log = processing_log

            # 保存更改
            db.session.commit()

            logger.info(f"土地利用数据自动处理完成: {layer.name}")

        except Exception as e:
            logger.error(f"自动处理土地利用数据失败: {e}")
            db.session.rollback()

    @classmethod
    def _auto_process_admin_data(cls, layer: 'GISLayer'):
        """自动处理行政区划数据"""
        try:
            logger.info(f"开始自动处理行政区划数据: {layer.name}")

            # 确保数据分类正确
            if layer.data_category != 'administrative':
                layer.data_category = 'administrative'
                logger.info(f"已将 {layer.name} 分类为行政区划数据")

            # 根据文件名推断省份（如果还没有设置）
            if not layer.province:
                province = cls._extract_province_from_filename(layer.original_filename or layer.name)
                if province:
                    layer.province = province
                    logger.info(f"为 {layer.name} 设置省份: {province}")

            # 更新处理状态
            layer.processing_status = 'processed'

            # 添加处理日志
            processing_log = f"自动处理行政区划数据 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            if layer.processing_log:
                layer.processing_log += f"\n{processing_log}"
            else:
                layer.processing_log = processing_log

            # 保存更改
            db.session.commit()

            logger.info(f"行政区划数据自动处理完成: {layer.name}")

        except Exception as e:
            logger.error(f"自动处理行政区划数据失败: {e}")
            db.session.rollback()

    @classmethod
    def _extract_province_from_filename(cls, filename: str) -> Optional[str]:
        """从文件名中提取省份信息"""
        if not filename:
            return None

        filename_lower = filename.lower()

        # 省份映射表
        province_mapping = {
            'henan': '河南省',
            'hubei': '湖北省',
            'fujian': '福建省',
            'guangdong': '广东省',
            'jiangsu': '江苏省',
            'zhejiang': '浙江省',
            'shandong': '山东省',
            'sichuan': '四川省',
            'anhui': '安徽省',
            'hunan': '湖南省',
            'jiangxi': '江西省',
            'shanxi': '山西省',
            'shaanxi': '陕西省',
            'gansu': '甘肃省',
            'qinghai': '青海省',
            'xinjiang': '新疆维吾尔自治区',
            'tibet': '西藏自治区',
            'guangxi': '广西壮族自治区',
            'ningxia': '宁夏回族自治区',
            'neimenggu': '内蒙古自治区',
            'beijing': '北京市',
            'tianjin': '天津市',
            'shanghai': '上海市',
            'chongqing': '重庆市',
            'hainan': '海南省',
            'yunnan': '云南省',
            'guizhou': '贵州省',
            'liaoning': '辽宁省',
            'jilin': '吉林省',
            'heilongjiang': '黑龙江省',
            'hebei': '河北省',
            'taiwan': '台湾省',
            'hongkong': '香港特别行政区',
            'macau': '澳门特别行政区'
        }

        # 检查英文省份名
        for eng_name, cn_name in province_mapping.items():
            if eng_name in filename_lower:
                return cn_name

        # 检查中文省份名
        for cn_name in province_mapping.values():
            province_short = cn_name.replace('省', '').replace('市', '').replace('自治区', '').replace('特别行政区', '')
            if province_short in filename:
                return cn_name

        return None

    @classmethod
    def _trigger_full_data_import(cls):
        """触发完整的数据导入脚本"""
        try:
            logger.info("触发完整数据导入脚本...")

            # 运行土地利用数据导入脚本
            cls._run_landuse_import_script()

            # 运行行政区划数据导入脚本
            cls._run_admin_import_script()

            logger.info("完整数据导入脚本执行完成")

        except Exception as e:
            logger.error(f"执行完整数据导入脚本失败: {e}")

    @classmethod
    def _run_landuse_import_script(cls):
        """运行土地利用数据导入脚本"""
        try:
            logger.info("开始运行土地利用数据导入脚本...")

            # 获取上传目录
            upload_base = os.path.join(current_app.root_path, '..', 'data', 'gis_layers')

            # 扫描所有土地利用文件
            processed_count = 0
            for root, dirs, files in os.walk(upload_base):
                for file in files:
                    if cls._is_landuse_file(file):
                        file_path = os.path.join(root, file)

                        # 检查是否已经在数据库中
                        existing_layer = GISLayer.query.filter(GISLayer.file_path == file_path).first()
                        if existing_layer:
                            continue

                        # 处理文件
                        try:
                            file_info = {
                                'name': file,
                                'path': file_path,
                                'size': os.path.getsize(file_path)
                            }

                            # 根据文件类型处理
                            layer = None
                            if file.lower().endswith(('.tif', '.tiff')):
                                layer = GISDataService.process_raster_file(file_info, root, 1)
                            elif file.lower().endswith('.shp'):
                                # 对于Shapefile，需要找到相关文件
                                file_group = cls._find_shapefile_group(file_path)
                                if file_group:
                                    layer = GISDataService.process_shapefile(file_group, root, 1)

                            if layer:
                                # 设置为土地利用数据
                                layer.data_category = 'landuse'

                                # 根据文件名推断省份
                                province = cls._extract_province_from_filename(file)
                                if province:
                                    layer.province = province

                                # 保存到数据库
                                db.session.add(layer)
                                db.session.commit()

                                processed_count += 1
                                logger.info(f"成功处理土地利用文件: {layer.name}")

                        except Exception as e:
                            logger.error(f"处理土地利用文件失败 {file}: {e}")
                            db.session.rollback()

            logger.info(f"土地利用数据导入完成，处理了 {processed_count} 个文件")

        except Exception as e:
            logger.error(f"运行土地利用数据导入脚本失败: {e}")

    @classmethod
    def _run_admin_import_script(cls):
        """运行行政区划数据导入脚本"""
        try:
            logger.info("开始运行行政区划数据导入脚本...")

            # 获取上传目录
            upload_base = os.path.join(current_app.root_path, '..', 'data', 'gis_layers')

            # 扫描所有行政区划文件
            processed_count = 0
            for root, dirs, files in os.walk(upload_base):
                for file in files:
                    if cls._is_admin_file(file):
                        file_path = os.path.join(root, file)

                        # 检查是否已经在数据库中
                        existing_layer = GISLayer.query.filter(GISLayer.file_path == file_path).first()
                        if existing_layer:
                            continue

                        # 处理文件
                        try:
                            file_info = {
                                'name': file,
                                'path': file_path,
                                'size': os.path.getsize(file_path)
                            }

                            # 根据文件类型处理
                            layer = None
                            if file.lower().endswith(('.tif', '.tiff')):
                                layer = GISDataService.process_raster_file(file_info, root, 1)
                            elif file.lower().endswith('.shp'):
                                # 对于Shapefile，需要找到相关文件
                                file_group = cls._find_shapefile_group(file_path)
                                if file_group:
                                    layer = GISDataService.process_shapefile(file_group, root, 1)

                            if layer:
                                # 设置为行政区划数据
                                layer.data_category = 'administrative'

                                # 根据文件名推断省份
                                province = cls._extract_province_from_filename(file)
                                if province:
                                    layer.province = province

                                # 保存到数据库
                                db.session.add(layer)
                                db.session.commit()

                                processed_count += 1
                                logger.info(f"成功处理行政区划文件: {layer.name}")

                        except Exception as e:
                            logger.error(f"处理行政区划文件失败 {file}: {e}")
                            db.session.rollback()

            logger.info(f"行政区划数据导入完成，处理了 {processed_count} 个文件")

        except Exception as e:
            logger.error(f"运行行政区划数据导入脚本失败: {e}")

    @classmethod
    def _find_shapefile_group(cls, shp_path: str) -> Optional[List[Dict]]:
        """查找Shapefile相关文件组"""
        try:
            base_path = os.path.splitext(shp_path)[0]
            extensions = ['.shp', '.shx', '.dbf', '.prj', '.cpg']

            file_group = []
            for ext in extensions:
                file_path = base_path + ext
                if os.path.exists(file_path):
                    file_group.append({
                        'name': os.path.basename(file_path),
                        'path': file_path,
                        'size': os.path.getsize(file_path)
                    })

            return file_group if file_group else None

        except Exception as e:
            logger.error(f"查找Shapefile文件组失败: {e}")
            return None
