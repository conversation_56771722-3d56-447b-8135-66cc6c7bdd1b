{% extends "base.html" %}

{% block title %}系统仪表板 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-tachometer-alt me-2"></i>系统仪表板
                    </h2>
                    <p class="text-muted mb-0">欢迎回来，{{ current_user.username }}！</p>
                </div>
                <div>
                    <span class="badge bg-success">系统正常运行</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 核心功能模块管理 -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cogs me-2"></i>核心功能模块管理
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- 数据管理模块 -->
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-primary">
                                <div class="card-body py-3">
                                    <h6 class="text-primary mb-2">
                                        <i class="fas fa-database me-1"></i>数据管理模块
                                    </h6>
                                    <p class="text-muted small mb-3">数据导入、处理和存储管理</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('main.data_import') }}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-upload me-1"></i>数据导入
                                        </a>
                                        <a href="{{ url_for('main.data_processing') }}" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-cogs me-1"></i>数据处理
                                        </a>
                                        <a href="{{ url_for('main.data_storage') }}" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-hdd me-1"></i>数据存储
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- 可视化模块 -->
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-info">
                                <div class="card-body py-3">
                                    <h6 class="text-info mb-2">
                                        <i class="fas fa-chart-line me-1"></i>可视化模块
                                    </h6>
                                    <p class="text-muted small mb-3">地图展示、图表分析和图层管理</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('visualization.map_view') }}" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-map me-1"></i>地图展示
                                        </a>
                                        <a href="{{ url_for('visualization.charts_view') }}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-chart-bar me-1"></i>图表分析
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 风险评估模块 -->
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-warning">
                                <div class="card-body py-3">
                                    <h6 class="text-warning mb-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i>风险评估模块
                                    </h6>
                                    <p class="text-muted small mb-3">风险等级划分和评估管理</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('risk_assessment.index') }}" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-layer-group me-1"></i>风险评估首页
                                        </a>
                                        <a href="{{ url_for('risk_assessment.projects') }}" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-project-diagram me-1"></i>项目管理
                                        </a>
                                        <a href="{{ url_for('risk_assessment.weight_config') }}" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-sliders-h me-1"></i>权重配置
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 管理员个人信息与系统统计 -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-shield me-2"></i>管理员信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="mb-3">
                            <i class="fas fa-user-shield fa-4x text-danger"></i>
                        </div>
                        <h5>{{ current_user.username }}</h5>
                        <p class="text-muted">{{ current_user.email }}</p>
                        <span class="badge bg-danger">系统管理员</span>
                        <p class="text-muted mt-2">
                            <small>注册时间: {{ current_user.created_at.strftime('%Y-%m-%d') }}</small>
                        </p>
                    </div>

                    <!-- 系统统计 -->
                    <div class="mb-3">
                        <h6 class="text-muted mb-3">系统统计</h6>
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="text-primary mb-0" id="total-users-count">0</h6>
                                    <small class="text-muted">总用户</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="text-success mb-0" id="online-users-count">0</h6>
                                    <small class="text-muted">在线用户</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h6 class="text-info mb-0" id="total-datasets-count">0</h6>
                                <small class="text-muted">数据集</small>
                            </div>
                        </div>
                    </div>

                    <!-- 编辑资料 -->
                    <div class="d-grid">
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                            <i class="fas fa-edit me-1"></i>编辑资料
                        </button>
                    </div>
                </div>
            </div>


        </div>
    </div>



<!-- 编辑资料模态框 -->
<div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editProfileModalLabel">
                    <i class="fas fa-user-edit me-2"></i>编辑个人资料
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editProfileForm">
                <div class="modal-body">
                    <div class="row">
                        <!-- 基本信息 -->
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">基本信息</h6>

                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" name="username"
                                       value="{{ current_user.username }}" required>
                                <div class="form-text">用户名用于登录系统</div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱地址</label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="{{ current_user.email }}" required>
                                <div class="form-text">用于接收系统通知</div>
                            </div>
                        </div>

                        <!-- 安全设置 -->
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">安全设置</h6>

                            <div class="mb-3">
                                <label for="current_password" class="form-label">当前密码</label>
                                <input type="password" class="form-control" id="current_password" name="current_password">
                                <div class="form-text">修改密码时需要输入当前密码</div>
                            </div>

                            <div class="mb-3">
                                <label for="new_password" class="form-label">新密码</label>
                                <input type="password" class="form-control" id="new_password" name="new_password">
                                <div class="form-text">留空则不修改密码</div>
                            </div>

                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">确认新密码</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>保存更改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-xs {
    font-size: 0.7rem;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.border-left-secondary {
    border-left: 0.25rem solid #6c757d !important;
}

.border-left-dark {
    border-left: 0.25rem solid #343a40 !important;
}

/* 时间线样式 */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 5px;
    font-size: 13px;
    color: #6c757d;
}
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // 更新在线用户统计
    function updateOnlineUsers() {
        fetch('/api/online-count')
            .then(response => response.json())
            .then(data => {
                $('#online-users-count').text(data.total);
                console.log('在线用户更新:', data);
            })
            .catch(error => {
                console.error('更新在线用户数失败:', error);
            });
    }

    // 更新系统统计数据
    function updateSystemStats() {
        // 更新总用户数
        fetch('/api/admin/users/stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    $('#total-users-count').text(data.data.total_users);
                }
            })
            .catch(error => {
                console.error('更新用户统计失败:', error);
            });

        // 更新数据集数量
        fetch('/api/datasets')
            .then(response => response.json())
            .then(data => {
                $('#total-datasets-count').text(data.length || 0);
            })
            .catch(error => {
                console.error('更新数据集统计失败:', error);
            });
    }

    // 每10秒更新一次在线用户数
    setInterval(updateOnlineUsers, 10000);

    // 页面加载时立即更新统计数据
    updateOnlineUsers();
    updateSystemStats();

    // 处理编辑资料表单提交
    $('#editProfileForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        // 验证密码
        if (data.new_password && data.new_password !== data.confirm_password) {
            showAlert('新密码和确认密码不匹配！', 'danger');
            return;
        }

        // 如果要修改密码，必须输入当前密码
        if (data.new_password && !data.current_password) {
            showAlert('修改密码时必须输入当前密码！', 'danger');
            return;
        }

        // 显示加载状态
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>保存中...').prop('disabled', true);

        // 发送更新请求
        fetch('/api/profile/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showAlert('个人资料更新成功！', 'success');
                $('#editProfileModal').modal('hide');

                // 更新页面显示的用户信息
                if (data.username !== '{{ current_user.username }}') {
                    location.reload(); // 如果用户名改变，刷新页面
                }
            } else {
                showAlert(result.message || '更新失败，请重试！', 'danger');
            }
        })
        .catch(error => {
            console.error('更新个人资料失败:', error);
            showAlert('网络错误，请重试！', 'danger');
        })
        .finally(() => {
            // 恢复按钮状态
            submitBtn.html(originalText).prop('disabled', false);
        });
    });

    // 密码确认验证
    $('#confirm_password').on('input', function() {
        const newPassword = $('#new_password').val();
        const confirmPassword = $(this).val();

        if (confirmPassword && newPassword !== confirmPassword) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
            $(this).after('<div class="invalid-feedback">密码不匹配</div>');
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });

    // 显示提示信息的函数
    function showAlert(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // 移除现有的提示
        $('.alert').remove();

        // 在页面顶部添加新提示
        $('body').prepend(alertHtml);

        // 3秒后自动消失
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 3000);
    }

    // 显示即将推出功能的提示
    function showComingSoon(featureName) {
        showAlert(`${featureName}功能即将推出，敬请期待！`, 'info');
    }

    // 快速可视化预览功能
    let quickCharts = {};
    
    // 绑定城市选择器变化事件
    $('#quickCitySelector').on('change', function() {
        const cityId = $(this).val();
        if (cityId) {
            loadQuickVisualization(cityId);
        } else {
            clearQuickVisualization();
        }
    });
    
    function loadQuickVisualization(cityId) {
        // 加载快速统计数据
        loadQuickStats(cityId);
        
        // 加载快速图表
        loadQuickCharts(cityId);
    }
    
    function loadQuickStats(cityId) {
        // 从真实数据API加载统计数据
        generateQuickStats(cityId).then(stats => {
            $('#quickFloodPoints').text(stats.floodPoints);
            $('#quickHighRisk').text(stats.highRisk);
            $('#quickFloodArea').text(stats.floodArea + ' km²');
            $('#quickAffectedPop').text(stats.affectedPop.toLocaleString() + ' 人');
        }).catch(error => {
            console.error('加载快速统计数据失败:', error);
            // 显示错误状态
            $('#quickFloodPoints').text('--');
            $('#quickHighRisk').text('--');
            $('#quickFloodArea').text('-- km²');
            $('#quickAffectedPop').text('-- 人');
        });
    }
    
    function loadQuickCharts(cityId) {
        // 加载降雨量趋势图
        createQuickRainfallChart(cityId);
        
        // 加载风险分布图
        createQuickRiskChart(cityId);
    }
    
    function generateQuickStats(cityId) {
        // 从真实数据API获取统计数据
        return fetch(`/api/city-stats/${cityId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    return data.stats;
                } else {
                    // 如果API失败，返回空数据
                    return {
                        floodPoints: 0,
                        highRisk: 0,
                        floodArea: 0,
                        affectedPop: 0
                    };
                }
            })
            .catch(error => {
                console.error('获取城市统计数据失败:', error);
                return {
                    floodPoints: 0,
                    highRisk: 0,
                    floodArea: 0,
                    affectedPop: 0
                };
            });
    }
    
    function createQuickRainfallChart(cityId) {
        const ctx = document.getElementById('quickRainfallChart').getContext('2d');
        
        if (quickCharts.rainfall) {
            quickCharts.rainfall.destroy();
        }
        
        // 生成最近7天的模拟降雨数据
        const dates = [];
        const rainfall = [];
        
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            dates.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
            rainfall.push(Math.random() * 20 + 2);
        }
        
        // 创建渐变色以提高视觉效果
        const gradient = ctx.createLinearGradient(0, 0, 0, 150);
        gradient.addColorStop(0, 'rgba(54, 162, 235, 0.4)');
        gradient.addColorStop(1, 'rgba(54, 162, 235, 0.1)');
        
        quickCharts.rainfall = new Chart(ctx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [{
                    label: '降雨量 (mm)',
                    data: rainfall,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: gradient,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 4,
                    pointBackgroundColor: 'rgb(54, 162, 235)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    x: {
                        grid: { display: false },
                        ticks: { font: { size: 10 } }
                    },
                    y: {
                        beginAtZero: true,
                        grid: { color: 'rgba(0,0,0,0.1)' },
                        ticks: { font: { size: 10 } }
                    }
                }
            }
        });
    }
    
    function createQuickRiskChart(cityId) {
        const ctx = document.getElementById('quickRiskChart').getContext('2d');
        
        if (quickCharts.risk) {
            quickCharts.risk.destroy();
        }
        
        // 生成风险等级分布数据
        const riskData = [
            Math.floor(Math.random() * 30) + 10, // 高风险
            Math.floor(Math.random() * 40) + 20, // 中风险
            Math.floor(Math.random() * 50) + 30  // 低风险
        ];
        
        quickCharts.risk = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['高风险', '中风险', '低风险'],
                datasets: [{
                    data: riskData,
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)'
                    ],
                    borderColor: [
                        'rgb(255, 99, 132)',
                        'rgb(255, 206, 86)',
                        'rgb(75, 192, 192)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: { size: 10 },
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }
    
    function clearQuickVisualization() {
        $('#quickFloodPoints, #quickHighRisk, #quickFloodArea, #quickAffectedPop').text('--');
        
        Object.values(quickCharts).forEach(chart => {
            if (chart) chart.destroy();
        });
        quickCharts = {};
    }

});
</script>
{% endblock %}
