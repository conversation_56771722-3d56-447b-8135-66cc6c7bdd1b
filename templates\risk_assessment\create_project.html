{% extends "base.html" %}

{% block title %}新建城市评估 - {{ super() }}{% endblock %}

{% block content %}
<div class="container">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-plus text-success me-2"></i>新建省份内涝风险评估</h2>
                    <p class="text-muted mb-0">创建一个新的省份内涝风险评估来预测和分析省份内各城市风险</p>
                </div>
                <div>
                    <a href="{{ url_for('risk_assessment.projects') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回省份列表
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建表单 -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-edit me-2"></i>省份评估信息</h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="createProjectForm">
                        <div class="mb-3">
                            <label for="project_name" class="form-label">
                                <i class="fas fa-map me-1"></i>省份评估名称 <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="project_name" name="project_name"
                                   placeholder="请输入省份评估名称（如：陕西省内涝风险评估）" required maxlength="100">
                            <div class="form-text">省份评估名称将用于标识和管理您的风险评估</div>
                        </div>

                        <div class="mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">
                                        <i class="fas fa-map-marker-alt me-1"></i>评估省份 <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="province" name="province" required onchange="updateLocation()">
                                        <option value="">请选择省份</option>
                                        <!-- 直辖市 -->
                                        <option value="北京市">北京市</option>
                                        <option value="天津市">天津市</option>
                                        <option value="上海市">上海市</option>
                                        <option value="重庆市">重庆市</option>
                                        <!-- 华北地区 -->
                                        <option value="河北省">河北省</option>
                                        <option value="山西省">山西省</option>
                                        <!-- 东北地区 -->
                                        <option value="辽宁省">辽宁省</option>
                                        <option value="吉林省">吉林省</option>
                                        <option value="黑龙江省">黑龙江省</option>
                                        <!-- 华东地区 -->
                                        <option value="江苏省">江苏省</option>
                                        <option value="浙江省">浙江省</option>
                                        <option value="安徽省">安徽省</option>
                                        <option value="福建省">福建省</option>
                                        <option value="江西省">江西省</option>
                                        <option value="山东省">山东省</option>
                                        <option value="台湾省">台湾省</option>
                                        <!-- 华中地区 -->
                                        <option value="河南省">河南省</option>
                                        <option value="湖北省">湖北省</option>
                                        <option value="湖南省">湖南省</option>
                                        <!-- 华南地区 -->
                                        <option value="广东省">广东省</option>
                                        <option value="海南省">海南省</option>
                                        <!-- 西南地区 -->
                                        <option value="四川省">四川省</option>
                                        <option value="贵州省">贵州省</option>
                                        <option value="云南省">云南省</option>
                                        <!-- 西北地区 -->
                                        <option value="陕西省">陕西省</option>
                                        <option value="甘肃省">甘肃省</option>
                                        <option value="青海省">青海省</option>
                                        <!-- 自治区 -->
                                        <option value="内蒙古自治区">内蒙古自治区</option>
                                        <option value="广西壮族自治区">广西壮族自治区</option>
                                        <option value="西藏自治区">西藏自治区</option>
                                        <option value="宁夏回族自治区">宁夏回族自治区</option>
                                        <option value="新疆维吾尔自治区">新疆维吾尔自治区</option>
                                        <!-- 特别行政区 -->
                                        <option value="香港特别行政区">香港特别行政区</option>
                                        <option value="澳门特别行政区">澳门特别行政区</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">
                                        <i class="fas fa-calendar-alt me-1"></i>评估年份 <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="assessment_year" name="assessment_year" required>
                                        <option value="">请选择年份</option>
                                        <option value="2025">2025年</option>
                                        <option value="2024">2024年</option>
                                        <option value="2023">2023年</option>
                                        <option value="2022">2022年</option>
                                        <option value="2021">2021年</option>
                                        <option value="2020">2020年</option>
                                        <option value="2019">2019年</option>
                                        <option value="2018">2018年</option>
                                        <option value="2017">2017年</option>
                                        <option value="2016">2016年</option>
                                        <option value="2015">2015年</option>
                                        <option value="2014">2014年</option>
                                    </select>
                                </div>
                            </div>
                            <input type="hidden" id="location" name="location" required>
                            <div class="form-text">选择要进行内涝风险评估的省份和对应的评估年份，系统将分析该省份内各城市的风险等级</div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label">
                                <i class="fas fa-align-left me-1"></i>评估描述
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="请输入评估描述，包括评估目的、范围、特殊要求等（如：针对陕西省的内涝风险评估）"></textarea>
                            <div class="form-text">详细描述省份风险评估的背景、目标和要求</div>
                        </div>

                        <div class="mb-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-info-circle text-info me-2"></i>省份评估创建后您可以：</h6>
                                    <ul class="mb-0">
                                        <li>上传省份级别的风险评估数据（包含各城市数据）</li>
                                        <li>批量导入CSV或Excel格式的省份数据文件</li>
                                        <li>配置和调整省份风险指标权重参数</li>
                                        <li>执行省份内各城市内涝风险计算并查看结果</li>
                                        <li>在地图上查看省份内各城市的风险等级分布</li>
                                        <li>导出省份评估结果和风险分析报告</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('risk_assessment.projects') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save me-1"></i>创建省份评估
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助信息 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-question-circle me-2"></i>需要帮助？</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-cloud-rain text-primary me-2"></i>发生概率指标 (P)：</h6>
                            <ul class="small">
                                <li><strong>全年降水量</strong>：年度总降水量 (mm) - 权重63.3%</li>
                                <li><strong>排水管网密度</strong>：单位面积排水管网长度 (km/km²) - 权重26.0%</li>
                                <li><strong>平均高程值</strong>：地面平均海拔高度 (m) - 权重10.7%</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-users text-success me-2"></i>影响程度指标 (I)：</h6>
                            <ul class="small">
                                <li><strong>GDP密度</strong>：单位面积GDP产值 (万元/km²) - 权重60.0%</li>
                                <li><strong>人口密度</strong>：单位面积人口数量 (人/km²) - 权重40.0%</li>
                            </ul>
                            <div class="mt-2 p-2 bg-light rounded">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    <strong>风险指数计算：</strong>RI = P × I
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('createProjectForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    
    // 显示加载状态
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>创建中...';
    submitBtn.disabled = true;
    
    // 表单验证
    const projectName = document.getElementById('project_name').value.trim();
    const location = document.getElementById('location').value.trim();

    if (!projectName) {
        e.preventDefault();
        alert('请输入省份评估名称');

        // 恢复按钮状态
        submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>创建省份评估';
        submitBtn.disabled = false;
        return;
    }

    if (!location) {
        e.preventDefault();
        alert('请选择评估省份');

        // 恢复按钮状态
        submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>创建省份评估';
        submitBtn.disabled = false;
        return;
    }
});

// 实时字符计数
document.getElementById('project_name').addEventListener('input', function() {
    const maxLength = 100;
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;
    
    let formText = this.nextElementSibling;
    if (remaining < 20) {
        formText.innerHTML = `省份评估名称将用于标识和管理您的风险评估 (还可输入${remaining}个字符)`;
        formText.className = remaining < 10 ? 'form-text text-warning' : 'form-text text-info';
    } else {
        formText.innerHTML = '省份评估名称将用于标识和管理您的风险评估';
        formText.className = 'form-text';
    }
});

// 页面加载完成后聚焦到项目名称输入框
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('project_name').focus();
});



// 更新隐藏的location字段
function updateLocation() {
    const province = document.getElementById('province').value;
    const locationInput = document.getElementById('location');

    if (province) {
        locationInput.value = province;
    } else {
        locationInput.value = '';
    }
}
</script>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    transition: all 0.2s;
}

.btn:hover {
    transform: translateY(-1px);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.bg-light {
    background-color: #f8f9fa !important;
}

.text-danger {
    color: #dc3545 !important;
}

.small {
    font-size: 0.875em;
}
</style>
{% endblock %}
