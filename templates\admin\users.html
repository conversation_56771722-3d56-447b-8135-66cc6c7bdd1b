{% extends "base.html" %}

{% block title %}用户管理 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-users-cog me-2"></i>用户管理
                    </h2>
                    <p class="text-muted mb-0">管理系统用户账户和权限</p>
                </div>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-user-plus me-1"></i>添加用户
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                总用户数
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-users">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                在线用户
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="online-users">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                管理员
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="admin-count">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                今日新增
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-new-users">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户列表 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list me-2"></i>用户列表
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="usersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>角色</th>
                            <th>状态</th>
                            <th>注册时间</th>
                            <th>最后登录</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 用户数据将通过JavaScript动态加载 -->
                        <tr>
                            <td colspan="8" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载用户数据...</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">
                    <i class="fas fa-user-plus me-2"></i>添加新用户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">角色</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="">请选择角色</option>
                            <option value="user">普通用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addUser()">
                    <i class="fas fa-save me-1"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">
                    <i class="fas fa-user-edit me-2"></i>编辑用户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId" name="userId">
                    <div class="mb-3">
                        <label for="editUsername" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="editUsername" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="editEmail" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="editPassword" class="form-label">新密码（留空则不修改）</label>
                        <input type="password" class="form-control" id="editPassword" name="password">
                    </div>
                    <div class="mb-3">
                        <label for="editRole" class="form-label">角色</label>
                        <select class="form-select" id="editRole" name="role" required>
                            <option value="">请选择角色</option>
                            <option value="user">普通用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editIsActive" name="is_active">
                            <label class="form-check-label" for="editIsActive">
                                账户激活
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateUser()">
                    <i class="fas fa-save me-1"></i>保存修改
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 初始化页面
    loadUsers();
    loadUserStats();

    // 每5秒更新一次在线用户数
    setInterval(updateOnlineUsers, 5000);

    // 页面加载时立即更新一次在线用户数
    updateOnlineUsers();
});

function loadUsers() {
    const tbody = $('#usersTable tbody');

    // 显示加载状态
    tbody.html(`
        <tr>
            <td colspan="8" class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载用户数据...</p>
            </td>
        </tr>
    `);

    // 从API获取用户数据
    fetch('/api/admin/users')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '';
                data.data.forEach(user => {
                    const roleClass = user.role === 'admin' ? 'bg-danger' : 'bg-primary';
                    const statusClass = user.is_active ? 'bg-success' : 'bg-secondary';
                    const statusText = user.is_active ? '活跃' : '禁用';

                    html += `
                        <tr>
                            <td>${user.id}</td>
                            <td>${user.username}</td>
                            <td>${user.email}</td>
                            <td><span class="badge ${roleClass}">${user.role === 'admin' ? '管理员' : '普通用户'}</span></td>
                            <td><span class="badge ${statusClass}">${statusText}</span></td>
                            <td>${user.created_at}</td>
                            <td>${user.last_login}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="editUser(${user.id})" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id})" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                });
                tbody.html(html);
            } else {
                tbody.html(`
                    <tr>
                        <td colspan="8" class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            加载用户数据失败: ${data.error || '未知错误'}
                        </td>
                    </tr>
                `);
            }
        })
        .catch(error => {
            console.error('加载用户数据失败:', error);
            tbody.html(`
                <tr>
                    <td colspan="8" class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        网络错误，请检查连接后重试
                    </td>
                </tr>
            `);
        });
}

function loadUserStats() {
    // 从API获取用户统计数据
    fetch('/api/admin/users/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                $('#total-users').text(data.data.total_users);
                // 在线用户数通过单独的API获取
                updateOnlineUsers();
                $('#admin-count').text(data.data.admin_count);
                $('#today-new-users').text(data.data.today_new_users);
            } else {
                console.error('加载用户统计失败:', data.error);
            }
        })
        .catch(error => {
            console.error('加载用户统计失败:', error);
        });
}

function updateOnlineUsers() {
    console.log('开始更新在线用户数...');
    // 获取在线用户统计
    fetch('/api/online-count')
        .then(response => {
            console.log('API响应状态:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('收到在线用户数据:', data);
            const currentValue = $('#online-users').text();
            $('#online-users').text(data.total);
            console.log(`在线用户数更新: ${currentValue} -> ${data.total}`);
        })
        .catch(error => {
            console.error('更新在线用户数失败:', error);
            $('#online-users').text('0');
        });
}

function addUser() {
    const form = document.getElementById('addUserForm');
    const formData = new FormData(form);
    const userData = Object.fromEntries(formData);

    // 验证表单数据
    if (!userData.username || !userData.email || !userData.password || !userData.role) {
        alert('请填写所有必需字段');
        return;
    }

    // 发送添加用户请求
    fetch('/api/admin/users', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            $('#addUserModal').modal('hide');

            // 清空表单
            form.reset();

            // 重新加载用户列表和统计
            loadUsers();
            loadUserStats();

            // 显示成功消息
            alert('用户添加成功！');
        } else {
            alert('添加用户失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('添加用户失败:', error);
        alert('网络错误，请检查连接后重试');
    });
}

function editUser(userId) {
    // 获取用户信息并填充到编辑表单
    fetch(`/api/admin/users`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const user = data.data.find(u => u.id === userId);
                if (user) {
                    // 填充表单
                    document.getElementById('editUserId').value = user.id;
                    document.getElementById('editUsername').value = user.username;
                    document.getElementById('editEmail').value = user.email;
                    document.getElementById('editPassword').value = '';
                    document.getElementById('editRole').value = user.role;
                    document.getElementById('editIsActive').checked = user.is_active;

                    // 显示编辑模态框
                    $('#editUserModal').modal('show');
                } else {
                    alert('找不到用户信息');
                }
            } else {
                alert('获取用户信息失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('获取用户信息失败:', error);
            alert('网络错误，请检查连接后重试');
        });
}

function updateUser() {
    const form = document.getElementById('editUserForm');
    const formData = new FormData(form);
    const userData = Object.fromEntries(formData);
    const userId = userData.userId;

    // 处理复选框
    userData.is_active = document.getElementById('editIsActive').checked;

    // 如果密码为空，则不发送密码字段
    if (!userData.password) {
        delete userData.password;
    }

    // 删除userId字段，因为它在URL中
    delete userData.userId;

    // 发送更新用户请求
    fetch(`/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            $('#editUserModal').modal('hide');

            // 重新加载用户列表和统计
            loadUsers();
            loadUserStats();

            alert('用户更新成功！');
        } else {
            alert('更新用户失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('更新用户失败:', error);
        alert('网络错误，请检查连接后重试');
    });
}

function deleteUser(userId) {
    if (confirm('确定要删除这个用户吗？此操作不可撤销！')) {
        // 发送删除用户请求
        fetch(`/api/admin/users/${userId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 重新加载用户列表和统计
                loadUsers();
                loadUserStats();
                alert('用户删除成功！');
            } else {
                alert('删除用户失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('删除用户失败:', error);
            alert('网络错误，请检查连接后重试');
        });
    }
}
</script>
{% endblock %}
