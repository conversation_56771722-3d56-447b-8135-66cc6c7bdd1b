{% extends "base.html" %}

{% block title %}用户注册 - {{ super() }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>用户注册
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form method="POST" id="registerForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-1"></i>用户名 <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="username" name="username" required
                                   minlength="3" maxlength="20" pattern="[a-zA-Z0-9_]+"
                                   title="用户名只能包含字母、数字和下划线，长度3-20位">
                            <div class="form-text">用户名只能包含字母、数字和下划线，长度3-20位</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>邮箱地址 <span class="text-danger">*</span>
                            </label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="form-text">请输入有效的邮箱地址</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-1"></i>密码 <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control" id="password" name="password" required
                                   minlength="6" maxlength="20">
                            <div class="form-text">密码长度6-20位</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-lock me-1"></i>确认密码 <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div class="invalid-feedback" id="password-mismatch">
                                两次输入的密码不一致
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="captcha" class="form-label">
                                <i class="fas fa-shield-alt me-1"></i>验证码 <span class="text-danger">*</span>
                            </label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="text" class="form-control" id="captcha" name="captcha" required
                                           placeholder="请输入验证码" maxlength="4">
                                </div>
                                <div class="col-6">
                                    <img id="captcha-image" src="" alt="验证码" class="img-fluid border rounded"
                                         style="height: 38px; cursor: pointer;" onclick="refreshCaptcha()"
                                         title="点击刷新验证码">
                                </div>
                            </div>
                            <small class="text-muted">点击图片可刷新验证码</small>
                        </div>               
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-user-plus me-2"></i>注册账户
                            </button>
                        </div>
                    </form>
                    
                    <hr>
                    
                    <div class="text-center">
                        <p class="mb-2">已有账户？</p>
                        <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>立即登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 刷新验证码
function refreshCaptcha() {
    fetch('/auth/captcha')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('captcha-image').src = data.image;
                document.getElementById('captcha').value = '';
            }
        })
        .catch(error => {
            console.error('刷新验证码失败:', error);
        });
}

$(document).ready(function() {
    // 页面加载时获取验证码
    refreshCaptcha();
    // 密码确认验证
    $('#confirm_password').on('input', function() {
        var password = $('#password').val();
        var confirmPassword = $(this).val();
        
        if (password !== confirmPassword) {
            $(this).addClass('is-invalid');
            $('#password-mismatch').show();
        } else {
            $(this).removeClass('is-invalid');
            $('#password-mismatch').hide();
        }
    });
    
    // 表单提交验证
    $('#registerForm').on('submit', function(e) {
        var username = $('#username').val().trim();
        var email = $('#email').val().trim();
        var password = $('#password').val();
        var confirmPassword = $('#confirm_password').val();
        var captcha = $('#captcha').val().trim();
        var agreeTerms = $('#agree_terms').is(':checked');
        
        // 基本验证
        if (!username || !email || !password || !confirmPassword || !captcha) {
            e.preventDefault();
            alert('请填写所有必填字段');
            return false;
        }
        
        // 密码确认验证
        if (password !== confirmPassword) {
            e.preventDefault();
            alert('两次输入的密码不一致');
            $('#confirm_password').focus();
            return false;
        }
        
        // 显示加载状态
        $(this).find('button[type="submit"]').prop('disabled', true).html(
            '<i class="fas fa-spinner fa-spin me-2"></i>注册中...'
        );
    });
    
    // 用户名实时验证
    $('#username').on('input', function() {
        var username = $(this).val();
        var pattern = /^[a-zA-Z0-9_]+$/;
        
        if (username.length > 0 && !pattern.test(username)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // 自动聚焦到用户名输入框
    $('#username').focus();
});
</script>
{% endblock %}
