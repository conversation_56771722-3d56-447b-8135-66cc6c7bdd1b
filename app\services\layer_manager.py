"""
图层管理服务
负责GIS图层的集成、坐标系转换、空间匹配和显示管理
"""
import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from flask import current_app

# 创建logger实例
logger = logging.getLogger(__name__)

# GIS库导入
try:
    import rasterio
    import numpy as np
    import geopandas as gpd
    from rasterio.warp import calculate_default_transform, reproject, Resampling
    from rasterio.crs import CRS
    from shapely.geometry import box, Point
    from pyproj import Transformer
    GIS_LIBS_AVAILABLE = True
    logger.info("图层管理器: GIS库加载成功")
except ImportError as e:
    GIS_LIBS_AVAILABLE = False
    logger.warning(f"图层管理器: GIS库不可用: {e}")
    rasterio = None
    np = None
    gpd = None

from app.models import GISLayer, db


class LayerManager:
    """图层管理器 - 负责图层集成和空间匹配"""
    
    # 标准坐标系
    STANDARD_CRS = 'EPSG:4326'  # WGS84地理坐标系
    
    # 基础图层类型
    BASE_LAYER_TYPES = {
        'administrative': '行政区划',
        'landuse': '土地利用',
        'elevation': '数字高程模型',
        'risk_zones': '风险区划'
    }
    
    def __init__(self):
        """初始化图层管理器"""
        self.base_layers_path = os.path.join(current_app.root_path, '..', 'data', 'base_layers')
        self.ensure_base_layers_directory()
    
    def ensure_base_layers_directory(self):
        """确保基础图层目录存在"""
        os.makedirs(self.base_layers_path, exist_ok=True)
        
        # 创建子目录
        for layer_type in self.BASE_LAYER_TYPES.keys():
            layer_dir = os.path.join(self.base_layers_path, layer_type)
            os.makedirs(layer_dir, exist_ok=True)
    
    def get_layer_spatial_info(self, layer_path: str) -> Dict[str, Any]:
        """获取图层的空间信息"""
        if not GIS_LIBS_AVAILABLE:
            logger.warning("GIS库不可用，无法获取空间信息")
            return {}
        
        try:
            # 检查文件类型
            file_ext = os.path.splitext(layer_path)[1].lower()
            
            if file_ext in ['.tif', '.tiff']:
                return self._get_raster_spatial_info(layer_path)
            elif file_ext == '.shp':
                return self._get_vector_spatial_info(layer_path)
            else:
                logger.warning(f"不支持的文件类型: {file_ext}")
                return {}
                
        except Exception as e:
            logger.error(f"获取空间信息失败: {e}")
            return {}
    
    def _get_raster_spatial_info(self, raster_path: str) -> Dict[str, Any]:
        """获取栅格数据的空间信息"""
        with rasterio.open(raster_path) as src:
            # 获取坐标系
            crs = src.crs.to_string() if src.crs else None
            
            # 获取边界框
            bounds = src.bounds
            
            # 获取像素大小
            transform = src.transform
            pixel_size_x = abs(transform[0])
            pixel_size_y = abs(transform[4])
            
            # 获取栅格尺寸
            width = src.width
            height = src.height
            
            return {
                'type': 'raster',
                'crs': crs,
                'bounds': [bounds.left, bounds.bottom, bounds.right, bounds.top],
                'pixel_size': [pixel_size_x, pixel_size_y],
                'dimensions': [width, height],
                'band_count': src.count,
                'data_type': str(src.dtypes[0])
            }
    
    def _get_vector_spatial_info(self, vector_path: str) -> Dict[str, Any]:
        """获取矢量数据的空间信息"""
        gdf = gpd.read_file(vector_path)
        
        # 获取坐标系
        crs = gdf.crs.to_string() if gdf.crs else None
        
        # 获取边界框
        bounds = gdf.total_bounds
        
        # 获取几何类型
        geometry_types = gdf.geom_type.unique().tolist()
        
        return {
            'type': 'vector',
            'crs': crs,
            'bounds': bounds.tolist(),
            'geometry_types': geometry_types,
            'feature_count': len(gdf),
            'attributes': gdf.columns.tolist()
        }
    
    def check_spatial_alignment(self, layer1_path: str, layer2_path: str) -> Dict[str, Any]:
        """检查两个图层的空间对齐情况"""
        if not GIS_LIBS_AVAILABLE:
            return {'aligned': False, 'reason': 'GIS库不可用'}
        
        try:
            info1 = self.get_layer_spatial_info(layer1_path)
            info2 = self.get_layer_spatial_info(layer2_path)
            
            if not info1 or not info2:
                return {'aligned': False, 'reason': '无法获取空间信息'}
            
            # 检查坐标系
            crs1 = info1.get('crs')
            crs2 = info2.get('crs')
            
            if crs1 != crs2:
                return {
                    'aligned': False, 
                    'reason': f'坐标系不匹配: {crs1} vs {crs2}',
                    'needs_reprojection': True,
                    'source_crs': crs1,
                    'target_crs': crs2
                }
            
            # 检查边界框重叠
            bounds1 = info1.get('bounds', [])
            bounds2 = info2.get('bounds', [])
            
            if len(bounds1) == 4 and len(bounds2) == 4:
                overlap = self._calculate_bounds_overlap(bounds1, bounds2)
                if overlap < 0.1:  # 重叠面积小于10%
                    return {
                        'aligned': False,
                        'reason': f'空间范围重叠度过低: {overlap:.2%}',
                        'overlap_ratio': overlap
                    }
            
            return {
                'aligned': True,
                'crs': crs1,
                'overlap_ratio': overlap if 'overlap' in locals() else 1.0
            }
            
        except Exception as e:
            logger.error(f"空间对齐检查失败: {e}")
            return {'aligned': False, 'reason': str(e)}
    
    def _calculate_bounds_overlap(self, bounds1: List[float], bounds2: List[float]) -> float:
        """计算两个边界框的重叠比例"""
        # bounds格式: [minx, miny, maxx, maxy]
        x_overlap = max(0, min(bounds1[2], bounds2[2]) - max(bounds1[0], bounds2[0]))
        y_overlap = max(0, min(bounds1[3], bounds2[3]) - max(bounds1[1], bounds2[1]))
        
        overlap_area = x_overlap * y_overlap
        
        area1 = (bounds1[2] - bounds1[0]) * (bounds1[3] - bounds1[1])
        area2 = (bounds2[2] - bounds2[0]) * (bounds2[3] - bounds2[1])
        
        union_area = area1 + area2 - overlap_area
        
        return overlap_area / union_area if union_area > 0 else 0
    
    def get_base_layers(self) -> List[Dict[str, Any]]:
        """获取系统基础图层列表"""
        base_layers = []
        
        for layer_type, display_name in self.BASE_LAYER_TYPES.items():
            layer_dir = os.path.join(self.base_layers_path, layer_type)
            
            if os.path.exists(layer_dir):
                for filename in os.listdir(layer_dir):
                    file_path = os.path.join(layer_dir, filename)
                    if os.path.isfile(file_path):
                        spatial_info = self.get_layer_spatial_info(file_path)
                        
                        base_layers.append({
                            'name': os.path.splitext(filename)[0],
                            'display_name': f"{display_name} - {os.path.splitext(filename)[0]}",
                            'type': layer_type,
                            'file_path': file_path,
                            'spatial_info': spatial_info
                        })
        
        return base_layers

    def reproject_layer(self, source_path: str, target_path: str, target_crs: str = None) -> bool:
        """重投影图层到目标坐标系"""
        if not GIS_LIBS_AVAILABLE:
            logger.warning("GIS库不可用，无法进行重投影")
            return False

        if target_crs is None:
            target_crs = self.STANDARD_CRS

        try:
            file_ext = os.path.splitext(source_path)[1].lower()

            if file_ext in ['.tif', '.tiff']:
                return self._reproject_raster(source_path, target_path, target_crs)
            elif file_ext == '.shp':
                return self._reproject_vector(source_path, target_path, target_crs)
            else:
                logger.warning(f"不支持重投影的文件类型: {file_ext}")
                return False

        except Exception as e:
            logger.error(f"重投影失败: {e}")
            return False

    def _reproject_raster(self, source_path: str, target_path: str, target_crs: str) -> bool:
        """重投影栅格数据"""
        with rasterio.open(source_path) as src:
            # 计算目标变换参数
            transform, width, height = calculate_default_transform(
                src.crs, target_crs, src.width, src.height, *src.bounds
            )

            # 创建输出文件的元数据
            kwargs = src.meta.copy()
            kwargs.update({
                'crs': target_crs,
                'transform': transform,
                'width': width,
                'height': height
            })

            # 执行重投影
            with rasterio.open(target_path, 'w', **kwargs) as dst:
                for i in range(1, src.count + 1):
                    reproject(
                        source=rasterio.band(src, i),
                        destination=rasterio.band(dst, i),
                        src_transform=src.transform,
                        src_crs=src.crs,
                        dst_transform=transform,
                        dst_crs=target_crs,
                        resampling=Resampling.nearest
                    )

        return True

    def _reproject_vector(self, source_path: str, target_path: str, target_crs: str) -> bool:
        """重投影矢量数据"""
        gdf = gpd.read_file(source_path)
        gdf_reprojected = gdf.to_crs(target_crs)
        gdf_reprojected.to_file(target_path)
        return True

    def integrate_user_layer_with_base(self, user_layer_id: int, base_layer_type: str) -> Dict[str, Any]:
        """将用户图层与基础图层进行集成"""
        try:
            # 获取用户图层
            user_layer = GISLayer.query.get(user_layer_id)
            if not user_layer:
                return {'success': False, 'error': '用户图层不存在'}

            # 获取基础图层
            base_layers = self.get_base_layers()
            base_layer = next((layer for layer in base_layers if layer['type'] == base_layer_type), None)

            if not base_layer:
                return {'success': False, 'error': f'基础图层类型 {base_layer_type} 不存在'}

            # 检查空间对齐
            alignment = self.check_spatial_alignment(user_layer.file_path, base_layer['file_path'])

            result = {
                'success': True,
                'user_layer': {
                    'id': user_layer.id,
                    'name': user_layer.name,
                    'type': user_layer.layer_type,
                    'file_path': user_layer.file_path
                },
                'base_layer': base_layer,
                'spatial_alignment': alignment
            }

            # 如果需要重投影，提供建议
            if not alignment.get('aligned', False) and alignment.get('needs_reprojection', False):
                result['recommendations'] = {
                    'action': 'reproject',
                    'source_crs': alignment.get('source_crs'),
                    'target_crs': alignment.get('target_crs', self.STANDARD_CRS),
                    'message': '建议将图层重投影到统一坐标系以确保正确叠加'
                }

            return result

        except Exception as e:
            logger.error(f"图层集成失败: {e}")
            return {'success': False, 'error': str(e)}

