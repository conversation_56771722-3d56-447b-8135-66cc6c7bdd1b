"""
GIS数据处理服务
专门处理Shapefile和TIFF文件的上传、解析和元数据提取
"""
import os
import json
import logging
from typing import Dict, List, Optional
from datetime import datetime

# 创建logger实例
logger = logging.getLogger(__name__)

# GIS库导入
try:
    import geopandas as gpd
    import rasterio
    GIS_LIBS_AVAILABLE = True
    logger.info("GIS库加载成功")
except ImportError as e:
    GIS_LIBS_AVAILABLE = False
    logger.warning(f"GIS库加载失败: {e}")
except Exception as e:
    GIS_LIBS_AVAILABLE = False
    logger.warning(f"GIS库初始化失败: {e}")

from app.models import GISLayer, db, get_china_time
from app.services.intelligent_classifier import IntelligentClassifier


class GISDataService:
    """GIS数据处理服务类"""

    @classmethod
    def process_shapefile(cls, file_group: List[Dict], upload_dir: str, user_id: int) -> Optional[GISLayer]:
        """处理Shapefile文件组"""
        if not GIS_LIBS_AVAILABLE:
            logger.error("GIS库不可用，无法处理Shapefile")
            return None
        
        try:
            # 找到主.shp文件
            shp_file = None
            for file_info in file_group:
                if file_info['name'].lower().endswith('.shp'):
                    shp_file = file_info
                    break
            
            if not shp_file:
                logger.error("未找到.shp文件")
                return None
            
            shp_path = shp_file['path']
            
            # 使用GeoPandas读取Shapefile
            gdf = gpd.read_file(shp_path)

            # 提取元数据
            bounds = gdf.total_bounds  # [minx, miny, maxx, maxy]
            feature_count = len(gdf)
            geometry_type = gdf.geom_type.iloc[0] if len(gdf) > 0 else 'Unknown'

            # 处理坐标系信息，避免EPSG:7等无效坐标系
            try:
                if gdf.crs is None or str(gdf.crs) == 'EPSG:7' or 'EPSG:7' in str(gdf.crs):
                    logger.warning(f"检测到无效坐标系 {gdf.crs}，设置为 EPSG:4326")
                    crs = 'EPSG:4326'
                else:
                    crs = str(gdf.crs)
            except Exception as e:
                logger.warning(f"坐标系处理失败: {e}，设置为 EPSG:4326")
                crs = 'EPSG:4326'
            
            # 提取属性字段信息
            attributes = []
            for col in gdf.columns:
                if col != 'geometry':
                    attributes.append({
                        'name': col,
                        'type': str(gdf[col].dtype),
                        'sample_values': gdf[col].dropna().head(3).tolist()
                    })
            
            # 使用智能分类器进行省份识别（矢量数据主要用于省份识别）
            try:
                filename_analysis = IntelligentClassifier._analyze_filename(shp_file['name'])
                province = filename_analysis.get('province')

                logger.info(f"矢量数据省份识别: {province}")

                # 如果智能分类失败，回退到原有方法
                if not province:
                    province = cls._extract_province_from_filename(shp_file['name'])

            except Exception as e:
                logger.warning(f"矢量数据智能分类失败，使用传统方法: {e}")
                province = cls._extract_province_from_filename(shp_file['name'])

            # 确定数据类别（区划或土地利用）
            base_name = os.path.splitext(shp_file['name'])[0].lower()
            if '区划' in base_name or 'administrative' in base_name:
                data_category = 'administrative'
            elif '土地利用' in base_name or 'landuse' in base_name:
                data_category = 'landuse'
            else:
                data_category = 'other'
            
            # 创建GISLayer记录
            layer = GISLayer(
                name=os.path.splitext(shp_file['name'])[0],
                display_name=os.path.splitext(shp_file['name'])[0],
                description=f"Shapefile矢量数据 - {feature_count}个要素",
                layer_type='vector',
                data_category=data_category,
                province=province,
                file_path=shp_path,
                original_filename=shp_file['name'],
                file_size=sum(f.get('size', 0) for f in file_group),
                coordinate_system=crs,
                feature_count=feature_count,
                geometry_type=geometry_type,
                processing_status='processed',
                created_by=user_id,
                created_at=get_china_time(),
                updated_at=get_china_time()
            )
            
            # 设置边界框和属性
            layer.set_bounds(bounds.tolist())
            layer.set_attributes(attributes)
            
            # 设置相关文件列表
            related_files = [f['path'] for f in file_group if f != shp_file]
            layer.set_related_files(related_files)
            
            # 设置默认样式
            default_style = cls._get_default_vector_style(data_category, geometry_type)
            layer.default_style = json.dumps(default_style)
            
            return layer
            
        except Exception as e:
            logger.error(f"处理Shapefile失败: {e}")
            return None
    
    @classmethod
    def process_raster_file(cls, file_info: Dict, upload_dir: str, user_id: int) -> Optional[GISLayer]:
        """处理栅格文件（TIFF）"""
        if not GIS_LIBS_AVAILABLE:
            logger.error("GIS库不可用，无法处理栅格文件")
            return None

        # 检查rasterio是否可用
        try:
            import rasterio
        except ImportError:
            logger.error("Rasterio库不可用，无法处理栅格文件")
            return None
        
        try:
            file_path = file_info['path']
            
            # 使用Rasterio读取栅格文件
            with rasterio.open(file_path) as src:
                # 提取元数据
                bounds = src.bounds
                width = src.width
                height = src.height
                band_count = src.count
                crs = str(src.crs) if src.crs else 'EPSG:4326'
                pixel_size_x = src.transform[0]
                pixel_size_y = abs(src.transform[4])
                data_type = str(src.dtypes[0])
                
                # 转换边界框为列表格式
                bounds_list = [bounds.left, bounds.bottom, bounds.right, bounds.top]
            
            # 使用智能分类器进行自动分类
            try:
                classification_result = IntelligentClassifier.get_enhanced_classification_info(
                    file_path, file_info['name']
                )

                # 提取分类结果
                classification = classification_result['classification']
                data_category = classification.get('data_type', 'other')
                province = classification.get('province')

                # 记录分类信息到日志
                logger.info(f"智能分类结果: 省份={province}, 类型={data_category}, "
                           f"置信度={classification.get('confidence', 0.0):.2f}")

                # 如果智能分类失败，回退到原有方法
                if not province:
                    province = cls._extract_province_from_filename(file_info['name'])

                if data_category == 'other':
                    filename_lower = file_info['name'].lower()
                    if 'clcd' in filename_lower or 'landuse' in filename_lower or '土地利用' in filename_lower:
                        data_category = 'landuse'
                    elif 'dem' in filename_lower or 'elevation' in filename_lower:
                        data_category = 'elevation'

            except Exception as e:
                logger.warning(f"智能分类失败，使用传统方法: {e}")
                # 回退到原有的简单分类方法
                filename_lower = file_info['name'].lower()
                if 'clcd' in filename_lower or 'landuse' in filename_lower or '土地利用' in filename_lower:
                    data_category = 'landuse'
                elif 'dem' in filename_lower or 'elevation' in filename_lower:
                    data_category = 'elevation'
                else:
                    data_category = 'other'

                province = cls._extract_province_from_filename(file_info['name'])
            
            # 创建GISLayer记录
            layer = GISLayer(
                name=os.path.splitext(file_info['name'])[0],
                display_name=os.path.splitext(file_info['name'])[0],
                description=f"栅格数据 - {width}x{height}像素，{band_count}个波段",
                layer_type='raster',
                data_category=data_category,
                province=province,
                file_path=file_path,
                original_filename=file_info['name'],
                file_size=file_info.get('size', 0),
                coordinate_system=crs,
                raster_width=width,
                raster_height=height,
                pixel_size_x=pixel_size_x,
                pixel_size_y=pixel_size_y,
                band_count=band_count,
                data_type=data_type,
                processing_status='processed',
                created_by=user_id,
                created_at=get_china_time(),
                updated_at=get_china_time()
            )
            
            # 设置边界框
            layer.set_bounds(bounds_list)
            
            # 设置默认样式
            default_style = cls._get_default_raster_style(data_category)
            layer.default_style = json.dumps(default_style)
            
            return layer
            
        except Exception as e:
            logger.error(f"处理栅格文件失败: {e}")
            return None
    
    @classmethod
    def _extract_province_from_filename(cls, filename: str) -> Optional[str]:
        """从文件名中提取省份信息"""
        try:
            from app.config import get_province_by_keyword

            filename_lower = filename.lower()

            # 使用统一的省份配置进行匹配
            for keyword in filename_lower.split():
                province = get_province_by_keyword(keyword)
                if province:
                    return province

            # 如果按空格分割没有匹配，尝试子字符串匹配
            from app.config import PROVINCE_KEYWORD_MAPPING
            for keyword, province in PROVINCE_KEYWORD_MAPPING.items():
                if keyword in filename_lower:
                    return province

            return None

        except ImportError:
            # 如果导入失败，使用原有的简化映射作为备用
            province_keywords = {
                '河南': ['henan', '河南'],
                '湖北': ['hubei', '湖北'],
                '福建': ['fujian', '福建'],
                '广东': ['guangdong', '广东'],
                '江苏': ['jiangsu', '江苏'],
                '浙江': ['zhejiang', '浙江'],
                '山东': ['shandong', '山东'],
                '四川': ['sichuan', '四川'],
                '湖南': ['hunan', '湖南'],
                '安徽': ['anhui', '安徽']
            }

            filename_lower = filename.lower()
            for province, keywords in province_keywords.items():
                for keyword in keywords:
                    if keyword in filename_lower:
                        return province

            return None
    
    @classmethod
    def _get_default_vector_style(cls, data_category: str, geometry_type: str) -> Dict:
        """获取矢量数据的默认样式"""
        if data_category == 'administrative':
            return {
                'color': '#FF0000',
                'weight': 2,
                'opacity': 0.8,
                'fillColor': '#FF0000',
                'fillOpacity': 0.1
            }
        elif data_category == 'landuse':
            return {
                'color': '#00FF00',
                'weight': 1,
                'opacity': 0.7,
                'fillColor': '#00FF00',
                'fillOpacity': 0.3
            }
        else:
            return {
                'color': '#0000FF',
                'weight': 1,
                'opacity': 0.6,
                'fillColor': '#0000FF',
                'fillOpacity': 0.2
            }
    
    @classmethod
    def _get_default_raster_style(cls, data_category: str) -> Dict:
        """获取栅格数据的默认样式"""
        if data_category == 'landuse':
            return {
                'opacity': 0.7,
                'colormap': 'landuse'
            }
        elif data_category == 'elevation':
            return {
                'opacity': 0.6,
                'colormap': 'terrain'
            }
        else:
            return {
                'opacity': 0.5,
                'colormap': 'viridis'
            }
