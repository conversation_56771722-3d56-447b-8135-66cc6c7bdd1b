"""
风险评估模块路由
"""
from flask import Blueprint, request, jsonify, render_template, flash, redirect, url_for
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
import os
import json
from datetime import datetime

# 尝试导入pandas，如果失败则设置标志
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    pd = None
from app import db
from app.models import (
    RiskWeightConfig, RiskAssessmentProject, RiskAssessmentData, 
    RiskCalculationResult, RiskMatrix
)
from .risk_service import RiskAssessmentService
from .matrix_builder import RiskMatrixBuilder
from .file_importer import FileImporter
from .multi_sheet_importer import MultiSheetExcelImporter
from .temp_file_manager import temp_file_manager

bp = Blueprint('risk_assessment', __name__, url_prefix='/risk-assessment')

# 初始化服务
risk_service = RiskAssessmentService()


@bp.route('/')
@login_required
def index():
    """风险评估主页"""
    return render_template('risk_assessment/index.html')


@bp.route('/projects')
@login_required
def projects():
    """项目列表页面"""
    user_projects = RiskAssessmentProject.query.filter_by(created_by=current_user.id).all()
    return render_template('risk_assessment/projects.html', projects=user_projects)


@bp.route('/project/create', methods=['GET', 'POST'])
@login_required
def create_project():
    """创建新省份评估"""
    if request.method == 'POST':
        try:
            project_name = request.form.get('project_name')
            description = request.form.get('description')
            location = request.form.get('location')
            assessment_year = request.form.get('assessment_year')

            if not project_name:
                flash('省份评估名称不能为空', 'error')
                return redirect(url_for('risk_assessment.create_project'))

            if not location:
                flash('请选择评估省份', 'error')
                return redirect(url_for('risk_assessment.create_project'))

            if not assessment_year:
                flash('请选择评估年份', 'error')
                return redirect(url_for('risk_assessment.create_project'))

            # 转换年份为整数
            try:
                assessment_year = int(assessment_year)
            except (ValueError, TypeError):
                flash('评估年份格式不正确', 'error')
                return redirect(url_for('risk_assessment.create_project'))

            project = risk_service.create_assessment_project(
                project_name=project_name,
                description=description,
                location=location,
                assessment_year=assessment_year,
                user_id=current_user.id
            )

            flash(f'省份评估 "{project_name}" ({assessment_year}年) 创建成功', 'success')
            return redirect(url_for('risk_assessment.project_detail', project_id=project.id))

        except Exception as e:
            flash(f'创建省份评估失败: {str(e)}', 'error')
            return redirect(url_for('risk_assessment.create_project'))

    return render_template('risk_assessment/create_project.html')


@bp.route('/project/<int:project_id>')
@login_required
def project_detail(project_id):
    """省份评估详情页面"""
    project = RiskAssessmentProject.query.get_or_404(project_id)

    # 检查权限
    if project.created_by != current_user.id and not current_user.is_admin():
        flash('您没有权限访问此省份评估', 'error')
        return redirect(url_for('risk_assessment.projects'))
    
    # 获取项目数据
    assessment_data = RiskAssessmentData.query.filter_by(project_id=project_id).all()
    calculation_results = RiskCalculationResult.query.filter_by(project_id=project_id).all()
    
    # 获取权重配置
    weight_config = project.weight_config or risk_service.get_default_weight_config()
    
    return render_template('risk_assessment/project_detail.html',
                         project=project,
                         assessment_data=assessment_data,
                         calculation_results=calculation_results,
                         weight_config=weight_config)


@bp.route('/project/<int:project_id>/results')
@login_required
def project_results(project_id):
    """项目结果展示页面"""
    project = RiskAssessmentProject.query.get_or_404(project_id)

    # 检查权限
    if project.created_by != current_user.id and not current_user.is_admin():
        flash('您没有权限访问此项目', 'error')
        return redirect(url_for('risk_assessment.projects'))

    return render_template('risk_assessment/results.html', project=project)


@bp.route('/weight-config')
@login_required
def weight_config():
    """权重配置管理页面"""
    return render_template('risk_assessment/weight_config.html')


# API接口
@bp.route('/api/projects', methods=['GET'])
@login_required
def api_get_projects():
    """获取用户的项目列表"""
    projects = RiskAssessmentProject.query.filter_by(created_by=current_user.id).all()
    
    return jsonify({
        'success': True,
        'projects': [{
            'id': p.id,
            'project_name': p.project_name,
            'description': p.description,
            'location': p.location,
            'status': p.status,
            'created_at': p.created_at.isoformat() if p.created_at else None,
            'data_count': len(p.assessment_data)
        } for p in projects]
    })


@bp.route('/api/project/<int:project_id>/data', methods=['POST'])
@login_required
def api_add_assessment_data(project_id):
    """添加评估数据到项目"""
    try:
        project = RiskAssessmentProject.query.get_or_404(project_id)
        
        # 检查权限
        if project.created_by != current_user.id and not current_user.is_admin():
            return jsonify({'success': False, 'error': '权限不足'}), 403
        
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '请提供数据'}), 400
        
        # 支持单个数据或数据列表
        data_list = data if isinstance(data, list) else [data]
        
        # 验证必需字段
        required_fields = ['annual_precipitation', 'drainage_network_density', 'elevation',
                          'population_density', 'gdp_per_area']
        
        for item in data_list:
            missing_fields = [field for field in required_fields if field not in item or item[field] is None]
            if missing_fields:
                return jsonify({
                    'success': False, 
                    'error': f'缺少必需字段: {", ".join(missing_fields)}'
                }), 400
        
        # 添加数据
        created_data = risk_service.add_assessment_data(project_id, data_list)
        
        return jsonify({
            'success': True,
            'message': f'成功添加 {len(created_data)} 条数据',
            'data_count': len(created_data)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/project/<int:project_id>', methods=['DELETE'])
@login_required
def api_delete_project(project_id):
    """删除项目"""
    try:
        project = RiskAssessmentProject.query.get_or_404(project_id)

        # 检查权限
        if project.created_by != current_user.id and not current_user.is_admin():
            return jsonify({'success': False, 'error': '权限不足'}), 403

        project_name = project.project_name

        # 按正确顺序删除相关数据
        # 1. 先删除计算结果（因为它们引用评估数据）
        calculation_results_count = RiskCalculationResult.query.filter_by(project_id=project_id).count()
        if calculation_results_count > 0:
            RiskCalculationResult.query.filter_by(project_id=project_id).delete()
            print(f"删除了 {calculation_results_count} 条计算结果")

        # 2. 再删除评估数据
        assessment_data_count = RiskAssessmentData.query.filter_by(project_id=project_id).count()
        if assessment_data_count > 0:
            RiskAssessmentData.query.filter_by(project_id=project_id).delete()
            print(f"删除了 {assessment_data_count} 条评估数据")

        # 3. 最后删除项目
        db.session.delete(project)
        db.session.commit()

        print(f"成功删除项目: {project_name} (ID: {project_id})")

        return jsonify({
            'success': True,
            'message': f'项目 "{project_name}" 删除成功'
        })

    except Exception as e:
        db.session.rollback()
        print(f"删除项目失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/project/<int:project_id>/calculate', methods=['POST'])
@login_required
def api_calculate_risks(project_id):
    """计算项目风险"""
    try:
        project = RiskAssessmentProject.query.get_or_404(project_id)

        # 检查权限
        if project.created_by != current_user.id and not current_user.is_admin():
            return jsonify({'success': False, 'error': '权限不足'}), 403

        # 执行计算
        results = risk_service.calculate_project_risks(project_id, current_user.id)

        return jsonify({
            'success': True,
            'message': f'成功计算 {len(results)} 条数据的风险等级',
            'results_count': len(results)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/project/<int:project_id>/results', methods=['GET'])
@login_required
def api_get_project_results(project_id):
    """获取项目计算结果"""
    try:
        project = RiskAssessmentProject.query.get_or_404(project_id)
        
        # 检查权限
        if project.created_by != current_user.id and not current_user.is_admin():
            return jsonify({'success': False, 'error': '权限不足'}), 403
        
        results = risk_service.get_project_results(project_id)
        
        return jsonify({
            'success': True,
            'project_id': project_id,
            'project_name': project.project_name,
            **results
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/weight-configs', methods=['GET'])
@login_required
def api_get_weight_configs():
    """获取权重配置列表"""
    configs = RiskWeightConfig.query.filter_by(is_active=True).all()
    
    return jsonify({
        'success': True,
        'configs': [config.to_dict() for config in configs]
    })


@bp.route('/api/weight-config/<int:config_id>', methods=['PUT'])
@login_required
def api_update_weight_config(config_id):
    """更新权重配置"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '请提供权重数据'}), 400
        
        config = risk_service.update_weight_config(config_id, data)
        
        return jsonify({
            'success': True,
            'message': '权重配置更新成功',
            'config': config.to_dict()
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/weight-config', methods=['POST'])
@login_required
def api_create_weight_config():
    """创建新的权重配置"""
    try:
        data = request.get_json()
        if not data or 'config_name' not in data:
            return jsonify({'success': False, 'error': '请提供配置名称'}), 400

        # 确保所有权重字段都有默认值
        weight_data = {
            'config_name': data.get('config_name', ''),
            'description': data.get('description', ''),
            'rainfall_weight': float(data.get('rainfall_weight', 0.633)),
            'drainage_weight': float(data.get('drainage_weight', 0.260)),
            'elevation_weight': float(data.get('elevation_weight', 0.107)),
            'gdp_weight': float(data.get('gdp_weight', 0.6)),
            'population_weight': float(data.get('population_weight', 0.4))
        }

        config = risk_service.create_custom_weight_config(
            config_name=weight_data['config_name'],
            weight_data=weight_data,
            user_id=current_user.id
        )

        return jsonify({
            'success': True,
            'message': '权重配置创建成功',
            'config': config.to_dict()
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/weight-config/<int:config_id>', methods=['DELETE'])
@login_required
def api_delete_weight_config(config_id):
    """删除权重配置"""
    try:
        # 检查配置是否存在
        config = RiskWeightConfig.query.get(config_id)
        if not config:
            return jsonify({'success': False, 'error': '权重配置不存在'}), 404

        # 检查权限：只有创建者或管理员可以删除
        if config.created_by != current_user.id and not current_user.is_admin():
            return jsonify({'success': False, 'error': '您没有权限删除此配置'}), 403

        # 不能删除默认配置
        if config.is_default:
            return jsonify({'success': False, 'error': '不能删除默认配置'}), 400

        # 检查是否有项目正在使用此配置
        from app.models import RiskAssessmentProject
        projects_using_config = RiskAssessmentProject.query.filter_by(weight_config_id=config_id).count()
        if projects_using_config > 0:
            return jsonify({
                'success': False,
                'error': f'此配置正被 {projects_using_config} 个项目使用，无法删除'
            }), 400

        # 删除配置
        config_name = config.config_name
        db.session.delete(config)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'权重配置 "{config_name}" 删除成功'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/applied-weight', methods=['POST'])
@login_required
def api_apply_weight():
    """应用权重配置到当前会话"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '请提供权重数据'}), 400

        # 验证权重数据
        required_fields = ['rainfall_weight', 'drainage_weight', 'elevation_weight', 'gdp_weight', 'population_weight']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'缺少必需字段: {field}'}), 400

        # 验证发生概率权重总和
        probability_total = float(data['rainfall_weight']) + float(data['drainage_weight']) + float(data['elevation_weight'])
        if abs(probability_total - 1.0) > 0.001:
            return jsonify({'success': False, 'error': f'发生概率权重总和必须为1.0，当前为{probability_total:.3f}'}), 400

        # 验证影响程度权重总和
        impact_total = float(data['gdp_weight']) + float(data['population_weight'])
        if abs(impact_total - 1.0) > 0.001:
            return jsonify({'success': False, 'error': f'影响程度权重总和必须为1.0，当前为{impact_total:.3f}'}), 400

        # 将权重配置保存到会话中
        from flask import session
        session['applied_weights'] = {
            'rainfall_weight': float(data['rainfall_weight']),
            'drainage_weight': float(data['drainage_weight']),
            'elevation_weight': float(data['elevation_weight']),
            'gdp_weight': float(data['gdp_weight']),
            'population_weight': float(data['population_weight']),
            'applied_at': datetime.now().isoformat(),
            'applied_by': current_user.id
        }

        return jsonify({
            'success': True,
            'message': '权重配置已应用',
            'applied_weights': session['applied_weights']
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/applied-weight', methods=['GET'])
@login_required
def api_get_applied_weight():
    """获取当前应用的权重配置"""
    try:
        from flask import session
        applied_weights = session.get('applied_weights')

        return jsonify({
            'success': True,
            'applied_weights': applied_weights
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/applied-weight', methods=['DELETE'])
@login_required
def api_clear_applied_weight():
    """清除当前应用的权重配置"""
    try:
        from flask import session
        if 'applied_weights' in session:
            del session['applied_weights']

        return jsonify({
            'success': True,
            'message': '已清除应用的权重配置'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/risk-matrix', methods=['GET'])
@login_required
def api_get_risk_matrix():
    """获取风险矩阵配置"""
    try:
        matrix = risk_service.get_default_risk_matrix()
        if not matrix:
            return jsonify({'success': False, 'error': '未找到风险矩阵配置'}), 404

        # 获取矩阵配置数据
        matrix_config = matrix.get_matrix_config()
        likelihood_levels = matrix.get_likelihood_levels()
        impact_levels = matrix.get_impact_levels()

        # 调试信息
        print(f"Matrix config type: {type(matrix_config)}")
        print(f"Likelihood levels type: {type(likelihood_levels)}")
        print(f"Impact levels type: {type(impact_levels)}")

        # 构建前端显示数据
        matrix_data = RiskMatrixBuilder.build_matrix_json_for_frontend(
            matrix_config,
            likelihood_levels,
            impact_levels
        )

        return jsonify({
            'success': True,
            'matrix_id': matrix.id,
            'matrix_name': matrix.matrix_name,
            'description': matrix.description,
            **matrix_data
        })

    except Exception as e:
        print(f"获取风险矩阵时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/project/<int:project_id>/import', methods=['POST'])
@login_required
def api_import_data_file(project_id):
    """导入数据文件（CSV或Excel）"""
    try:
        project = RiskAssessmentProject.query.get_or_404(project_id)

        # 检查权限
        if project.created_by != current_user.id and not current_user.is_admin():
            return jsonify({'success': False, 'error': '权限不足'}), 403

        # 检查文件
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '请选择文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '请选择文件'}), 400

        # 保存临时文件
        original_filename = file.filename
        filename = secure_filename(original_filename)

        # 如果secure_filename移除了所有字符，使用原始扩展名
        if not filename or '.' not in filename:
            file_ext = os.path.splitext(original_filename)[1].lower()
            filename = f"upload{file_ext}"

        temp_path = os.path.join('temp', f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{filename}")
        os.makedirs(os.path.dirname(temp_path), exist_ok=True)
        file.save(temp_path)

        try:
            # 使用文件导入器处理文件
            importer = FileImporter()
            success, data_list, message = importer.process_file(temp_path)

            print(f"文件处理结果: success={success}, message={message}, data_count={len(data_list) if data_list else 0}")

            if not success:
                print(f"文件处理失败: {message}")
                return jsonify({
                    'success': False,
                    'error': message
                }), 400

            # 导入数据到数据库
            created_data = risk_service.add_assessment_data(project_id, data_list)

            return jsonify({
                'success': True,
                'message': f'成功导入 {len(created_data)} 条数据',
                'imported_count': len(created_data),
                'batch_id': data_list[0].get('import_batch_id') if data_list else None
            })

        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/projects/upload-multi-year', methods=['POST'])
@login_required
def upload_multi_year_file():
    """上传多年份Excel文件"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': '没有选择文件'
            }), 400

        file = request.files['file']
        province_name = request.form.get('province_name', '').strip()

        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '没有选择文件'
            }), 400

        if not province_name:
            return jsonify({
                'success': False,
                'error': '请指定省份名称'
            }), 400

        # 检查文件类型
        filename = file.filename.lower()
        if not (filename.endswith('.xlsx') or filename.endswith('.xls')):
            return jsonify({
                'success': False,
                'error': '只支持Excel文件格式（.xlsx, .xls）'
            }), 400

        # 保存上传的文件到临时目录
        temp_path = os.path.join('temp', f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}")
        os.makedirs(os.path.dirname(temp_path), exist_ok=True)
        file.save(temp_path)

        try:
            # 使用多工作表导入器处理文件
            importer = MultiSheetExcelImporter()
            success, results, message = importer.process_multi_sheet_file(temp_path, province_name)

            print(f"多工作表处理结果: success={success}, message={message}")
            print(f"处理详情: {results}")

            if not success:
                print(f"多工作表处理失败: {message}")
                print(f"错误详情: {results.get('errors', [])}")
                return jsonify({
                    'success': False,
                    'error': message,
                    'details': results.get('errors', [])
                }), 400

            # 创建多年份项目并导入数据
            risk_service = RiskAssessmentService()
            creation_results = risk_service.create_multi_year_projects(
                province_name=province_name,
                years_data=results['years_data'],
                user_id=current_user.id
            )

            return jsonify({
                'success': True,
                'message': f'成功导入 {province_name} 多年份数据',
                'import_summary': {
                    'total_sheets': results['total_sheets'],
                    'processed_sheets': results['processed_sheets'],
                    'failed_sheets': results['failed_sheets'],
                    'total_records': results['summary']['total_records'],
                    'years_processed': results['summary']['years_processed'],
                    'created_projects': creation_results['created_projects'],
                    'failed_projects': creation_results['failed_projects']
                },
                'errors': results.get('errors', [])
            })

        finally:
            # 清理临时文件
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    print(f"已清理临时文件: {temp_path}")
            except Exception as cleanup_error:
                print(f"清理临时文件失败: {cleanup_error}")

    except Exception as e:
        print(f"多年份文件上传处理异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': f'文件处理失败: {str(e)}'
        }), 500





@bp.route('/api/project/<int:project_id>/data/<int:data_id>', methods=['DELETE'])
@login_required
def api_delete_assessment_data(project_id, data_id):
    """删除评估数据"""
    try:
        project = RiskAssessmentProject.query.get_or_404(project_id)

        # 检查权限
        if project.created_by != current_user.id and not current_user.is_admin():
            return jsonify({'success': False, 'error': '权限不足'}), 403

        # 删除数据
        data = RiskAssessmentData.query.filter_by(id=data_id, project_id=project_id).first()
        if not data:
            return jsonify({'success': False, 'error': '数据不存在'}), 404

        # 删除相关的计算结果
        RiskCalculationResult.query.filter_by(data_id=data_id).delete()

        # 删除数据
        db.session.delete(data)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '数据删除成功'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/project/<int:project_id>/data', methods=['GET'])
@login_required
def api_get_assessment_data(project_id):
    """获取项目的评估数据"""
    try:
        project = RiskAssessmentProject.query.get_or_404(project_id)

        # 检查权限
        if project.created_by != current_user.id and not current_user.is_admin():
            return jsonify({'success': False, 'error': '权限不足'}), 403

        data_list = RiskAssessmentData.query.filter_by(project_id=project_id).all()

        return jsonify({
            'success': True,
            'project_id': project_id,
            'data_count': len(data_list),
            'data': [data.to_dict() for data in data_list]
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
