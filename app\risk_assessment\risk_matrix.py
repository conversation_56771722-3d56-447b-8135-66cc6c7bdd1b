"""
风险矩阵处理器
实现5×5风险矩阵的构建和风险等级判定
"""
from typing import Dict, List, Optional, Tuple
from app.models import RiskMatrix
import json


class RiskMatrixProcessor:
    """风险矩阵处理器"""
    
    def __init__(self, risk_matrix: RiskMatrix):
        """
        初始化风险矩阵处理器
        
        Args:
            risk_matrix: 风险矩阵配置对象
        """
        self.risk_matrix = risk_matrix
        self.matrix_config = risk_matrix.get_matrix_config()
        self.likelihood_levels = risk_matrix.get_likelihood_levels()
        self.impact_levels = risk_matrix.get_impact_levels()
    
    def determine_likelihood_level(self, likelihood_probability: float) -> str:
        """
        根据发生概率确定发生概率等级

        Args:
            likelihood_probability: 发生概率 (0-1)

        Returns:
            发生概率等级 ('A', 'B', 'C', 'D', 'E')
        """
        if likelihood_probability < 0.15:
            return 'A'  # 极低 [0.00, 0.15)
        elif likelihood_probability < 0.35:
            return 'B'  # 低 [0.15, 0.35)
        elif likelihood_probability < 0.60:
            return 'C'  # 中等 [0.35, 0.60)
        elif likelihood_probability < 0.85:
            return 'D'  # 高 [0.60, 0.85)
        else:
            return 'E'  # 极高 [0.85, 1.00]
    
    def determine_impact_level(self, impact_degree: float, economic_value: float = None) -> str:
        """
        根据影响程度得分确定影响程度等级

        Args:
            impact_degree: 影响程度得分 (0-1)
            economic_value: 经济价值（可选，用于辅助判断）

        Returns:
            影响程度等级 ('轻微', '较小', '中等', '严重', '灾难性')
        """
        # 基于影响程度得分确定等级
        if impact_degree < 0.10:
            return '轻微'  # [0.00, 0.10)
        elif impact_degree < 0.30:
            return '较小'  # [0.10, 0.30)
        elif impact_degree < 0.50:
            return '中等'  # [0.30, 0.50)
        elif impact_degree < 0.75:
            return '严重'  # [0.50, 0.75)
        else:
            return '灾难性'  # [0.75, 1.00]
    
    def get_final_risk_level(self, likelihood_level: str, impact_level: str) -> str:
        """
        根据发生概率等级和影响程度等级确定最终风险等级
        
        Args:
            likelihood_level: 发生概率等级
            impact_level: 影响程度等级
            
        Returns:
            最终风险等级
        """
        if (self.matrix_config and 
            likelihood_level in self.matrix_config and 
            impact_level in self.matrix_config[likelihood_level]):
            return self.matrix_config[likelihood_level][impact_level]
        
        # 如果矩阵配置不存在，使用默认逻辑
        return self._get_default_risk_level(likelihood_level, impact_level)
    
    def _get_default_risk_level(self, likelihood_level: str, impact_level: str) -> str:
        """
        默认的风险等级判定逻辑
        
        Args:
            likelihood_level: 发生概率等级
            impact_level: 影响程度等级
            
        Returns:
            风险等级
        """
        # 基于用户提供的5×5风险矩阵表
        default_matrix = {
            'A': {  # 极低概率 [0.00, 0.15)
                '轻微': '极低风险',
                '较小': '极低风险',
                '中等': '低风险',
                '严重': '低风险',
                '灾难性': '中风险'
            },
            'B': {  # 低概率 [0.15, 0.35)
                '轻微': '极低风险',
                '较小': '低风险',
                '中等': '低风险',
                '严重': '中风险',
                '灾难性': '高风险'
            },
            'C': {  # 中等概率 [0.35, 0.60)
                '轻微': '低风险',
                '较小': '中风险',
                '中等': '中风险',
                '严重': '高风险',
                '灾难性': '极高风险'
            },
            'D': {  # 高概率 [0.60, 0.85)
                '轻微': '中风险',
                '较小': '高风险',
                '中等': '高风险',
                '严重': '极高风险',
                '灾难性': '极高风险'
            },
            'E': {  # 极高概率 [0.85, 1.00]
                '轻微': '高风险',
                '较小': '极高风险',
                '中等': '极高风险',
                '严重': '极高风险',
                '灾难性': '极高风险'
            }
        }
        
        return default_matrix.get(likelihood_level, {}).get(impact_level, '中风险')
    
    def process_risk_assessment(self, likelihood_probability: float, impact_degree: float,
                              economic_value: float = None) -> Dict:
        """
        处理风险评估，返回完整的风险等级判定结果

        Args:
            likelihood_probability: 发生概率 (0-1)
            impact_degree: 影响程度得分 (0-1)
            economic_value: 经济价值（可选）

        Returns:
            包含风险等级判定结果的字典
        """
        # 确定发生概率等级
        likelihood_level = self.determine_likelihood_level(likelihood_probability)

        # 确定影响程度等级
        impact_level = self.determine_impact_level(impact_degree, economic_value)

        # 确定最终风险等级
        final_risk_level = self.get_final_risk_level(likelihood_level, impact_level)

        return {
            'likelihood_level': likelihood_level,
            'impact_level': impact_level,
            'final_risk_level': final_risk_level,
            'likelihood_description': self._get_likelihood_description(likelihood_level),
            'impact_description': self._get_impact_description(impact_level),
            'risk_level_color': self._get_risk_level_color(final_risk_level),
            'risk_level_badge_class': self._get_risk_level_badge_class(final_risk_level)
        }
    
    def _get_likelihood_description(self, likelihood_level: str) -> str:
        """获取发生概率等级描述"""
        if self.likelihood_levels and likelihood_level in self.likelihood_levels:
            return self.likelihood_levels[likelihood_level].get('name', likelihood_level)
        
        descriptions = {
            'A': '极低',
            'B': '低',
            'C': '中等',
            'D': '高',
            'E': '极高'
        }
        return descriptions.get(likelihood_level, likelihood_level)
    
    def _get_impact_description(self, impact_level: str) -> str:
        """获取影响程度等级描述"""
        if self.impact_levels and impact_level in self.impact_levels:
            return self.impact_levels[impact_level].get('description', impact_level)
        
        return impact_level
    
    def _get_risk_level_color(self, risk_level: str) -> str:
        """获取风险等级对应的颜色"""
        color_map = {
            '极低风险': '#d4edda',   # 浅绿色
            '低风险': '#28a745',     # 绿色
            '中风险': '#ffc107',     # 黄色
            '高风险': '#fd7e14',     # 橙色
            '极高风险': '#dc3545'    # 红色
        }
        return color_map.get(risk_level, '#6c757d')
    
    def _get_risk_level_badge_class(self, risk_level: str) -> str:
        """获取风险等级对应的Bootstrap徽章样式类"""
        class_map = {
            '极低风险': 'light',
            '低风险': 'success',
            '中风险': 'warning',
            '高风险': 'danger',
            '极高风险': 'danger'
        }
        return class_map.get(risk_level, 'secondary')
    
    def get_matrix_visualization_data(self) -> Dict:
        """
        获取用于前端可视化的矩阵数据
        
        Returns:
            包含矩阵可视化数据的字典
        """
        if not self.matrix_config:
            return {}
        
        # 构建可视化数据
        visualization_data = {
            'matrix': self.matrix_config,
            'likelihood_levels': self.likelihood_levels or {},
            'impact_levels': self.impact_levels or {},
            'risk_level_colors': {
                '低风险': '#28a745',
                '中风险': '#ffc107', 
                '高风险': '#fd7e14',
                '极高风险': '#dc3545'
            }
        }
        
        return visualization_data
    
    def validate_matrix_config(self) -> Tuple[bool, str]:
        """
        验证矩阵配置是否有效
        
        Returns:
            (是否有效, 错误信息)
        """
        if not self.matrix_config:
            return False, "矩阵配置不存在"
        
        # 检查矩阵结构
        expected_likelihood_levels = ['A', 'B', 'C', 'D', 'E']
        expected_impact_levels = ['轻微', '较小', '中等', '严重', '灾难性']
        
        for likelihood in expected_likelihood_levels:
            if likelihood not in self.matrix_config:
                return False, f"缺少发生概率等级: {likelihood}"
            
            for impact in expected_impact_levels:
                if impact not in self.matrix_config[likelihood]:
                    return False, f"缺少影响程度等级: {likelihood}-{impact}"
        
        return True, "矩阵配置有效"
