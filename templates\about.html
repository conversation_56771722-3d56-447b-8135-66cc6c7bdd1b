{% extends "base.html" %}

{% block title %}关于我们 - {{ super() }}{% endblock %}

{% block content %}
<div class="container">
    <!-- 页面标题 -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold mb-3">关于我们</h1>
            <p class="lead text-muted">了解城市内涝风险评估系统</p>
        </div>
    </div>
    
    <!-- 系统介绍 -->
    <div class="row mb-5">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow">
                <div class="card-body p-5">
                    <h3 class="mb-4">
                        <i class="fas fa-info-circle text-primary me-2"></i>系统简介
                    </h3>
                    <p class="mb-4">
                        城市内涝风险评估系统是一个基于GIS技术的智能化城市内涝风险评估平台。
                        系统采用Python 3.8.10后端和HTML5前端技术栈，实现了从数据管理、洪水仿真计算、风险评估到可视化展示的完整工作流程。
                    </p>
                    <p class="mb-4">
                        本系统旨在为城市防洪减灾提供科学的决策支持，通过精确的洪水仿真计算和实时风险评估，
                        帮助相关部门及时发现潜在风险，制定有效的防灾减灾措施。
                    </p>
                    <p class="mb-0">
                        系统具有操作简便、功能完善、可扩展性强等特点，适用于各级政府部门、科研院所、
                        工程咨询公司等单位的洪水风险管理工作。
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 技术架构 -->
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="text-center mb-4">
                <i class="fas fa-cogs text-primary me-2"></i>技术架构
            </h3>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <div class="mb-3">
                                <i class="fas fa-desktop fa-3x text-primary"></i>
                            </div>
                            <h5 class="card-title">前端技术</h5>
                            <ul class="list-unstyled">
                                <li>HTML5 + CSS3</li>
                                <li>JavaScript ES6+</li>
                                <li>Bootstrap 5</li>
                                <li>Leaflet地图引擎</li>
                                <li>ECharts图表库</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <div class="mb-3">
                                <i class="fas fa-server fa-3x text-success"></i>
                            </div>
                            <h5 class="card-title">后端技术</h5>
                            <ul class="list-unstyled">
                                <li>Python 3.8.10</li>
                                <li>Flask Web框架</li>
                                <li>SQLAlchemy ORM</li>
                                <li>Flask-Login认证</li>
                                <li>Celery异步任务</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <div class="mb-3">
                                <i class="fas fa-database fa-3x text-info"></i>
                            </div>
                            <h5 class="card-title">数据存储</h5>
                            <ul class="list-unstyled">
                                <li>SQLite数据库</li>
                                <li>SpatiaLite扩展</li>
                                <li>GDAL/OGR处理</li>
                                <li>空间索引优化</li>
                                <li>数据备份机制</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 核心算法 -->
    <div class="row mb-5">
        <div class="col-lg-10 mx-auto">
            <div class="card shadow">
                <div class="card-body p-5">
                    <h3 class="mb-4">
                        <i class="fas fa-calculator text-primary me-2"></i>核心算法
                    </h3>
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <h5>
                                <i class="fas fa-chart-line text-success me-2"></i>SCS-CN径流计算
                            </h5>
                            <p class="text-muted">
                                基于美国土壤保持局开发的SCS-CN方法，根据土地利用类型、土壤类型和前期含水量条件，
                                自动计算径流系数，实现精确的降雨径流模拟。
                            </p>
                        </div>
                        <div class="col-md-6 mb-4">
                            <h5>
                                <i class="fas fa-water text-info me-2"></i>二维水动力学模拟
                            </h5>
                            <p class="text-muted">
                                采用有限差分方法求解浅水方程，模拟洪水在二维空间中的演进过程，
                                提供水深、流速等关键水文参数的时空分布。
                            </p>
                        </div>
                        <div class="col-md-6 mb-4">
                            <h5>
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>多因子风险评估
                            </h5>
                            <p class="text-muted">
                                综合考虑危险性、脆弱性和暴露性三个维度，构建多因子风险评估模型，
                                量化评估不同区域的洪水风险等级。
                            </p>
                        </div>
                        <div class="col-md-6 mb-4">
                            <h5>
                                <i class="fas fa-bell text-danger me-2"></i>智能预警算法
                            </h5>
                            <p class="text-muted">
                                基于历史数据和实时监测信息，建立动态预警阈值，
                                实现自动化的洪水风险预警和预报功能。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
