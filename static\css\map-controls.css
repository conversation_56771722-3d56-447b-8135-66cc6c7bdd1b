/**
 * 地图控件样式 - 指北针和比例尺
 * 与项目整体风格保持一致的控件样式
 */

/* 指北针控件样式 */
.leaflet-control-compass {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    padding: 8px;
    margin: 10px;
    transition: all 0.3s ease;
    cursor: default;
    user-select: none;
}

.leaflet-control-compass:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
}

.compass-container {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.compass-needle {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.compass-north {
    font-size: 12px;
    font-weight: bold;
    color: #dc3545;
    margin-bottom: 2px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.compass-arrow {
    font-size: 20px;
    color: #dc3545;
    line-height: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* 比例尺控件样式优化 */
.leaflet-control-scale {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    padding: 4px 8px;
    margin: 10px;
    transition: all 0.3s ease;
}

.leaflet-control-scale:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.leaflet-control-scale-line {
    border: 2px solid #333;
    border-top: none;
    color: #333;
    font-size: 11px;
    font-weight: 600;
    line-height: 1.2;
    padding: 2px 5px 1px;
    background: transparent;
    white-space: nowrap;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* 响应式设计 */
/* 平板设备 */
@media (max-width: 1024px) {
    .leaflet-control-compass {
        margin: 8px;
    }

    .leaflet-control-scale,
    .leaflet-control-advanced-scale {
        margin: 8px;
    }
}

/* 手机横屏 */
@media (max-width: 768px) {
    .leaflet-control-compass {
        margin: 5px;
        padding: 6px;
    }

    .compass-container {
        width: 35px;
        height: 35px;
    }

    .compass-north {
        font-size: 10px;
    }

    .compass-arrow {
        font-size: 18px;
    }

    .leaflet-control-scale,
    .leaflet-control-advanced-scale {
        margin: 5px;
        padding: 3px 6px;
    }

    .leaflet-control-scale-line,
    .advanced-scale-bar {
        font-size: 10px;
        padding: 1px 4px;
    }

    .advanced-scale-info {
        font-size: 9px;
    }
}

/* 手机竖屏 */
@media (max-width: 480px) {
    .leaflet-control-compass {
        margin: 3px;
        padding: 4px;
    }

    .compass-container {
        width: 30px;
        height: 30px;
    }

    .compass-north {
        font-size: 9px;
    }

    .compass-arrow {
        font-size: 16px;
    }

    .leaflet-control-scale,
    .leaflet-control-advanced-scale {
        margin: 3px;
        padding: 2px 4px;
    }

    .leaflet-control-scale-line,
    .advanced-scale-bar {
        font-size: 9px;
        padding: 1px 3px;
    }

    .advanced-scale-info {
        font-size: 8px;
    }
}

/* 超小屏幕设备 */
@media (max-width: 320px) {
    .leaflet-control-compass {
        margin: 2px;
        padding: 3px;
    }

    .compass-container {
        width: 25px;
        height: 25px;
    }

    .compass-north {
        font-size: 8px;
    }

    .compass-arrow {
        font-size: 14px;
    }

    .leaflet-control-scale,
    .leaflet-control-advanced-scale {
        margin: 2px;
        padding: 1px 3px;
        max-width: 100px;
    }

    .leaflet-control-scale-line,
    .advanced-scale-bar {
        font-size: 8px;
        padding: 1px 2px;
    }

    .advanced-scale-info {
        font-size: 7px;
    }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
    .leaflet-control-compass,
    .leaflet-control-scale,
    .leaflet-control-advanced-scale {
        margin: 3px;
    }
}

/* 深色主题适配（如果项目有深色主题） */
@media (prefers-color-scheme: dark) {
    .leaflet-control-compass {
        background: rgba(33, 37, 41, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .leaflet-control-compass:hover {
        background: rgba(33, 37, 41, 1);
    }
    
    .leaflet-control-scale {
        background: rgba(33, 37, 41, 0.95);
    }
    
    .leaflet-control-scale:hover {
        background: rgba(33, 37, 41, 1);
    }
    
    .leaflet-control-scale-line {
        border-color: #fff;
        color: #fff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    }
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
    .leaflet-control-compass {
        border-width: 3px;
        border-color: #000;
    }
    
    .compass-north,
    .compass-arrow {
        color: #000;
        text-shadow: none;
    }
    
    .leaflet-control-scale-line {
        border-width: 3px;
        border-color: #000;
        color: #000;
        text-shadow: none;
    }
}

/* 动画效果 */
@keyframes compassPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.leaflet-control-compass:active {
    animation: compassPulse 0.3s ease;
}

/* 高级比例尺控件样式 */
.leaflet-control-advanced-scale {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    padding: 6px 10px;
    margin: 10px;
    transition: all 0.3s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.leaflet-control-advanced-scale:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.advanced-scale-bar {
    border: 2px solid #333;
    border-top: none;
    color: #333;
    font-size: 11px;
    font-weight: 600;
    line-height: 1.2;
    padding: 2px 5px 1px;
    background: transparent;
    white-space: nowrap;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    margin-bottom: 4px;
}

.advanced-scale-info {
    font-size: 10px;
    color: #666;
    text-align: center;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* 自定义比例尺控件样式 */
.custom-scale-control {
    border-radius: 6px !important;
    overflow: hidden;
}

/* 确保控件在全屏模式下也能正常显示 */
.fullscreen-map .leaflet-control-compass,
.fullscreen-map .leaflet-control-scale,
.fullscreen-map .leaflet-control-advanced-scale {
    z-index: 1000;
}

/* 图片地图控件样式 - 用于土地利用图片等静态图片 */
.image-map-controls {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.image-map-compass {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    padding: 8px;
    transition: all 0.3s ease;
    cursor: default;
    user-select: none;
    pointer-events: auto;
}

.image-map-compass:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
}

.image-map-scale {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    padding: 6px 10px;
    transition: all 0.3s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    pointer-events: auto;
}

.image-map-scale:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.image-scale-bar {
    border: 2px solid #333;
    border-top: none;
    color: #333;
    font-size: 11px;
    font-weight: 600;
    line-height: 1.2;
    padding: 2px 5px 1px;
    background: transparent;
    white-space: nowrap;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    margin-bottom: 4px;
    min-width: 60px;
    text-align: center;
}

.image-scale-info {
    font-size: 10px;
    color: #666;
    text-align: center;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* 深色主题下的高级比例尺样式 */
@media (prefers-color-scheme: dark) {
    .leaflet-control-advanced-scale {
        background: rgba(33, 37, 41, 0.95);
    }

    .leaflet-control-advanced-scale:hover {
        background: rgba(33, 37, 41, 1);
    }

    .advanced-scale-bar {
        border-color: #fff;
        color: #fff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    }

    .advanced-scale-info {
        color: #ccc;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    }
}
