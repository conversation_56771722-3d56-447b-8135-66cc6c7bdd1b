{% extends "base.html" %}

{% block title %}用户工作台 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-user-circle me-2"></i>用户工作台
                    </h2>
                    <p class="text-muted mb-0">欢迎回来，{{ current_user.username }}！</p>
                </div>
                <div>
                    <span class="badge bg-primary">普通用户</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户功能区域 -->
    <div class="row">
        <!-- 核心功能模块操作 -->
        <div class="col-lg-9 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tools me-2"></i>功能模块操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- 数据管理模块 - 用户操作 -->
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-primary">
                                <div class="card-body py-3">
                                    <h6 class="text-primary mb-2">
                                        <i class="fas fa-database me-1"></i>数据管理模块
                                    </h6>
                                    <p class="text-muted small mb-3">数据导入、处理和存储管理</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('main.data_import') }}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-upload me-1"></i>数据导入
                                        </a>
                                        <a href="{{ url_for('main.data_processing') }}" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-cogs me-1"></i>数据处理
                                        </a>
                                        <a href="{{ url_for('main.data_storage') }}" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-hdd me-1"></i>数据存储
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- 可视化模块 - 用户操作 -->
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-info">
                                <div class="card-body py-3">
                                    <h6 class="text-info mb-2">
                                        <i class="fas fa-chart-line me-1"></i>可视化模块
                                    </h6>
                                    <p class="text-muted small mb-3">地图展示、图表分析和图层管理</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('visualization.map_view') }}" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-map me-1"></i>地图展示
                                        </a>
                                        <a href="{{ url_for('visualization.charts_view') }}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-chart-bar me-1"></i>图表分析
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 风险评估模块 - 用户操作（限制功能） -->
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-warning">
                                <div class="card-body py-3">
                                    <h6 class="text-warning mb-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i>风险评估模块
                                    </h6>
                                    <p class="text-muted small mb-3">查看风险评估信息</p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('risk_assessment.index') }}" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-layer-group me-1"></i>风险评估首页
                                        </a>
                                        <a href="{{ url_for('risk_assessment.projects') }}" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-project-diagram me-1"></i>项目管理
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 个人信息与使用统计 -->
        <div class="col-lg-3 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user me-2"></i>个人信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="mb-3">
                            <i class="fas fa-user-circle fa-4x text-primary"></i>
                        </div>
                        <h5>{{ current_user.username }}</h5>
                        <p class="text-muted">{{ current_user.email }}</p>
                        <span class="badge bg-primary">普通用户</span>
                        <p class="text-muted mt-2">
                            <small>注册时间: {{ current_user.created_at.strftime('%Y-%m-%d') }}</small>
                        </p>
                    </div>

                    <!-- 使用统计 -->
                    <div class="mb-3">
                        <h6 class="text-muted mb-3">使用统计</h6>
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="text-primary mb-0">0</h6>
                                    <small class="text-muted">数据集</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="text-success mb-0">0</h6>
                                    <small class="text-muted">计算任务</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h6 class="text-info mb-0">0</h6>
                                <small class="text-muted">地图项目</small>
                            </div>
                        </div>
                    </div>

                    <!-- 编辑资料按钮 -->
                    <div class="d-grid">
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                            <i class="fas fa-edit me-1"></i>编辑资料
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

</div>

<!-- 编辑资料模态框 -->
<div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editProfileModalLabel">
                    <i class="fas fa-user-edit me-2"></i>编辑个人资料
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editProfileForm">
                <div class="modal-body">
                    <div class="row">
                        <!-- 基本信息 -->
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">基本信息</h6>
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" name="username" value="{{ current_user.username }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱地址</label>
                                <input type="email" class="form-control" id="email" name="email" value="{{ current_user.email }}" required>
                            </div>
                        </div>

                        <!-- 密码修改 -->
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">密码修改</h6>
                            <div class="mb-3">
                                <label for="current_password" class="form-label">当前密码</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" placeholder="修改密码时必填">
                            </div>
                            <div class="mb-3">
                                <label for="new_password" class="form-label">新密码</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" placeholder="不修改请留空">
                            </div>
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">确认新密码</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="不修改请留空">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>保存修改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.text-xs {
    font-size: 0.7rem;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

/* 时间线样式 */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 5px;
    font-size: 13px;
    color: #6c757d;
}
</style>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 更新在线用户统计
    function updateOnlineUsers() {
        fetch('/api/online-count')
            .then(response => response.json())
            .then(data => {
                $('#online-users-count').text(data.total);
            })
            .catch(error => {
                console.error('更新在线用户数失败:', error);
            });
    }

    // 每10秒更新一次在线用户数
    setInterval(updateOnlineUsers, 10000);
    updateOnlineUsers();

    // 处理编辑资料表单提交
    $('#editProfileForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        // 验证密码
        if (data.new_password && data.new_password !== data.confirm_password) {
            showAlert('新密码和确认密码不匹配！', 'danger');
            return;
        }

        // 如果要修改密码，必须输入当前密码
        if (data.new_password && !data.current_password) {
            showAlert('修改密码时必须输入当前密码！', 'danger');
            return;
        }

        // 显示加载状态
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>保存中...').prop('disabled', true);

        // 发送更新请求
        fetch('/api/profile/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showAlert('个人资料更新成功！', 'success');
                $('#editProfileModal').modal('hide');

                // 更新页面显示的用户信息
                if (data.username !== '{{ current_user.username }}') {
                    location.reload(); // 如果用户名改变，刷新页面
                }
            } else {
                showAlert(result.message || '更新失败，请重试！', 'danger');
            }
        })
        .catch(error => {
            console.error('更新个人资料失败:', error);
            showAlert('网络错误，请重试！', 'danger');
        })
        .finally(() => {
            // 恢复按钮状态
            submitBtn.html(originalText).prop('disabled', false);
        });
    });
});

// 显示提示信息
function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    // 移除现有的提示
    $('.alert').remove();

    // 在页面顶部添加新提示
    $('body').prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 3000);
}

// 显示即将推出功能的提示
function showComingSoon(featureName) {
    showAlert(`${featureName}功能即将推出，敬请期待！`, 'info');
}
</script>
{% endblock %}
