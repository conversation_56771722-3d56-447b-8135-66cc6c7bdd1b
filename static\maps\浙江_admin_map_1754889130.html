<!DOCTYPE html>
<html>
<head>
<style>
/* 地图控件样式 - 指北针和比例尺 */
.map-control-compass {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    padding: 8px;
    transition: all 0.3s ease;
    cursor: default;
    user-select: none;
    z-index: 1000;
}

.map-control-compass:hover {
    background: rgba(255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
}

.compass-container {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.compass-needle {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.compass-north {
    font-size: 12px;
    font-weight: bold;
    color: #dc3545;
    margin-bottom: 2px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.compass-arrow {
    font-size: 20px;
    color: #dc3545;
    line-height: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.map-control-scale {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    padding: 6px 10px;
    transition: all 0.3s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    z-index: 1000;
}

.map-control-scale:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.scale-bar {
    border: 2px solid #333;
    border-top: none;
    color: #333;
    font-size: 11px;
    font-weight: 600;
    line-height: 1.2;
    padding: 2px 5px 1px;
    background: transparent;
    white-space: nowrap;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    margin-bottom: 4px;
    min-width: 60px;
    max-width: 200px;
    text-align: center;
    transition: width 0.3s ease;
    position: relative;
}

.scale-info {
    font-size: 10px;
    color: #666;
    text-align: center;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}
</style>

    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/maps/zhejiang.js"></script>

    
</head>
<body >
    <div id="b0ea9f758ebf44a9bda660ef5f8ae8ca" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_b0ea9f758ebf44a9bda660ef5f8ae8ca = echarts.init(
            document.getElementById('b0ea9f758ebf44a9bda660ef5f8ae8ca'), 'white', {renderer: 'canvas'});
        var option_b0ea9f758ebf44a9bda660ef5f8ae8ca = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "map",
            "name": "\u884c\u653f\u533a\u5212",
            "label": {
                "show": true,
                "color": "#2c3e50",
                "margin": 8,
                "fontSize": 10,
                "fontWeight": "bold",
                "valueAnimation": false
            },
            "map": "\u6d59\u6c5f",
            "data": [
                {
                    "name": "\u676d\u5dde\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5b81\u6ce2\u5e02",
                    "value": 1
                },
                {
                    "name": "\u6e29\u5dde\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5609\u5174\u5e02",
                    "value": 1
                },
                {
                    "name": "\u6e56\u5dde\u5e02",
                    "value": 1
                },
                {
                    "name": "\u7ecd\u5174\u5e02",
                    "value": 1
                },
                {
                    "name": "\u91d1\u534e\u5e02",
                    "value": 1
                },
                {
                    "name": "\u8862\u5dde\u5e02",
                    "value": 1
                },
                {
                    "name": "\u821f\u5c71\u5e02",
                    "value": 1
                },
                {
                    "name": "\u53f0\u5dde\u5e02",
                    "value": 1
                },
                {
                    "name": "\u4e3d\u6c34\u5e02",
                    "value": 1
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1.2,
            "zlevel": 0,
            "z": 2,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "mapValueCalculation": "sum",
            "showLegendSymbol": true,
            "itemStyle": {
                "color": "#a8e6cf",
                "borderColor": "#2c3e50",
                "borderWidth": 2
            },
            "emphasis": {},
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u884c\u653f\u533a\u5212"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": "{b}<br/>\u884c\u653f\u7ea7\u522b: \u5730\u7ea7\u5e02",
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u6d59\u6c5f\u7701\u884c\u653f\u533a\u5212\u56fe",
            "target": "blank",
            "subtext": "\u5e02\u7ea7\u884c\u653f\u533a\u5212\u8fb9\u754c",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "color": "#2c3e50",
                "fontWeight": "bold",
                "fontSize": 18
            },
            "subtextStyle": {
                "color": "#7f8c8d",
                "fontSize": 12
            }
        }
    ],
    "visualMap": {
        "show": false,
        "type": "continuous",
        "min": 0,
        "max": 1,
        "inRange": {
            "color": [
                "#a8e6cf",
                "#a8e6cf"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "hoverLink": true,
        "orient": "vertical",
        "padding": 5,
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 140,
        "borderWidth": 0
    },
    "dataZoom": [
        {
            "show": false,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        }
    ]
};
        chart_b0ea9f758ebf44a9bda660ef5f8ae8ca.setOption(option_b0ea9f758ebf44a9bda660ef5f8ae8ca);
    </script>

<div class="map-control-compass" title="指北针 - 北向指示器">
    <div class="compass-container">
        <div class="compass-needle">
            <div class="compass-north">N</div>
            <div class="compass-arrow">↑</div>
        </div>
    </div>
</div>

<div class="map-control-scale" title="比例尺">
    <div class="scale-bar" id="scaleBar">50 km</div>
    <div class="scale-info" id="scaleInfo">缩放级别: 1.0x</div>
</div>

<script>
// 全局变量跟踪缩放状态
window.currentMapZoom = 1;
window.updateCounter = 0;

// 强制更新比例尺 - 不检查缩放变化
function forceUpdateScale() {
    try {
        var scaleBar = document.getElementById('scaleBar');
        var scaleInfo = document.getElementById('scaleInfo');

        if (!scaleBar || !scaleInfo) {
            console.log('比例尺元素未找到');
            return;
        }

        // 获取ECharts实例
        var chartDom = document.getElementById('main');
        if (!chartDom) {
            console.log('图表容器未找到');
            return;
        }

        var myChart = echarts.getInstanceByDom(chartDom);
        if (!myChart) {
            console.log('ECharts实例未找到');
            return;
        }

        // 获取当前缩放级别和地图配置
        var option = myChart.getOption();
        var geo = option.geo && option.geo[0];
        if (!geo) {
            console.log('地理配置未找到');
            return;
        }

        // 获取基础缩放级别
        var baseZoom = geo.zoom || 1;
        var mapType = geo.map || 'china';

        // 检测dataZoom缩放
        var dataZoomScale = 1;
        if (option.dataZoom && option.dataZoom.length > 0) {
            var dataZoom = option.dataZoom[0];
            if (dataZoom.start !== undefined && dataZoom.end !== undefined) {
                // 计算dataZoom的缩放比例
                var range = dataZoom.end - dataZoom.start;
                if (range > 0 && range < 100) {
                    dataZoomScale = 100 / range;
                }
            }
        }

        // 综合缩放级别
        var totalZoom = baseZoom * dataZoomScale;

        window.updateCounter++;
        console.log('强制更新比例尺 #' + window.updateCounter + ' - 地图类型:', mapType,
                   '基础缩放:', baseZoom, 'dataZoom缩放:', dataZoomScale, '总缩放:', totalZoom);
        window.currentMapZoom = totalZoom;

        // 使用简化的比例尺计算方法
        var scaleData = calculateSimpleScale(mapType, totalZoom, chartDom);

        scaleBar.textContent = scaleData.scaleText;
        scaleInfo.textContent = scaleData.infoText;

        // 动态调整比例尺条的宽度
        var scaleWidth = Math.max(60, Math.min(150, scaleData.pixelWidth));
        scaleBar.style.width = scaleWidth + 'px';

        console.log('比例尺已更新:', scaleData.scaleText, '信息:', scaleData.infoText);

    } catch (error) {
        console.log('更新比例尺时出错:', error);
    }
}

// 动态比例尺功能 - 采用土地利用图的简化方法
function updateScale() {
    forceUpdateScale();
}

// 简化的比例尺计算方法 - 采用土地利用图的方法
function calculateSimpleScale(mapType, zoom, chartDom) {
    try {
        // 获取图表容器的像素尺寸
        var containerWidth = chartDom.offsetWidth;
        var containerHeight = chartDom.offsetHeight;

        // 不同地图类型的地理范围（经纬度）
        var mapBounds = getMapBounds(mapType);
        if (!mapBounds) {
            return {
                scaleText: '50 km',
                infoText: '1:5000000 (缩放: 1.0x)',
                pixelWidth: 100
            };
        }

        // 计算地理宽度（经度差转换为公里）
        var geoWidth = Math.abs(mapBounds.east - mapBounds.west);
        var geoHeight = Math.abs(mapBounds.north - mapBounds.south);

        // 考虑纬度影响，使用平均纬度
        var avgLat = (mapBounds.north + mapBounds.south) / 2;
        var latFactor = Math.cos(avgLat * Math.PI / 180);

        // 更精确的经纬度到公里转换
        var realWidthKm = geoWidth * 111.32 * latFactor;  // 更精确的转换系数
        var realHeightKm = geoHeight * 110.54;  // 纬度1度约等于110.54公里

        // 使用宽度来计算比例尺，因为通常地图是横向显示
        var realDistanceKm = realWidthKm;
        var pixelDistance = containerWidth;

        // 计算每像素代表的公里数，考虑缩放级别
        var kmPerPixel = realDistanceKm / (pixelDistance * zoom);

        // 生成合适的比例尺长度（100像素）
        var scalePixels = 100;
        var scaleKm = kmPerPixel * scalePixels;

        // 调整到合适的数值，避免显示过大的数值
        var displayKm;
        var scaleText;

        if (scaleKm >= 500) {
            // 对于大于500km的，按500的倍数取整
            displayKm = Math.round(scaleKm / 500) * 500;
            scaleText = displayKm + ' km';
        } else if (scaleKm >= 100) {
            // 对于100-500km的，按100的倍数取整
            displayKm = Math.round(scaleKm / 100) * 100;
            scaleText = displayKm + ' km';
        } else if (scaleKm >= 50) {
            // 对于50-100km的，按50的倍数取整
            displayKm = Math.round(scaleKm / 50) * 50;
            scaleText = displayKm + ' km';
        } else if (scaleKm >= 10) {
            // 对于10-50km的，按10的倍数取整
            displayKm = Math.round(scaleKm / 10) * 10;
            scaleText = displayKm + ' km';
        } else if (scaleKm >= 1) {
            // 对于1-10km的，按1km取整
            displayKm = Math.round(scaleKm);
            scaleText = displayKm + ' km';
        } else {
            // 小于1km的显示为米
            displayKm = scaleKm;
            var meters = Math.round(displayKm * 1000);
            if (meters >= 500) {
                meters = Math.round(meters / 100) * 100;
            } else if (meters >= 100) {
                meters = Math.round(meters / 50) * 50;
            } else if (meters >= 10) {
                meters = Math.round(meters / 10) * 10;
            }
            scaleText = meters + ' m';
        }

        var infoText = '1:' + Math.round(displayKm * 100000) + ' (缩放: ' + zoom.toFixed(1) + 'x)';

        return {
            scaleText: scaleText,
            infoText: infoText,
            pixelWidth: scalePixels
        };

    } catch (error) {
        console.log('计算比例尺时出错:', error);
        return {
            scaleText: '100 km',
            infoText: '1:10000000 (缩放: 1.0x)',
            pixelWidth: 100
        };
    }
}

// 获取不同地图类型的地理边界（更精确的边界数据）
function getMapBounds(mapType) {
    var bounds = {
        'china': { north: 53.56, south: 18.15, east: 134.77, west: 73.50 },
        '河南': { north: 36.37, south: 31.38, east: 116.65, west: 110.35 },
        '北京': { north: 41.05, south: 39.44, east: 117.51, west: 115.42 },
        '天津': { north: 40.25, south: 38.60, east: 118.00, west: 116.70 },
        '上海': { north: 31.87, south: 30.68, east: 122.20, west: 120.85 },
        '重庆': { north: 32.20, south: 28.16, east: 110.18, west: 105.30 },
        '广东': { north: 25.32, south: 20.22, east: 117.22, west: 109.67 },
        '广西': { north: 26.38, south: 20.90, east: 112.05, west: 104.45 },
        '江苏': { north: 35.13, south: 30.75, east: 121.95, west: 116.36 },
        '浙江': { north: 31.17, south: 27.02, east: 123.25, west: 118.01 },
        '山东': { north: 38.41, south: 34.38, east: 122.72, west: 114.79 },
        '四川': { north: 34.32, south: 26.05, east: 108.54, west: 97.35 },
        '湖北': { north: 33.28, south: 29.05, east: 116.08, west: 108.35 },
        '湖南': { north: 30.13, south: 24.64, east: 114.26, west: 108.79 },
        '福建': { north: 28.30, south: 23.51, east: 120.70, west: 115.84 },
        '安徽': { north: 34.65, south: 29.41, east: 119.31, west: 114.89 },
        '江西': { north: 30.05, south: 24.49, east: 118.47, west: 113.58 },
        '陕西': { north: 39.58, south: 31.71, east: 111.27, west: 105.46 },
        '河北': { north: 42.61, south: 36.05, east: 119.84, west: 113.46 },
        '山西': { north: 40.73, south: 34.58, east: 114.33, west: 110.23 },
        '辽宁': { north: 43.26, south: 38.72, east: 125.77, west: 118.85 },
        '吉林': { north: 46.31, south: 40.88, east: 134.00, west: 121.62 },
        '黑龙江': { north: 53.56, south: 43.42, east: 135.09, west: 121.13 }
    };

    // 移除省字后缀进行匹配
    var normalizedMapType = mapType.replace('省', '').replace('市', '').replace('自治区', '');
    return bounds[normalizedMapType] || bounds[mapType] || bounds['china'];
}

// 格式化比例尺数值
function formatScaleValue(km) {
    if (km >= 1000) {
        var thousands = Math.round(km / 1000);
        if (thousands >= 10) {
            thousands = Math.round(thousands / 10) * 10;
        }
        return { value: thousands * 1000, text: thousands + '000 km' };
    } else if (km >= 100) {
        var hundreds = Math.round(km / 100) * 100;
        return { value: hundreds, text: hundreds + ' km' };
    } else if (km >= 10) {
        var tens = Math.round(km / 10) * 10;
        return { value: tens, text: tens + ' km' };
    } else if (km >= 1) {
        var ones = Math.round(km);
        return { value: ones, text: ones + ' km' };
    } else {
        var meters = Math.round(km * 1000);
        if (meters >= 100) {
            meters = Math.round(meters / 100) * 100;
        } else if (meters >= 10) {
            meters = Math.round(meters / 10) * 10;
        }
        return { value: meters / 1000, text: meters + ' m' };
    }
}

// 监听地图缩放事件
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保ECharts完全加载
    setTimeout(function() {
        initializeScaleControls();
    }, 1500);

    // 如果页面已经加载完成，也尝试初始化
    if (document.readyState === 'complete') {
        setTimeout(initializeScaleControls, 500);
    }
});

// 初始化比例尺控件
function initializeScaleControls() {
    try {
        var chartDom = document.getElementById('main');
        if (!chartDom) {
            console.log('地图容器未找到，稍后重试...');
            setTimeout(initializeScaleControls, 1000);
            return;
        }

        var myChart = echarts.getInstanceByDom(chartDom);
        if (!myChart) {
            console.log('ECharts实例未找到，稍后重试...');
            setTimeout(initializeScaleControls, 1000);
            return;
        }

        console.log('开始初始化地图比例尺控件...');

        // 监听地图漫游事件（包括缩放和平移）
        myChart.on('georoam', function(params) {
            console.log('地图漫游事件触发:', params);
            forceUpdateScale();
            setTimeout(forceUpdateScale, 50);
            setTimeout(forceUpdateScale, 150);
            setTimeout(forceUpdateScale, 300);
        });

        // 监听地图完成事件
        myChart.on('finished', function() {
            console.log('地图渲染完成，更新比例尺');
            setTimeout(forceUpdateScale, 200);
        });

        // 监听所有可能的缩放事件
        myChart.on('dataZoom', function(params) {
            console.log('数据缩放事件:', params);
            setTimeout(forceUpdateScale, 50);
            setTimeout(forceUpdateScale, 200);
            setTimeout(forceUpdateScale, 500);
        });

        // 监听dataZoom的具体变化
        myChart.on('datazoom', function(params) {
            console.log('dataZoom变化事件:', params);
            setTimeout(forceUpdateScale, 50);
            setTimeout(forceUpdateScale, 200);
        });

        // 监听鼠标滚轮事件 - 增强版本
        chartDom.addEventListener('wheel', function(e) {
            console.log('鼠标滚轮事件触发，deltaY:', e.deltaY);
            // 确保滚轮事件不被阻止
            e.stopPropagation();
            setTimeout(forceUpdateScale, 50);
            setTimeout(forceUpdateScale, 200);
            setTimeout(forceUpdateScale, 500);
        }, { passive: false });

        // 监听鼠标事件
        chartDom.addEventListener('mouseup', function(e) {
            console.log('鼠标释放事件');
            setTimeout(forceUpdateScale, 100);
        });

        // 监听触摸事件（移动端支持）
        chartDom.addEventListener('touchend', function(e) {
            console.log('触摸结束事件');
            setTimeout(forceUpdateScale, 100);
        });

        // 监听键盘事件（支持键盘缩放）
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && (e.key === '+' || e.key === '-' || e.key === '0')) {
                console.log('键盘缩放事件:', e.key);
                setTimeout(forceUpdateScale, 100);
            }
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            console.log('窗口大小变化，更新比例尺');
            setTimeout(forceUpdateScale, 300);
        });

        // 初始化比例尺显示
        forceUpdateScale();
        setTimeout(forceUpdateScale, 500);
        setTimeout(forceUpdateScale, 1000);
        setTimeout(forceUpdateScale, 2000);

        // 高频率定期更新比例尺（确保捕获所有变化）
        setInterval(forceUpdateScale, 1000);

        console.log('比例尺控件初始化完成，开始高频监听');

    } catch (error) {
        console.log('初始化比例尺监听器时出错:', error);
        // 如果初始化失败，稍后重试
        setTimeout(initializeScaleControls, 2000);
    }
}

// 手动触发比例尺更新（供外部调用）
window.updateMapScale = function() {
    updateScale();
};
</script>
</body>
</html>
