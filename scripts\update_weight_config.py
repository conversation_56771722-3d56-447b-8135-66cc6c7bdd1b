#!/usr/bin/env python3
"""
更新权重配置脚本
将默认权重配置更新为新的权重值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models import RiskWeightConfig

def update_default_weight_config():
    """更新默认权重配置"""
    app = create_app()
    
    with app.app_context():
        try:
            # 查找默认配置
            config = RiskWeightConfig.query.filter_by(is_default=True).first()
            
            if not config:
                print("❌ 未找到默认权重配置")
                return
            
            print(f"找到默认配置: {config.config_name}")
            print("更新前的权重值:")
            print(f"  降雨权重: {config.rainfall_weight}")
            print(f"  排水权重: {config.drainage_weight}")
            print(f"  地形权重: {config.elevation_weight}")
            
            # 更新权重值
            config.config_name = '新版本默认权重配置'
            config.description = '基于新风险评估模型的默认权重配置方案'
            config.rainfall_weight = 0.633  # w_R 降雨权重
            config.drainage_weight = 0.260  # w_D 排水能力权重
            config.elevation_weight = 0.107  # w_T 地形权重
            
            # 验证权重配置
            is_valid, error_msg = config.validate_weights()
            if not is_valid:
                print(f"❌ 权重配置验证失败: {error_msg}")
                return
            
            # 保存更改
            db.session.commit()
            
            print("✅ 权重配置更新成功!")
            print("更新后的权重值:")
            print(f"  降雨权重: {config.rainfall_weight}")
            print(f"  排水权重: {config.drainage_weight}")
            print(f"  地形权重: {config.elevation_weight}")
            print(f"  权重总和: {config.rainfall_weight + config.drainage_weight + config.elevation_weight}")
            
        except Exception as e:
            print(f"❌ 更新失败: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    update_default_weight_config()
