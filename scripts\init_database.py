"""
城市内涝风险评估系统 - 完整数据库初始化脚本
包含所有表结构和默认数据的创建
"""
import os
import sys
import sqlite3
import json
import argparse
from pathlib import Path
from datetime import datetime, timezone, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app import create_app, db
from app.models import (
    User, Role, Dataset, SystemLog, GISLayer, AnalysisHistory, SystemMetrics,
    DataProcessingHistory, DataProcessingConfig, RiskWeightConfig,
    RiskAssessmentProject, RiskAssessmentData, RiskCalculationResult,
    RiskMatrix, get_china_time
)


def init_spatialite_db(db_path):
    """初始化SpatiaLite数据库"""
    print("正在初始化SpatiaLite数据库...")

    # 确保数据目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)

    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # 尝试加载SpatiaLite扩展
        cursor.execute("SELECT load_extension('mod_spatialite')")
        print("✓ SpatiaLite扩展加载成功")

        # 初始化空间元数据
        cursor.execute("SELECT InitSpatialMetaData(1)")
        print("✓ 空间元数据初始化成功")

        # 设置数据库优化参数
        cursor.execute("PRAGMA journal_mode=WAL")
        cursor.execute("PRAGMA synchronous=NORMAL")
        cursor.execute("PRAGMA cache_size=10000")
        cursor.execute("PRAGMA temp_store=memory")
        cursor.execute("PRAGMA foreign_keys=ON")
        print("✓ 数据库参数优化完成")

        conn.commit()

    except sqlite3.Error as e:
        print(f"⚠ SpatiaLite初始化失败: {e}")
        print("将使用标准SQLite数据库")

        # 设置基本的数据库参数
        cursor.execute("PRAGMA journal_mode=WAL")
        cursor.execute("PRAGMA synchronous=NORMAL")
        cursor.execute("PRAGMA cache_size=10000")
        cursor.execute("PRAGMA temp_store=memory")
        cursor.execute("PRAGMA foreign_keys=ON")
        conn.commit()
        print("✓ 标准SQLite数据库参数设置完成")

    finally:
        conn.close()


def create_default_roles():
    """创建默认角色"""
    print("正在创建默认角色...")

    # 创建管理员角色
    admin_role = Role.query.filter_by(name='admin').first()
    if not admin_role:
        admin_role = Role(name='admin', description='系统管理员，拥有所有权限')
        db.session.add(admin_role)
        print("✓ 创建管理员角色")

    # 创建普通用户角色
    user_role = Role.query.filter_by(name='user').first()
    if not user_role:
        user_role = Role(name='user', description='普通用户，拥有基本功能权限')
        db.session.add(user_role)
        print("✓ 创建普通用户角色")

    db.session.commit()
    return admin_role, user_role

def create_default_users(admin_role, user_role):
    """创建默认用户"""
    print("正在创建默认用户...")

    # 创建默认管理员账户
    admin_user = User.query.filter_by(username='admin').first()
    if not admin_user:
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            role_id=admin_role.id,  
            is_active=True
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
        print("✓ 创建默认管理员账户 (admin/admin123)")

    # 创建演示用户账户
    demo_user = User.query.filter_by(username='demo').first()
    if not demo_user:
        demo_user = User(
            username='demo',
            email='<EMAIL>',
            role_id=user_role.id,
            is_active=True
        )
        demo_user.set_password('demo123')
        db.session.add(demo_user)
        print("✓ 创建演示用户账户 (demo/demo123)")

    db.session.commit()
    return admin_user


def create_default_risk_weight_config(admin_user):
    """创建默认风险权重配置"""
    print("正在创建默认风险权重配置...")

    # 创建默认权重配置
    default_config = RiskWeightConfig.query.filter_by(config_name='默认权重配置').first()
    if not default_config:
        default_config = RiskWeightConfig(
            config_name='默认权重配置',
            description='基于新风险评估模型的默认权重配置方案',
            rainfall_weight=0.633,  # w_R 降雨权重
            drainage_weight=0.260,  # w_D 排水能力权重
            elevation_weight=0.107,  # w_T 地形权重
            is_default=True,
            is_active=True,
            created_by=admin_user.id
        )
        db.session.add(default_config)
        print("✓ 创建默认风险权重配置")

    db.session.commit()
    return default_config


def create_default_risk_matrix(admin_user):
    """创建默认风险矩阵"""
    print("正在创建默认风险矩阵...")

    # 默认5x5风险矩阵配置（字典格式）
    matrix_config = {
        'A': {  # 极低概率 [0.00, 0.15)
            '轻微': '极低风险',
            '较小': '极低风险',
            '中等': '低风险',
            '严重': '低风险',
            '灾难性': '中风险'
        },
        'B': {  # 低概率 [0.15, 0.35)
            '轻微': '极低风险',
            '较小': '低风险',
            '中等': '低风险',
            '严重': '中风险',
            '灾难性': '高风险'
        },
        'C': {  # 中等概率 [0.35, 0.60)
            '轻微': '低风险',
            '较小': '中风险',
            '中等': '中风险',
            '严重': '高风险',
            '灾难性': '极高风险'
        },
        'D': {  # 高概率 [0.60, 0.85)
            '轻微': '中风险',
            '较小': '高风险',
            '中等': '高风险',
            '严重': '极高风险',
            '灾难性': '极高风险'
        },
        'E': {  # 极高概率 [0.85, 1.00]
            '轻微': '高风险',
            '较小': '极高风险',
            '中等': '极高风险',
            '严重': '极高风险',
            '灾难性': '极高风险'
        }
    }

    # 发生概率等级定义
    likelihood_levels = {
        'A': {'name': '极低', 'description': '极不可能发生', 'range': '0.00-0.15'},
        'B': {'name': '低', 'description': '不太可能发生', 'range': '0.15-0.35'},
        'C': {'name': '中等', 'description': '可能发生', 'range': '0.35-0.60'},
        'D': {'name': '高', 'description': '很可能发生', 'range': '0.60-0.85'},
        'E': {'name': '极高', 'description': '极可能发生', 'range': '0.85-1.00'}
    }

    # 影响程度等级定义
    impact_levels = {
        '轻微': {'name': '轻微', 'description': '局部积水，影响有限'},
        '较小': {'name': '较小', 'description': '小范围积水，简单提示'},
        '中等': {'name': '中等', 'description': '区域积水，交通受阻'},
        '严重': {'name': '严重', 'description': '大面积积水，财产损失'},
        '灾难性': {'name': '灾难性', 'description': '重大人员伤亡，系统瘫痪'}
    }

    default_matrix = RiskMatrix.query.filter_by(matrix_name='默认风险矩阵').first()
    if not default_matrix:
        default_matrix = RiskMatrix(
            matrix_name='默认风险矩阵',
            description='标准5x5风险评估矩阵，适用于城市内涝风险评估',
            matrix_config=json.dumps(matrix_config, ensure_ascii=False),
            likelihood_levels=json.dumps(likelihood_levels, ensure_ascii=False),
            impact_levels=json.dumps(impact_levels, ensure_ascii=False),
            is_default=True,
            is_active=True,
            created_by=admin_user.id
        )
        db.session.add(default_matrix)
        print("✓ 创建默认风险矩阵")

    db.session.commit()
    return default_matrix


def create_default_processing_configs(admin_user):
    """创建默认数据处理配置"""
    print("正在创建默认数据处理配置...")

    # 矢量数据清洗配置
    vector_config = DataProcessingConfig.query.filter_by(config_name='矢量数据标准清洗').first()
    if not vector_config:
        vector_params = {
            'remove_duplicates': True,
            'fix_geometry': True,
            'validate_topology': True,
            'coordinate_system': 'EPSG:4326',
            'encoding': 'utf-8'
        }
        vector_config = DataProcessingConfig(
            config_name='矢量数据标准清洗',
            config_type='clean',
            description='矢量数据的标准清洗配置，包括去重、几何修复等',
            config_parameters=json.dumps(vector_params, ensure_ascii=False),
            data_types='vector,geojson,land_use,rivers,drainage',
            is_default=True,
            is_active=True,
            created_by=admin_user.id
        )
        db.session.add(vector_config)
        print("✓ 创建矢量数据清洗配置")

    db.session.commit()


def create_system_log_entry(admin_user):
    """创建系统初始化日志"""
    log_entry = SystemLog(
        user_id=admin_user.id,
        action='database_initialization',
        target='system',
        details='数据库初始化完成，创建了所有表结构和默认数据',
        ip_address='127.0.0.1',
        user_agent='Database Init Script'
    )
    db.session.add(log_entry)
    db.session.commit()


def backup_existing_database(db_path):
    """备份现有数据库"""
    if os.path.exists(db_path):
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        try:
            import shutil
            shutil.copy2(db_path, backup_path)
            print(f"✓ 现有数据库已备份到: {backup_path}")
            return backup_path
        except Exception as e:
            print(f"⚠ 数据库备份失败: {e}")
            return None
    return None


def drop_all_tables():
    """删除所有现有表"""
    print("正在删除现有表...")
    try:
        db.drop_all()
        print("✓ 现有表删除完成")
    except Exception as e:
        print(f"⚠ 删除表时出现错误: {e}")


def create_all_tables():
    """创建所有表"""
    print("正在创建数据库表...")
    try:
        db.create_all()
        print("✓ 数据库表创建完成")

        # 显示创建的表
        inspector = db.inspect(db.engine)
        tables = inspector.get_table_names()
        print(f"✓ 成功创建 {len(tables)} 个表:")

        # 按类别分组显示表
        core_tables = []
        gis_tables = []
        risk_tables = []
        system_tables = []

        for table in sorted(tables):
            if table in ['users', 'roles']:
                core_tables.append(table)
            elif 'gis' in table or 'dataset' in table:
                gis_tables.append(table)
            elif 'risk' in table:
                risk_tables.append(table)
            else:
                system_tables.append(table)

        if core_tables:
            print("   📁 核心表:")
            for table in core_tables:
                print(f"      - {table}")

        if gis_tables:
            print("   🗺️  GIS数据表:")
            for table in gis_tables:
                print(f"      - {table}")

        if risk_tables:
            print("   ⚠️  风险评估表:")
            for table in risk_tables:
                print(f"      - {table}")

        if system_tables:
            print("   🔧 系统表:")
            for table in system_tables:
                print(f"      - {table}")

    except Exception as e:
        print(f"✗ 创建表失败: {e}")
        raise


def verify_table_structure():
    """验证表结构完整性"""
    print("正在验证表结构完整性...")

    inspector = db.inspect(db.engine)
    tables = inspector.get_table_names()

    # 预期的表列表
    expected_tables = {
        'users', 'roles', 'datasets', 'gis_layers', 'system_logs',
        'analysis_history', 'system_metrics', 'data_processing_history',
        'data_processing_config', 'risk_weight_configs', 'risk_assessment_projects',
        'risk_assessment_data', 'risk_calculation_results', 'risk_matrices'
    }

    missing_tables = expected_tables - set(tables)
    extra_tables = set(tables) - expected_tables

    if missing_tables:
        print(f"⚠️  缺少表: {missing_tables}")
        return False

    if extra_tables:
        print(f"ℹ️  额外表: {extra_tables}")

    # 验证关键表的字段
    critical_tables = {
        'users': ['id', 'username', 'email', 'password_hash', 'role_id', 'is_active', 'created_at'],
        'roles': ['id', 'name', 'description', 'created_at'],
        'datasets': ['id', 'name', 'data_type', 'file_path', 'processing_status', 'created_by', 'created_at'],
        'gis_layers': ['id', 'name', 'layer_type', 'data_category', 'file_path', 'processing_status', 'created_by'],
        'risk_assessment_data': ['id', 'project_id', 'annual_precipitation', 'drainage_network_density', 'elevation', 'population_density', 'gdp_per_area'],
        'risk_weight_configs': ['id', 'config_name', 'rainfall_weight', 'drainage_weight', 'elevation_weight', 'is_default'],
        'risk_matrices': ['id', 'matrix_name', 'matrix_config', 'likelihood_levels', 'impact_levels', 'is_default'],
        'data_processing_history': ['id', 'dataset_id', 'operation_type', 'status', 'progress', 'start_time'],
        'system_logs': ['id', 'action', 'target', 'created_at']
    }

    all_valid = True
    for table_name, expected_columns in critical_tables.items():
        if table_name in tables:
            columns = [col['name'] for col in inspector.get_columns(table_name)]
            missing_columns = set(expected_columns) - set(columns)
            if missing_columns:
                print(f"❌ 表 {table_name} 缺少字段: {missing_columns}")
                all_valid = False
            else:
                print(f"✅ 表 {table_name} 结构完整")
        else:
            print(f"❌ 缺少关键表: {table_name}")
            all_valid = False

    if all_valid:
        print("✅ 所有表结构验证通过")
    else:
        print("⚠️  发现表结构问题")

    return all_valid


def check_and_fix_missing_fields():
    """检查并修复缺失的字段"""
    print("正在检查并修复缺失的字段...")

    inspector = db.inspect(db.engine)

    # 检查datasets表是否有processing_status字段
    if 'datasets' in inspector.get_table_names():
        columns = [col['name'] for col in inspector.get_columns('datasets')]
        if 'processing_status' not in columns:
            print("正在为datasets表添加processing_status字段...")
            try:
                db.engine.execute('ALTER TABLE datasets ADD COLUMN processing_status VARCHAR(20) DEFAULT "uploaded"')
                print("✅ 添加processing_status字段成功")
            except Exception as e:
                print(f"⚠️  添加processing_status字段失败: {e}")

        if 'processing_log' not in columns:
            print("正在为datasets表添加processing_log字段...")
            try:
                db.engine.execute('ALTER TABLE datasets ADD COLUMN processing_log TEXT')
                print("✅ 添加processing_log字段成功")
            except Exception as e:
                print(f"⚠️  添加processing_log字段失败: {e}")

    # 检查gis_layers表是否有extra_metadata字段
    if 'gis_layers' in inspector.get_table_names():
        columns = [col['name'] for col in inspector.get_columns('gis_layers')]
        if 'extra_metadata' not in columns:
            print("正在为gis_layers表添加extra_metadata字段...")
            try:
                db.engine.execute('ALTER TABLE gis_layers ADD COLUMN extra_metadata JSON')
                print("✅ 添加extra_metadata字段成功")
            except Exception as e:
                print(f"⚠️  添加extra_metadata字段失败: {e}")

    print("✅ 字段检查和修复完成")


def perform_database_health_check():
    """执行数据库健康检查"""
    print("正在执行数据库健康检查...")

    try:
        # 检查用户表
        user_count = User.query.count()
        role_count = Role.query.count()
        print(f"✅ 用户系统: {user_count} 个用户, {role_count} 个角色")

        # 检查数据集表
        dataset_count = Dataset.query.count()
        print(f"✅ 数据管理: {dataset_count} 个数据集")

        # 检查GIS图层表
        try:
            gis_layer_count = GISLayer.query.count()
            print(f"✅ GIS图层: {gis_layer_count} 个图层")
        except Exception as e:
            print(f"ℹ️  GIS图层表: 暂无数据 ({e})")

        # 检查风险评估配置
        risk_config_count = RiskWeightConfig.query.count()
        risk_matrix_count = RiskMatrix.query.count()
        print(f"✅ 风险评估: {risk_config_count} 个权重配置, {risk_matrix_count} 个风险矩阵")

        # 检查数据处理配置
        processing_config_count = DataProcessingConfig.query.count()
        print(f"✅ 数据处理: {processing_config_count} 个处理配置")

        # 检查系统日志
        log_count = SystemLog.query.count()
        print(f"✅ 系统日志: {log_count} 条记录")

        print("✅ 数据库健康检查完成")
        return True

    except Exception as e:
        print(f"❌ 数据库健康检查失败: {e}")
        return False


def initialize_all_default_data():
    """初始化所有默认数据"""
    print("正在初始化默认数据...")

    # 创建角色
    admin_role, user_role = create_default_roles()

    # 创建用户
    admin_user = create_default_users(admin_role, user_role)

    # 创建风险权重配置
    default_config = create_default_risk_weight_config(admin_user)

    # 创建风险矩阵
    default_matrix = create_default_risk_matrix(admin_user)

    # 创建数据处理配置
    create_default_processing_configs(admin_user)

    # 创建系统日志
    create_system_log_entry(admin_user)

    print("✓ 所有默认数据初始化完成")

    return {
        'admin_user': admin_user,
        'default_config': default_config,
        'default_matrix': default_matrix
    }


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='城市内涝风险评估系统 - 完整数据库初始化脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python scripts/init_database.py                    # 完整初始化
  python scripts/init_database.py --no-backup       # 不备份现有数据库
  python scripts/init_database.py --verify-only     # 仅验证表结构
  python scripts/init_database.py --health-check    # 仅执行健康检查
        """
    )

    parser.add_argument('--no-backup', action='store_true',
                       help='不备份现有数据库')
    parser.add_argument('--verify-only', action='store_true',
                       help='仅验证表结构，不进行初始化')
    parser.add_argument('--health-check', action='store_true',
                       help='仅执行数据库健康检查')
    parser.add_argument('--force', action='store_true',
                       help='强制重新初始化，即使数据库已存在')

    return parser.parse_args()


def main():
    """主函数"""
    args = parse_arguments()

    print("=" * 80)
    print("城市内涝风险评估系统 - 完整数据库初始化")
    print("=" * 80)

    # 创建Flask应用
    app = create_app()

    with app.app_context():
        # 获取数据库路径
        db_uri = app.config['SQLALCHEMY_DATABASE_URI']
        if db_uri.startswith('sqlite:///'):
            db_path = db_uri.replace('sqlite:///', '')
        else:
            print("✗ 仅支持SQLite数据库")
            return

        print(f"数据库路径: {db_path}")

        # 仅健康检查模式
        if args.health_check:
            print("🔍 执行数据库健康检查模式")
            health_check_passed = perform_database_health_check()
            if health_check_passed:
                print("✅ 数据库健康检查通过")
            else:
                print("❌ 数据库健康检查失败")
            return

        # 仅验证模式
        if args.verify_only:
            print("🔍 执行表结构验证模式")
            structure_valid = verify_table_structure()
            if structure_valid:
                print("✅ 表结构验证通过")
            else:
                print("❌ 表结构验证失败")
            return

        # 完整初始化模式
        backup_path = None
        if not args.no_backup:
            # 备份现有数据库
            backup_path = backup_existing_database(db_path)

        # 初始化SpatiaLite数据库
        init_spatialite_db(db_path)

        # 删除现有表
        drop_all_tables()

        # 创建所有表
        create_all_tables()

        # 验证表结构
        structure_valid = verify_table_structure()

        # 检查并修复缺失字段
        if not structure_valid:
            check_and_fix_missing_fields()
            # 重新验证
            verify_table_structure()

        # 初始化默认数据
        default_data = initialize_all_default_data()

        # 执行数据库健康检查
        health_check_passed = perform_database_health_check()

        print("=" * 80)
        print("🎉 数据库初始化完成！")
        print("=" * 80)
        print("📋 默认账户信息：")
        print("   👤 管理员: admin / admin123")
        print("   👤 演示用户: demo / demo123")
        print("   👤 分析师: analyst / analyst123")
        print("=" * 80)
        print("⚙️  默认配置信息：")
        print(f"   📊 风险权重配置: {default_data['default_config'].config_name}")
        print(f"   📈 风险矩阵: {default_data['default_matrix'].matrix_name}")
        print("=" * 80)
        print("✅ 数据库包含完整的表结构和字段")
        print("✅ 所有默认数据已初始化")
        print("✅ 系统已准备就绪，可以开始使用")
        print("=" * 80)

        if health_check_passed:
            print("🚀 快速开始指南:")
            print("   1. 启动应用: python run.py 或 flask run")
            print("   2. 访问系统: http://localhost:5000")
            print("   3. 登录系统: 使用上述任一账户登录")
            print("   4. 开始使用: 上传数据、进行风险评估")
            print("=" * 80)
        else:
            print("⚠️  数据库健康检查未完全通过，请检查日志")
            print("=" * 80)

        if backup_path:
            print(f"📁 原数据库备份位置: {backup_path}")
            print("=" * 80)


if __name__ == '__main__':
    main()
