#!/usr/bin/env python3
"""
重新处理上传的土地利用数据
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from app.extensions import db
from app.models import GISLayer
from app.services.gis_data_service import GISDataService
import config

def process_uploaded_landuse():
    """重新处理上传的土地利用数据"""
    # 创建简化的Flask应用
    app = Flask(__name__)
    app.config.from_object(config.DevelopmentConfig)
    db.init_app(app)
    
    with app.app_context():
        print("=== 重新处理上传的土地利用数据 ===\n")
        
        # 查找上传目录中的文件
        upload_base = os.path.join(os.path.dirname(__file__), 'data', 'gis_layers')
        
        if not os.path.exists(upload_base):
            print("❌ 上传目录不存在")
            return
        
        processed_count = 0
        
        for root, dirs, files in os.walk(upload_base):
            for file in files:
                file_path = os.path.join(root, file)
                
                # 检查是否是土地利用相关文件
                if not is_landuse_file(file):
                    continue
                
                print(f"处理文件: {file}")
                
                # 检查是否已经在数据库中
                existing_layer = GISLayer.query.filter(GISLayer.file_path == file_path).first()
                if existing_layer:
                    print(f"  ⚠️ 文件已在数据库中: {existing_layer.name}")
                    continue
                
                # 处理文件
                try:
                    file_info = {
                        'name': file,
                        'path': file_path,
                        'size': os.path.getsize(file_path)
                    }
                    
                    # 根据文件类型处理
                    if file.lower().endswith(('.tif', '.tiff')):
                        layer = GISDataService.process_raster_file(file_info, root, 1)  # 假设用户ID为1
                    elif file.lower().endswith('.shp'):
                        # 对于Shapefile，需要找到相关文件
                        file_group = find_shapefile_group(file_path)
                        layer = GISDataService.process_shapefile(file_group, root, 1)
                    else:
                        print(f"  ❌ 不支持的文件类型: {file}")
                        continue
                    
                    if layer:
                        # 手动设置为土地利用数据
                        layer.data_category = 'landuse'
                        
                        # 根据文件名推断省份
                        province = extract_province_from_filename(file)
                        if province:
                            layer.province = province
                            print(f"  📍 检测到省份: {province}")
                        
                        # 保存到数据库
                        db.session.add(layer)
                        db.session.commit()
                        
                        print(f"  ✅ 成功处理: {layer.name}")
                        processed_count += 1
                    else:
                        print(f"  ❌ 处理失败")
                        
                except Exception as e:
                    print(f"  ❌ 处理出错: {e}")
                    db.session.rollback()
        
        print(f"\n=== 处理完成 ===")
        print(f"成功处理 {processed_count} 个文件")
        
        # 显示更新后的土地利用图层
        landuse_layers = GISLayer.query.filter(
            GISLayer.data_category == 'landuse',
            GISLayer.processing_status == 'processed'
        ).all()
        
        print(f"\n=== 当前土地利用图层 ({len(landuse_layers)} 个) ===")
        for layer in landuse_layers:
            print(f"✅ {layer.name}")
            print(f"   省份: {layer.province}")
            print(f"   类型: {layer.layer_type}")
            print(f"   状态: {layer.processing_status}")
            print()

def is_landuse_file(filename):
    """判断是否是土地利用文件"""
    filename_lower = filename.lower()
    
    # 检查文件扩展名
    if not filename_lower.endswith(('.tif', '.tiff', '.shp')):
        return False
    
    # 检查文件名关键词
    landuse_keywords = [
        'landuse', 'land_use', 'clcd', '土地利用', '用地',
        'lulc', 'land_cover', 'classification'
    ]
    
    return any(keyword in filename_lower for keyword in landuse_keywords)

def extract_province_from_filename(filename):
    """从文件名中提取省份信息"""
    filename_lower = filename.lower()
    
    # 省份映射
    province_mapping = {
        'fujian': '福建省',
        'henan': '河南省',
        'hubei': '湖北省',
        'guangdong': '广东省',
        'shandong': '山东省',
        'jiangsu': '江苏省',
        'zhejiang': '浙江省',
        'anhui': '安徽省',
        'jiangxi': '江西省',
        'hunan': '湖南省',
        'hubei': '湖北省',
        'guangxi': '广西壮族自治区',
        'hainan': '海南省',
        'sichuan': '四川省',
        'guizhou': '贵州省',
        'yunnan': '云南省',
        'tibet': '西藏自治区',
        'shaanxi': '陕西省',
        'gansu': '甘肃省',
        'qinghai': '青海省',
        'ningxia': '宁夏回族自治区',
        'xinjiang': '新疆维吾尔自治区',
        'beijing': '北京市',
        'tianjin': '天津市',
        'shanghai': '上海市',
        'chongqing': '重庆市',
        'liaoning': '辽宁省',
        'jilin': '吉林省',
        'heilongjiang': '黑龙江省',
        'inner_mongolia': '内蒙古自治区',
        'shanxi': '山西省',
        'hebei': '河北省'
    }
    
    for eng_name, cn_name in province_mapping.items():
        if eng_name in filename_lower:
            return cn_name
    
    return None

def find_shapefile_group(shp_path):
    """查找Shapefile相关文件组"""
    base_path = os.path.splitext(shp_path)[0]
    extensions = ['.shp', '.shx', '.dbf', '.prj', '.cpg']
    
    file_group = []
    for ext in extensions:
        file_path = base_path + ext
        if os.path.exists(file_path):
            file_group.append({
                'name': os.path.basename(file_path),
                'path': file_path,
                'size': os.path.getsize(file_path)
            })
    
    return file_group

if __name__ == '__main__':
    process_uploaded_landuse()
