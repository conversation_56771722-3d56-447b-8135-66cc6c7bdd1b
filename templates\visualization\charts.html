{% extends "base.html" %}

{% block title %}图表分析 - 可视化分析 - {{ super() }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/visualization.css') }}" />
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-line me-2"></i>图表分析
                    </h2>
                    <p class="text-muted mb-0">降雨量、排水管道密度、人口密度、GDP密度时间序列分析</p>
                </div>
                <div>
                    <span class="badge bg-info me-2">基于历史气象和内涝统计</span>
                    <a href="{{ url_for('visualization.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 控制面板 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-3">
                    <div class="row align-items-center">
                        <!-- 省份选择 -->
                        <div class="col-md-2">
                            <label for="provinceSelect" class="form-label mb-1">选择省份</label>
                            <select class="form-select" id="provinceSelect">
                                <option value="">请选择省份...</option>
                                <!-- 直辖市 -->
                                <option value="北京市">北京市</option>
                                <option value="天津市">天津市</option>
                                <option value="上海市">上海市</option>
                                <option value="重庆市">重庆市</option>
                                <!-- 华北地区 -->
                                <option value="河北省">河北省</option>
                                <option value="山西省">山西省</option>
                                <option value="内蒙古自治区">内蒙古自治区</option>
                                <!-- 东北地区 -->
                                <option value="辽宁省">辽宁省</option>
                                <option value="吉林省">吉林省</option>
                                <option value="黑龙江省">黑龙江省</option>
                                <!-- 华东地区 -->
                                <option value="江苏省">江苏省</option>
                                <option value="浙江省">浙江省</option>
                                <option value="安徽省">安徽省</option>
                                <option value="福建省">福建省</option>
                                <option value="江西省">江西省</option>
                                <option value="山东省">山东省</option>
                                <!-- 华中地区 -->
                                <option value="河南省">河南省</option>
                                <option value="湖北省">湖北省</option>
                                <option value="湖南省">湖南省</option>
                                <!-- 华南地区 -->
                                <option value="广东省">广东省</option>
                                <option value="广西壮族自治区">广西壮族自治区</option>
                                <option value="海南省">海南省</option>
                                <!-- 西南地区 -->
                                <option value="四川省">四川省</option>
                                <option value="贵州省">贵州省</option>
                                <option value="云南省">云南省</option>
                                <option value="西藏自治区">西藏自治区</option>
                                <!-- 西北地区 -->
                                <option value="陕西省">陕西省</option>
                                <option value="甘肃省">甘肃省</option>
                                <option value="青海省">青海省</option>
                                <option value="宁夏回族自治区">宁夏回族自治区</option>
                                <option value="新疆维吾尔自治区">新疆维吾尔自治区</option>
                                <!-- 特别行政区 -->
                                <option value="香港特别行政区">香港特别行政区</option>
                                <option value="澳门特别行政区">澳门特别行政区</option>
                            </select>
                        </div>

                        <!-- 城市选择 -->
                        <div class="col-md-2" id="citySelectContainer" style="display: none;">
                            <label for="citySelect" class="form-label mb-1">选择城市</label>
                            <select class="form-select" id="citySelect">
                                <option value="">请选择城市...</option>
                            </select>
                        </div>



                        <!-- 刷新按钮 -->
                        <div class="col-md-2">
                            <label class="form-label mb-1">&nbsp;</label>
                            <div class="d-grid">
                                <button class="btn btn-primary" id="refreshBtn">
                                    <i class="fas fa-sync me-1"></i>刷新数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- 图表区域 -->
    <div class="row">
        <!-- 降雨量图表 -->
        <div class="col-lg-6 mb-4" id="rainfallChartCard">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-cloud-rain me-2 text-primary"></i>降雨量趋势
                    </h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="changeChartType('rainfall', 'line')">折线图</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeChartType('rainfall', 'bar')">柱状图</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeChartType('rainfall', 'area')">面积图</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="rainfallChart" height="300"></canvas>
                </div>
                <div class="card-footer bg-light">
                    <div class="row text-center">
                        <div class="col-4">
                            <small class="text-muted d-block">平均值</small>
                            <strong id="rainfallAvg">--</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">最大值</small>
                            <strong id="rainfallMax">--</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">总计</small>
                            <strong id="rainfallTotal">--</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- 关键指标图表区域 -->
        <!-- 排水管道密度分布图 -->
        <div class="col-lg-6 mb-4" id="drainageDensityChartCard">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-water me-2 text-info"></i>排水管道密度分布
                    </h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="changeChartType('drainageDensity', 'line')">折线图</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeChartType('drainageDensity', 'bar')">柱状图</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeChartType('drainageDensity', 'area')">面积图</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="drainageDensityChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <!-- 人口密度分布图 -->
        <div class="row">
        <div class="col-lg-6 mb-4" id="populationDensityChartCard">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-users me-2 text-success"></i>人口密度分布
                    </h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="changeChartType('populationDensity', 'line')">折线图</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeChartType('populationDensity', 'bar')">柱状图</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeChartType('populationDensity', 'area')">面积图</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="populationDensityChart" height="300"></canvas>
                </div>
            </div>
        </div>

    <!-- 新增指标图表区域 -->
        <!-- GDP密度分布图 -->
        <div class="col-lg-6 mb-4" id="gdpDensityChartCard">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2 text-warning"></i>GDP密度分布
                    </h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="changeChartType('gdpDensity', 'line')">折线图</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeChartType('gdpDensity', 'bar')">柱状图</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeChartType('gdpDensity', 'area')">面积图</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="gdpDensityChart" height="300"></canvas>
                </div>
                <div class="card-footer bg-light">
                    <div class="row text-center">
                        <div class="col-4">
                            <small class="text-muted d-block">平均值</small>
                            <strong id="gdpDensityAvg">--</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">最大值</small>
                            <strong id="gdpDensityMax">--</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">总计</small>
                            <strong id="gdpDensityTotal">--</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    </div>




</div>
{% endblock %}

{% block styles %}
<style>
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.15s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn-group .btn-check:checked + .btn {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}



.chart-container {
    position: relative;
    height: 300px;
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
}

.modal-xl {
    max-width: 90%;
}

.dropdown-menu {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/chartjs-adapter-date-fns/2.0.0/chartjs-adapter-date-fns.bundle.min.js"></script>
<script>
// 全局变量
let currentProvince = null;
let currentCity = null; // 不设置默认城市，需要用户选择
let currentYear = '2024'; // 默认年份
let charts = {};
let chartData = {};

// 省份城市数据 - 中国所有省份、自治区、直辖市和特别行政区
const provinceCityData = {
    // 直辖市
    '北京市': [
        { id: 'beijing', name: '北京市' }
    ],
    '天津市': [
        { id: 'tianjin', name: '天津市' }
    ],
    '上海市': [
        { id: 'shanghai', name: '上海市' }
    ],
    '重庆市': [
        { id: 'chongqing', name: '重庆市' }
    ],

    // 省份
    '河北省': [
        { id: 'shijiazhuang', name: '石家庄市' },
        { id: 'tangshan', name: '唐山市' },
        { id: 'qinhuangdao', name: '秦皇岛市' },
        { id: 'handan', name: '邯郸市' },
        { id: 'xingtai', name: '邢台市' },
        { id: 'baoding', name: '保定市' },
        { id: 'zhangjiakou', name: '张家口市' },
        { id: 'chengde', name: '承德市' },
        { id: 'cangzhou', name: '沧州市' },
        { id: 'langfang', name: '廊坊市' },
        { id: 'hengshui', name: '衡水市' }
    ],
    '山西省': [
        { id: 'taiyuan', name: '太原市' },
        { id: 'datong', name: '大同市' },
        { id: 'yangquan', name: '阳泉市' },
        { id: 'changzhi', name: '长治市' },
        { id: 'jincheng', name: '晋城市' },
        { id: 'shuozhou', name: '朔州市' },
        { id: 'jinzhong', name: '晋中市' },
        { id: 'yuncheng', name: '运城市' },
        { id: 'xinzhou', name: '忻州市' },
        { id: 'linfen', name: '临汾市' },
        { id: 'lvliang', name: '吕梁市' }
    ],
    '辽宁省': [
        { id: 'shenyang', name: '沈阳市' },
        { id: 'dalian', name: '大连市' },
        { id: 'anshan', name: '鞍山市' },
        { id: 'fushun', name: '抚顺市' },
        { id: 'benxi', name: '本溪市' },
        { id: 'dandong', name: '丹东市' },
        { id: 'jinzhou', name: '锦州市' },
        { id: 'yingkou', name: '营口市' },
        { id: 'fuxin', name: '阜新市' },
        { id: 'liaoyang', name: '辽阳市' },
        { id: 'panjin', name: '盘锦市' },
        { id: 'tieling', name: '铁岭市' },
        { id: 'chaoyang', name: '朝阳市' },
        { id: 'huludao', name: '葫芦岛市' }
    ],
    '吉林省': [
        { id: 'changchun', name: '长春市' },
        { id: 'jilin', name: '吉林市' },
        { id: 'siping', name: '四平市' },
        { id: 'liaoyuan', name: '辽源市' },
        { id: 'tonghua', name: '通化市' },
        { id: 'baishan', name: '白山市' },
        { id: 'songyuan', name: '松原市' },
        { id: 'baicheng', name: '白城市' },
        { id: 'yanbian', name: '延边朝鲜族自治州' }
    ],
    '黑龙江省': [
        { id: 'harbin', name: '哈尔滨市' },
        { id: 'qiqihar', name: '齐齐哈尔市' },
        { id: 'jixi', name: '鸡西市' },
        { id: 'hegang', name: '鹤岗市' },
        { id: 'shuangyashan', name: '双鸭山市' },
        { id: 'daqing', name: '大庆市' },
        { id: 'yichun', name: '伊春市' },
        { id: 'jiamusi', name: '佳木斯市' },
        { id: 'qitaihe', name: '七台河市' },
        { id: 'mudanjiang', name: '牡丹江市' },
        { id: 'heihe', name: '黑河市' },
        { id: 'suihua', name: '绥化市' },
        { id: 'daxinganling', name: '大兴安岭地区' }
    ],
    '江苏省': [
        { id: 'nanjing', name: '南京市' },
        { id: 'wuxi', name: '无锡市' },
        { id: 'xuzhou', name: '徐州市' },
        { id: 'changzhou', name: '常州市' },
        { id: 'suzhou', name: '苏州市' },
        { id: 'nantong', name: '南通市' },
        { id: 'lianyungang', name: '连云港市' },
        { id: 'huaian', name: '淮安市' },
        { id: 'yancheng', name: '盐城市' },
        { id: 'yangzhou', name: '扬州市' },
        { id: 'zhenjiang', name: '镇江市' },
        { id: 'taizhou_js', name: '泰州市' },
        { id: 'suqian', name: '宿迁市' }
    ],
    '浙江省': [
        { id: 'hangzhou', name: '杭州市' },
        { id: 'ningbo', name: '宁波市' },
        { id: 'wenzhou', name: '温州市' },
        { id: 'jiaxing', name: '嘉兴市' },
        { id: 'huzhou', name: '湖州市' },
        { id: 'shaoxing', name: '绍兴市' },
        { id: 'jinhua', name: '金华市' },
        { id: 'quzhou', name: '衢州市' },
        { id: 'zhoushan', name: '舟山市' },
        { id: 'taizhou_zj', name: '台州市' },
        { id: 'lishui', name: '丽水市' }
    ],
    '安徽省': [
        { id: 'hefei', name: '合肥市' },
        { id: 'wuhu', name: '芜湖市' },
        { id: 'bengbu', name: '蚌埠市' },
        { id: 'huainan', name: '淮南市' },
        { id: 'maanshan', name: '马鞍山市' },
        { id: 'huaibei', name: '淮北市' },
        { id: 'tongling', name: '铜陵市' },
        { id: 'anqing', name: '安庆市' },
        { id: 'huangshan', name: '黄山市' },
        { id: 'chuzhou', name: '滁州市' },
        { id: 'fuyang', name: '阜阳市' },
        { id: 'suzhou_ah', name: '宿州市' },
        { id: 'luan', name: '六安市' },
        { id: 'bozhou', name: '亳州市' },
        { id: 'chizhou', name: '池州市' },
        { id: 'xuancheng', name: '宣城市' }
    ],
    '福建省': [
        { id: 'fuzhou', name: '福州市' },
        { id: 'xiamen', name: '厦门市' },
        { id: 'putian', name: '莆田市' },
        { id: 'sanming', name: '三明市' },
        { id: 'quanzhou', name: '泉州市' },
        { id: 'zhangzhou', name: '漳州市' },
        { id: 'nanping', name: '南平市' },
        { id: 'longyan', name: '龙岩市' },
        { id: 'ningde', name: '宁德市' }
    ],
    '江西省': [
        { id: 'nanchang', name: '南昌市' },
        { id: 'jingdezhen', name: '景德镇市' },
        { id: 'pingxiang', name: '萍乡市' },
        { id: 'jiujiang', name: '九江市' },
        { id: 'xinyu', name: '新余市' },
        { id: 'yingtan', name: '鹰潭市' },
        { id: 'ganzhou', name: '赣州市' },
        { id: 'jian', name: '吉安市' },
        { id: 'yichun_jx', name: '宜春市' },
        { id: 'fuzhou_jx', name: '抚州市' },
        { id: 'shangrao', name: '上饶市' }
    ],
    '山东省': [
        { id: 'jinan', name: '济南市' },
        { id: 'qingdao', name: '青岛市' },
        { id: 'zibo', name: '淄博市' },
        { id: 'zaozhuang', name: '枣庄市' },
        { id: 'dongying', name: '东营市' },
        { id: 'yantai', name: '烟台市' },
        { id: 'weifang', name: '潍坊市' },
        { id: 'jining', name: '济宁市' },
        { id: 'taian', name: '泰安市' },
        { id: 'weihai', name: '威海市' },
        { id: 'rizhao', name: '日照市' },
        { id: 'laiwu', name: '莱芜市' },
        { id: 'linyi', name: '临沂市' },
        { id: 'dezhou', name: '德州市' },
        { id: 'liaocheng', name: '聊城市' },
        { id: 'binzhou', name: '滨州市' },
        { id: 'heze', name: '菏泽市' }
    ],
    '河南省': [
        { id: 'zhengzhou', name: '郑州市' },
        { id: 'kaifeng', name: '开封市' },
        { id: 'luoyang', name: '洛阳市' },
        { id: 'pingdingshan', name: '平顶山市' },
        { id: 'anyang', name: '安阳市' },
        { id: 'hebi', name: '鹤壁市' },
        { id: 'xinxiang', name: '新乡市' },
        { id: 'jiaozuo', name: '焦作市' },
        { id: 'puyang', name: '濮阳市' },
        { id: 'xuchang', name: '许昌市' },
        { id: 'luohe', name: '漯河市' },
        { id: 'sanmenxia', name: '三门峡市' },
        { id: 'nanyang', name: '南阳市' },
        { id: 'shangqiu', name: '商丘市' },
        { id: 'xinyang', name: '信阳市' },
        { id: 'zhoukou', name: '周口市' },
        { id: 'zhumadian', name: '驻马店市' },
        { id: 'jiyuan', name: '济源市' }
    ],
    '湖北省': [
        { id: 'wuhan', name: '武汉市' },
        { id: 'huangshi', name: '黄石市' },
        { id: 'shiyan', name: '十堰市' },
        { id: 'yichang', name: '宜昌市' },
        { id: 'xiangyang', name: '襄阳市' },
        { id: 'ezhou', name: '鄂州市' },
        { id: 'jingmen', name: '荆门市' },
        { id: 'xiaogan', name: '孝感市' },
        { id: 'jingzhou', name: '荆州市' },
        { id: 'huanggang', name: '黄冈市' },
        { id: 'xianning', name: '咸宁市' },
        { id: 'suizhou', name: '随州市' },
        { id: 'enshi', name: '恩施土家族苗族自治州' },
        { id: 'xiantao', name: '仙桃市' },
        { id: 'qianjiang', name: '潜江市' },
        { id: 'tianmen', name: '天门市' },
        { id: 'shennongjia', name: '神农架林区' }
    ],
    '湖南省': [
        { id: 'changsha', name: '长沙市' },
        { id: 'zhuzhou', name: '株洲市' },
        { id: 'xiangtan', name: '湘潭市' },
        { id: 'hengyang', name: '衡阳市' },
        { id: 'shaoyang', name: '邵阳市' },
        { id: 'yueyang', name: '岳阳市' },
        { id: 'changde', name: '常德市' },
        { id: 'zhangjiajie', name: '张家界市' },
        { id: 'yiyang', name: '益阳市' },
        { id: 'chenzhou', name: '郴州市' },
        { id: 'yongzhou', name: '永州市' },
        { id: 'huaihua', name: '怀化市' },
        { id: 'loudi', name: '娄底市' },
        { id: 'xiangxi', name: '湘西土家族苗族自治州' }
    ],
    '广东省': [
        { id: 'guangzhou', name: '广州市' },
        { id: 'shaoguan', name: '韶关市' },
        { id: 'shenzhen', name: '深圳市' },
        { id: 'zhuhai', name: '珠海市' },
        { id: 'shantou', name: '汕头市' },
        { id: 'foshan', name: '佛山市' },
        { id: 'jiangmen', name: '江门市' },
        { id: 'zhanjiang', name: '湛江市' },
        { id: 'maoming', name: '茂名市' },
        { id: 'zhaoqing', name: '肇庆市' },
        { id: 'huizhou', name: '惠州市' },
        { id: 'meizhou', name: '梅州市' },
        { id: 'shanwei', name: '汕尾市' },
        { id: 'heyuan', name: '河源市' },
        { id: 'yangjiang', name: '阳江市' },
        { id: 'qingyuan', name: '清远市' },
        { id: 'dongguan', name: '东莞市' },
        { id: 'zhongshan', name: '中山市' },
        { id: 'chaozhou', name: '潮州市' },
        { id: 'jieyang', name: '揭阳市' },
        { id: 'yunfu', name: '云浮市' }
    ],
    '广西壮族自治区': [
        { id: 'nanning', name: '南宁市' },
        { id: 'liuzhou', name: '柳州市' },
        { id: 'guilin', name: '桂林市' },
        { id: 'wuzhou', name: '梧州市' },
        { id: 'beihai', name: '北海市' },
        { id: 'fangchenggang', name: '防城港市' },
        { id: 'qinzhou', name: '钦州市' },
        { id: 'guigang', name: '贵港市' },
        { id: 'yulin_gx', name: '玉林市' },
        { id: 'baise', name: '百色市' },
        { id: 'hezhou', name: '贺州市' },
        { id: 'hechi', name: '河池市' },
        { id: 'laibin', name: '来宾市' },
        { id: 'chongzuo', name: '崇左市' }
    ],
    '海南省': [
        { id: 'haikou', name: '海口市' },
        { id: 'sanya', name: '三亚市' },
        { id: 'sansha', name: '三沙市' },
        { id: 'danzhou', name: '儋州市' }
    ],
    '四川省': [
        { id: 'chengdu', name: '成都市' },
        { id: 'zigong', name: '自贡市' },
        { id: 'panzhihua', name: '攀枝花市' },
        { id: 'luzhou', name: '泸州市' },
        { id: 'deyang', name: '德阳市' },
        { id: 'mianyang', name: '绵阳市' },
        { id: 'guangyuan', name: '广元市' },
        { id: 'suining', name: '遂宁市' },
        { id: 'neijiang', name: '内江市' },
        { id: 'leshan', name: '乐山市' },
        { id: 'nanchong', name: '南充市' },
        { id: 'meishan', name: '眉山市' },
        { id: 'yibin', name: '宜宾市' },
        { id: 'guangan', name: '广安市' },
        { id: 'dazhou', name: '达州市' },
        { id: 'yaan', name: '雅安市' },
        { id: 'bazhong', name: '巴中市' },
        { id: 'ziyang', name: '资阳市' },
        { id: 'aba', name: '阿坝藏族羌族自治州' },
        { id: 'ganzi', name: '甘孜藏族自治州' },
        { id: 'liangshan', name: '凉山彝族自治州' }
    ],
    '贵州省': [
        { id: 'guiyang', name: '贵阳市' },
        { id: 'liupanshui', name: '六盘水市' },
        { id: 'zunyi', name: '遵义市' },
        { id: 'anshun', name: '安顺市' },
        { id: 'bijie', name: '毕节市' },
        { id: 'tongren', name: '铜仁市' },
        { id: 'qianxinan', name: '黔西南布依族苗族自治州' },
        { id: 'qiandongnan', name: '黔东南苗族侗族自治州' },
        { id: 'qiannan', name: '黔南布依族苗族自治州' }
    ],
    '云南省': [
        { id: 'kunming', name: '昆明市' },
        { id: 'qujing', name: '曲靖市' },
        { id: 'yuxi', name: '玉溪市' },
        { id: 'baoshan', name: '保山市' },
        { id: 'zhaotong', name: '昭通市' },
        { id: 'lijiang', name: '丽江市' },
        { id: 'puer', name: '普洱市' },
        { id: 'lincang', name: '临沧市' },
        { id: 'chuxiong', name: '楚雄彝族自治州' },
        { id: 'honghe', name: '红河哈尼族彝族自治州' },
        { id: 'wenshan', name: '文山壮族苗族自治州' },
        { id: 'xishuangbanna', name: '西双版纳傣族自治州' },
        { id: 'dali', name: '大理白族自治州' },
        { id: 'dehong', name: '德宏傣族景颇族自治州' },
        { id: 'nujiang', name: '怒江傈僳族自治州' },
        { id: 'diqing', name: '迪庆藏族自治州' }
    ],
    '西藏自治区': [
        { id: 'lhasa', name: '拉萨市' },
        { id: 'rikaze', name: '日喀则市' },
        { id: 'changdu', name: '昌都市' },
        { id: 'linzhi', name: '林芝市' },
        { id: 'shannan', name: '山南市' },
        { id: 'naqu', name: '那曲市' },
        { id: 'ali', name: '阿里地区' }
    ],
    '陕西省': [
        { id: 'xian', name: '西安市' },
        { id: 'tongchuan', name: '铜川市' },
        { id: 'baoji', name: '宝鸡市' },
        { id: 'xianyang', name: '咸阳市' },
        { id: 'weinan', name: '渭南市' },
        { id: 'yanan', name: '延安市' },
        { id: 'hanzhong', name: '汉中市' },
        { id: 'yulin_sx', name: '榆林市' },
        { id: 'ankang', name: '安康市' },
        { id: 'shangluo', name: '商洛市' }
    ],
    '甘肃省': [
        { id: 'lanzhou', name: '兰州市' },
        { id: 'jiayuguan', name: '嘉峪关市' },
        { id: 'jinchang', name: '金昌市' },
        { id: 'baiyin', name: '白银市' },
        { id: 'tianshui', name: '天水市' },
        { id: 'wuwei', name: '武威市' },
        { id: 'zhangye', name: '张掖市' },
        { id: 'pingliang', name: '平凉市' },
        { id: 'jiuquan', name: '酒泉市' },
        { id: 'qingyang', name: '庆阳市' },
        { id: 'dingxi', name: '定西市' },
        { id: 'longnan', name: '陇南市' },
        { id: 'linxia', name: '临夏回族自治州' },
        { id: 'gannan', name: '甘南藏族自治州' }
    ],
    '青海省': [
        { id: 'xining', name: '西宁市' },
        { id: 'haidong', name: '海东市' },
        { id: 'haibei', name: '海北藏族自治州' },
        { id: 'huangnan', name: '黄南藏族自治州' },
        { id: 'hainan_qh', name: '海南藏族自治州' },
        { id: 'guoluo', name: '果洛藏族自治州' },
        { id: 'yushu', name: '玉树藏族自治州' },
        { id: 'haixi', name: '海西蒙古族藏族自治州' }
    ],
    '宁夏回族自治区': [
        { id: 'yinchuan', name: '银川市' },
        { id: 'shizuishan', name: '石嘴山市' },
        { id: 'wuzhong', name: '吴忠市' },
        { id: 'guyuan', name: '固原市' },
        { id: 'zhongwei', name: '中卫市' }
    ],
    '新疆维吾尔自治区': [
        { id: 'urumqi', name: '乌鲁木齐市' },
        { id: 'karamay', name: '克拉玛依市' },
        { id: 'turpan', name: '吐鲁番市' },
        { id: 'hami', name: '哈密市' },
        { id: 'changji', name: '昌吉回族自治州' },
        { id: 'bortala', name: '博尔塔拉蒙古自治州' },
        { id: 'bayingolin', name: '巴音郭楞蒙古自治州' },
        { id: 'aksu', name: '阿克苏地区' },
        { id: 'kizilsu', name: '克孜勒苏柯尔克孜自治州' },
        { id: 'kashgar', name: '喀什地区' },
        { id: 'hotan', name: '和田地区' },
        { id: 'ili', name: '伊犁哈萨克自治州' },
        { id: 'tacheng', name: '塔城地区' },
        { id: 'altay', name: '阿勒泰地区' }
    ],
    '内蒙古自治区': [
        { id: 'hohhot', name: '呼和浩特市' },
        { id: 'baotou', name: '包头市' },
        { id: 'wuhai', name: '乌海市' },
        { id: 'chifeng', name: '赤峰市' },
        { id: 'tongliao', name: '通辽市' },
        { id: 'ordos', name: '鄂尔多斯市' },
        { id: 'hulunbuir', name: '呼伦贝尔市' },
        { id: 'bayannur', name: '巴彦淖尔市' },
        { id: 'ulanqab', name: '乌兰察布市' },
        { id: 'hinggan', name: '兴安盟' },
        { id: 'xilingol', name: '锡林郭勒盟' },
        { id: 'alxa', name: '阿拉善盟' }
    ],

    // 特别行政区
    '香港特别行政区': [
        { id: 'hongkong', name: '香港' }
    ],
    '澳门特别行政区': [
        { id: 'macau', name: '澳门' }
    ]
};

$(document).ready(function() {
    bindEvents();

    // 显示选择提示
    showSelectionPrompt();

    console.log('页面初始化完成');
});

function bindEvents() {
    // 省份选择变化
    $('#provinceSelect').on('change', function() {
        currentProvince = $(this).val();
        currentCity = null;

        if (currentProvince) {
            updateCitySelector(currentProvince);
            $('#citySelectContainer').show();
            // 显示选择提示，等待用户选择城市
            showSelectionPrompt();
        } else {
            $('#citySelectContainer').hide();
            clearAllCharts();
            showSelectionPrompt();
        }
    });

    // 城市选择变化
    $('#citySelect').on('change', function() {
        const cityId = $(this).val();
        if (cityId) {
            // 存储城市ID，在数据加载时转换为城市名称
            currentCity = cityId;
            // 隐藏选择提示，显示图表
            hideSelectionPrompt();
            // 直接加载所有图表数据
            loadAllCharts();
        } else {
            currentCity = null;
            clearAllCharts();
            showSelectionPrompt();
        }
    });





    // 淹没范围视图切换
    $('input[name="extentView"]').on('change', function() {
        updateExtentChart($(this).val());
    });

    // 工具按钮
    $('#refreshBtn').on('click', refreshAllData);
}

// 省份城市相关辅助函数
function updateCitySelector(province) {
    const $citySelect = $('#citySelect');
    $citySelect.empty().append('<option value="">请选择城市...</option>');

    if (provinceCityData[province]) {
        provinceCityData[province].forEach(city => {
            $citySelect.append(`<option value="${city.id}">${city.name}</option>`);
        });
    }
}

function findProvinceByCity(cityId) {
    for (const [province, cities] of Object.entries(provinceCityData)) {
        if (cities.some(city => city.id === cityId)) {
            return province;
        }
    }
    return null;
}

function getCityNameById(cityId) {
    for (const [province, cities] of Object.entries(provinceCityData)) {
        const city = cities.find(city => city.id === cityId);
        if (city) {
            return city.name;
        }
    }
    return cityId; // 如果找不到，返回原ID
}



// 显示图表卡片
function showChartCard(cardId) {
    const card = document.getElementById(cardId);
    if (card) {
        card.style.display = 'block';

        // 显示canvas
        const canvas = card.querySelector('canvas');
        if (canvas) {
            canvas.style.display = 'block';
        }

        // 移除无数据提示
        const noDataDiv = card.querySelector('.no-data-message');
        if (noDataDiv) {
            noDataDiv.remove();
        }
    }
}

// 隐藏图表卡片并显示无数据提示
function hideChartCard(cardId, message) {
    const card = document.getElementById(cardId);
    if (card) {
        // 隐藏图表内容
        const canvas = card.querySelector('canvas');
        if (canvas) {
            canvas.style.display = 'none';
        }

        // 显示无数据提示
        let noDataDiv = card.querySelector('.no-data-message');
        if (!noDataDiv) {
            noDataDiv = document.createElement('div');
            noDataDiv.className = 'no-data-message text-center py-4';
            noDataDiv.innerHTML = `
                <i class="fas fa-exclamation-circle text-muted mb-2" style="font-size: 2rem;"></i>
                <p class="text-muted mb-0">${message}</p>
            `;
            const cardBody = card.querySelector('.card-body');
            if (cardBody) {
                cardBody.appendChild(noDataDiv);
            }
        } else {
            noDataDiv.querySelector('p').textContent = message;
        }
    }
}

// 显示选择提示
function showSelectionPrompt() {
    // 隐藏所有图表卡片
    const chartCardIds = [
        'rainfallChartCard', 'averageElevationChartCard', 'drainageDensityChartCard',
        'populationDensityChartCard', 'gdpDensityChartCard'
    ];
    chartCardIds.forEach(cardId => {
        const card = document.getElementById(cardId);
        if (card) {
            card.style.display = 'none';
        }
    });

    // 显示或创建选择提示
    let promptDiv = document.getElementById('selectionPrompt');
    if (!promptDiv) {
        promptDiv = document.createElement('div');
        promptDiv.id = 'selectionPrompt';
        promptDiv.className = 'text-center py-5';
        promptDiv.innerHTML = `
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <i class="fas fa-map-marker-alt text-primary mb-3" style="font-size: 3rem;"></i>
                    <h4 class="text-muted mb-3">请选择省份和城市</h4>
                    <p class="text-muted">
                        请先在上方选择省份，然后选择具体城市，<br>
                        系统将为您展示该地区的详细数据分析图表
                    </p>
                    <div class="mt-4">
                        <span class="badge bg-info me-2">
                            <i class="fas fa-info-circle me-1"></i>
                            数据来源：省份管理页面导入的历史数据
                        </span>
                    </div>
                </div>
            </div>
        `;

        // 将提示插入到图表区域
        const chartsContainer = document.querySelector('.charts-container') || document.querySelector('.container-fluid');
        if (chartsContainer) {
            chartsContainer.appendChild(promptDiv);
        }
    } else {
        promptDiv.style.display = 'block';
    }
}

// 隐藏选择提示
function hideSelectionPrompt() {
    const promptDiv = document.getElementById('selectionPrompt');
    if (promptDiv) {
        promptDiv.style.display = 'none';
    }

    // 显示所有图表卡片
    const chartCardIds = [
        'rainfallChartCard', 'averageElevationChartCard', 'drainageDensityChartCard',
        'populationDensityChartCard', 'gdpDensityChartCard'
    ];
    chartCardIds.forEach(cardId => {
        const card = document.getElementById(cardId);
        if (card) {
            card.style.display = 'block';
        }
    });
}

function loadAllCharts() {
    if (!currentCity) return Promise.resolve();

    // 清除所有图表的无数据提示
    clearAllCharts();

    showLoadingSpinner();

    // 加载所有图表数据
    return Promise.all([
        loadRainfallData(),
        loadAverageElevationData(),
        loadDrainageDensityData(),
        loadPopulationDensityData(),
        loadGdpDensityData()
    ]).then(() => {
        hideLoadingSpinner();
    }).catch(error => {
        console.error('加载图表数据失败:', error);
        hideLoadingSpinner();
        showAlert('加载数据失败，请重试', 'danger');
        throw error; // 重新抛出错误，让调用者能够处理
    });
}

function loadRainfallData() {
    // 将城市ID转换为城市名称
    const cityName = getCityNameById(currentCity);
    return fetch(`/visualization/api/charts/rainfall?province=${currentProvince}&city=${encodeURIComponent(cityName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                chartData.rainfall = data.data;
                showChartCard('rainfallChartCard');
                createRainfallChart();
            } else {
                console.warn('降雨量数据加载失败:', data.error);
                chartData.rainfall = null;
                hideChartCard('rainfallChartCard', `暂无降雨量数据: ${data.error}`);
            }
        })
        .catch(error => {
            console.error('降雨量数据请求失败:', error);
            chartData.rainfall = null;
            hideChartCard('rainfallChartCard', '降雨量数据加载失败');
        });
}

function loadAverageElevationData() {
    // 将城市ID转换为城市名称
    const cityName = getCityNameById(currentCity);
    return fetch(`/visualization/api/charts/average-elevation?province=${currentProvince}&city=${encodeURIComponent(cityName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                chartData.averageElevation = data.data;
                showChartCard('averageElevationChartCard');
                createAverageElevationChart();
            } else {
                console.warn('平均高程数据加载失败:', data.error);
                chartData.averageElevation = null;
                hideChartCard('averageElevationChartCard', `暂无平均高程数据: ${data.error || '数据不存在'}`);
            }
        })
        .catch(error => {
            console.error('平均高程数据请求失败:', error);
            chartData.averageElevation = null;
            hideChartCard('averageElevationChartCard', '平均高程数据加载失败');
        });
}

function loadElevationData() {
    return fetch(`/visualization/api/charts/flood-elevation?city=${currentCity}&year=${currentYear}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                chartData.elevation = data.data;
                showChartCard('elevationChartCard');
                createElevationChart();
            } else {
                console.warn('高程数据加载失败:', data.error);
                chartData.elevation = null;
                hideChartCard('elevationChartCard', `暂无高程数据: ${data.error || '数据不存在'}`);
            }
        })
        .catch(error => {
            console.error('高程数据请求失败:', error);
            chartData.elevation = null;
            hideChartCard('elevationChartCard', '高程数据加载失败');
        });
}

function loadExtentData() {
    console.log(`加载淹没范围数据: city=${currentCity}, year=${currentYear}`);
    return fetch(`/visualization/api/charts/flood-extent?city=${currentCity}&year=${currentYear}`)
        .then(response => response.json())
        .then(data => {
            console.log('淹没范围API响应:', data);
            if (data.success && data.data) {
                chartData.extent = data.data;
                console.log('淹没范围数据:', chartData.extent);
            } else {
                console.error('淹没范围API返回失败:', data.error);
                chartData.extent = null;
            }
            createExtentChart();
            createExtentPieChart();
        })
        .catch(error => {
            console.error('加载淹没范围数据失败:', error);
            chartData.extent = null;
            createExtentChart();
            createExtentPieChart();
        });
}

function createRainfallChart(chartType = 'bar') {
    if (!chartData.rainfall) return;

    // 确保canvas可见
    const canvas = document.getElementById('rainfallChart');
    if (canvas) {
        canvas.style.display = 'block';
    }

    const ctx = canvas.getContext('2d');

    if (charts.rainfall) {
        charts.rainfall.destroy();
    }

    // 根据数据类型确定标签和数据
    const isTimeSeriesData = chartData.rainfall.years !== undefined;
    const labels = isTimeSeriesData ? chartData.rainfall.years.map(year => year + '年') : chartData.rainfall.regions;
    const data = chartData.rainfall.rainfall;

    // 计算Y轴范围
    const axisRange = calculateAxisRange(data);

    // 根据图表类型设置不同的颜色和样式
    let dataset = {
        label: isTimeSeriesData ? '年降雨量趋势' : '年降雨量',
        data: data
    };

    if (chartType === 'line') {
        dataset.borderColor = 'rgb(54, 162, 235)';
        dataset.backgroundColor = 'rgba(54, 162, 235, 0.1)';
        dataset.fill = true;
        dataset.tension = 0.4;
        dataset.pointRadius = 3;
        dataset.pointHoverRadius = 5;
    } else if (chartType === 'bar') {
        // 柱状图使用渐变色，颜色更明显
        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, 'rgba(54, 162, 235, 0.9)');
        gradient.addColorStop(1, 'rgba(54, 162, 235, 0.6)');
        dataset.backgroundColor = gradient;
        dataset.borderColor = 'rgb(54, 162, 235)';
        dataset.borderWidth = 2;
    } else if (chartType === 'area') {
        // 面积图实际是填充的折线图
        dataset.borderColor = 'rgb(54, 162, 235)';
        dataset.backgroundColor = 'rgba(54, 162, 235, 0.3)';
        dataset.fill = 'origin';
        dataset.tension = 0.4;
        dataset.pointRadius = 0;
        dataset.pointHoverRadius = 4;
    }

    charts.rainfall = new Chart(ctx, {
        type: chartType === 'area' ? 'line' : chartType,
        data: {
            labels: labels,
            datasets: [dataset]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return `降雨量: ${context.parsed.y}mm`;
                        }
                    }
                }
            },
            scales: {
                x: createXAxisConfig(),
                y: {
                    beginAtZero: chartType === 'bar' ? true : false,  // 柱状图从0开始，其他图表自动调整
                    min: chartType === 'bar' ? 0 : axisRange.min,
                    max: axisRange.max,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    title: {
                        display: true,
                        text: '降雨量 (mm)'
                    },
                    ticks: {
                        callback: function(value) {
                            return Math.round(value) + 'mm';
                        },
                        maxTicksLimit: 8
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });

    // 更新统计信息
    if (chartData.rainfall) {
        $('#rainfallAvg').text(chartData.rainfall.average + 'mm');
        $('#rainfallMax').text(chartData.rainfall.max + 'mm');
        $('#rainfallTotal').text(chartData.rainfall.total + 'mm');
    }
}

function createAverageElevationChart(chartType = 'line') {
    if (!chartData.averageElevation) return;

    // 确保canvas可见
    const canvas = document.getElementById('averageElevationChart');
    if (canvas) {
        canvas.style.display = 'block';
    }

    const ctx = canvas.getContext('2d');

    if (charts.averageElevation) {
        charts.averageElevation.destroy();
    }

    // 根据数据类型确定标签和数据
    const isTimeSeriesData = chartData.averageElevation.years !== undefined;
    const labels = isTimeSeriesData ? chartData.averageElevation.years.map(year => year + '年') : chartData.averageElevation.regions;
    const data = chartData.averageElevation.elevation;

    // 计算Y轴范围
    const axisRange = calculateAxisRange(data);

    // 根据图表类型设置不同的颜色和样式
    let dataset = {
        label: isTimeSeriesData ? '平均高程趋势' : '平均高程',
        data: data
    };

    if (chartType === 'bar') {
        dataset.backgroundColor = 'rgba(40, 167, 69, 0.6)';
        dataset.borderColor = 'rgba(40, 167, 69, 1)';
        dataset.borderWidth = 1;
    } else if (chartType === 'area') {
        dataset.backgroundColor = 'rgba(40, 167, 69, 0.2)';
        dataset.borderColor = 'rgba(40, 167, 69, 1)';
        dataset.borderWidth = 2;
        dataset.fill = true;
        dataset.tension = 0.4;
    } else { // line
        dataset.backgroundColor = 'rgba(40, 167, 69, 0.1)';
        dataset.borderColor = 'rgba(40, 167, 69, 1)';
        dataset.borderWidth = 2;
        dataset.fill = false;
        dataset.tension = 0.4;
    }

    charts.averageElevation = new Chart(ctx, {
        type: chartType === 'area' ? 'line' : chartType,
        data: {
            labels: labels,
            datasets: [dataset]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: isTimeSeriesData ? '平均高程变化趋势' : '平均高程分布'
                },
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return `平均高程: ${context.parsed.y}m`;
                        }
                    }
                }
            },
            scales: {
                x: createXAxisConfig(),
                y: {
                    beginAtZero: chartType === 'bar' ? true : false,  // 柱状图从0开始，其他图表自动调整
                    min: chartType === 'bar' ? 0 : axisRange.min,
                    max: axisRange.max,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    title: {
                        display: true,
                        text: '平均高程 (m)'
                    },
                    ticks: {
                        callback: function(value) {
                            return Math.round(value) + 'm';
                        },
                        maxTicksLimit: 8
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });

    // 更新统计信息
    if (chartData.averageElevation) {
        $('#elevationAvg').text(chartData.averageElevation.average + 'm');
        $('#elevationMax').text(chartData.averageElevation.max + 'm');
        $('#elevationMin').text(chartData.averageElevation.min + 'm');
    }
}

function createElevationChart(chartType = 'line') {
    const ctx = document.getElementById('elevationChart').getContext('2d');
    
    if (charts.elevation) {
        charts.elevation.destroy();
    }
    
    // 设置不同图表类型的数据集
    let datasets = [];
    
    if (chartType === 'bar') {
        // 柱状图使用更鲜明的颜色
        const maxGradient = ctx.createLinearGradient(0, 0, 0, 400);
        maxGradient.addColorStop(0, 'rgba(255, 99, 132, 0.9)');
        maxGradient.addColorStop(1, 'rgba(255, 99, 132, 0.6)');
        
        const avgGradient = ctx.createLinearGradient(0, 0, 0, 400);
        avgGradient.addColorStop(0, 'rgba(255, 206, 86, 0.9)');
        avgGradient.addColorStop(1, 'rgba(255, 206, 86, 0.6)');
        
        datasets = [{
            label: '最大淹没高程',
            data: chartData.elevation.max_elevation,
            backgroundColor: maxGradient,
            borderColor: 'rgb(255, 99, 132)',
            borderWidth: 2
        }, {
            label: '平均淹没高程',
            data: chartData.elevation.avg_elevation,
            backgroundColor: avgGradient,
            borderColor: 'rgb(255, 206, 86)',
            borderWidth: 2
        }];
    } else if (chartType === 'scatter') {
        datasets = [{
            label: '最大淹没高程',
            data: chartData.elevation.max_elevation,
            backgroundColor: 'rgba(255, 99, 132, 0.7)',
            borderColor: 'rgb(255, 99, 132)',
            pointRadius: 6,
            pointHoverRadius: 8
        }, {
            label: '平均淹没高程',
            data: chartData.elevation.avg_elevation,
            backgroundColor: 'rgba(255, 206, 86, 0.7)',
            borderColor: 'rgb(255, 206, 86)',
            pointRadius: 6,
            pointHoverRadius: 8
        }];
    } else {
        // 默认折线图
        datasets = [{
            label: '最大淹没高程',
            data: chartData.elevation.max_elevation,
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            fill: false,
            tension: 0.4,
            pointRadius: 3
        }, {
            label: '平均淹没高程',
            data: chartData.elevation.avg_elevation,
            borderColor: 'rgb(255, 206, 86)',
            backgroundColor: 'rgba(255, 206, 86, 0.1)',
            fill: false,
            tension: 0.4,
            pointRadius: 3
        }];
    }
    
    charts.elevation = new Chart(ctx, {
        type: chartType,
        data: {
            labels: chartData.elevation.dates,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 15
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${context.parsed.y}m`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxTicksLimit: 10
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value + 'm';
                        }
                    }
                }
            }
        }
    });
}

function createExtentChart() {
    console.log('创建淹没范围图表');
    const ctx = document.getElementById('extentChart').getContext('2d');

    if (!chartData.extent) {
        console.error('淹没范围数据为空，无法创建图表');
        return;
    }

    console.log('当前淹没范围数据:', chartData.extent);

    if (charts.extent) {
        charts.extent.destroy();
    }

    const view = $('input[name="extentView"]:checked').val();
    console.log('当前视图模式:', view);
    console.log('可用数据:', {
        dates: chartData.extent.dates?.length,
        affected_area: chartData.extent.affected_area?.length,
        affected_population: chartData.extent.affected_population?.length
    });
    let datasets = [];

    if (view === 'area' || view === 'combined') {
        // 创建渐变色
        const areaGradient = ctx.createLinearGradient(0, 0, 0, 400);
        areaGradient.addColorStop(0, 'rgba(255, 140, 0, 0.8)');
        areaGradient.addColorStop(1, 'rgba(255, 140, 0, 0.4)');

        datasets.push({
            label: '淹没面积',
            data: chartData.extent.affected_area,
            borderColor: 'rgb(255, 140, 0)',  // 更深的橙色
            backgroundColor: areaGradient,  // 使用渐变色
            yAxisID: 'y',
            type: 'bar',
            borderWidth: 2
        });
    }

    if (view === 'population' || view === 'combined') {
        // 为人口数据创建渐变色
        const populationGradient = ctx.createLinearGradient(0, 0, 0, 400);
        populationGradient.addColorStop(0, 'rgba(138, 43, 226, 0.8)');
        populationGradient.addColorStop(1, 'rgba(138, 43, 226, 0.4)');

        datasets.push({
            label: '受影响人口',
            data: chartData.extent.affected_population,
            borderColor: 'rgb(138, 43, 226)',  // 更深的紫色
            backgroundColor: view === 'combined' ? 'rgba(138, 43, 226, 0.6)' : populationGradient,
            yAxisID: view === 'combined' ? 'y1' : 'y',
            type: view === 'combined' ? 'line' : 'bar',
            borderWidth: 2,
            pointRadius: view === 'combined' ? 4 : 0,
            pointHoverRadius: view === 'combined' ? 6 : 0
        });
    }

    console.log('创建的数据集:', datasets);
    console.log('数据集数量:', datasets.length);

    charts.extent = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: chartData.extent.dates,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true
                    }
                }
            },
            scales: getExtentScales(view)
        }
    });
}

function createExtentPieChart() {
    const ctx = document.getElementById('extentPieChart').getContext('2d');
    
    if (charts.extentPie) {
        charts.extentPie.destroy();
    }
    
    // 计算风险等级分布
    const riskDistribution = [
        Math.floor(Math.random() * 30) + 10, // 高风险
        Math.floor(Math.random() * 40) + 20, // 中风险
        Math.floor(Math.random() * 50) + 30  // 低风险
    ];
    
    charts.extentPie = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['高风险区域', '中风险区域', '低风险区域'],
            datasets: [{
                data: riskDistribution,
                backgroundColor: [
                    'rgba(220, 20, 60, 0.9)',   // 深红色 - 高风险
                    'rgba(255, 165, 0, 0.9)',   // 橙色 - 中风险
                    'rgba(34, 139, 34, 0.9)'    // 森林绿 - 低风险
                ],
                borderColor: [
                    'rgb(220, 20, 60)',
                    'rgb(255, 165, 0)',
                    'rgb(34, 139, 34)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 10,
                        usePointStyle: true,
                        font: {
                            size: 11
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${context.label}: ${percentage}%`;
                        }
                    }
                }
            }
        }
    });
}

function getExtentScales(view) {
    if (view === 'combined') {
        return {
            x: {
                grid: { display: false },
                ticks: { maxTicksLimit: 8 }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                grid: { color: 'rgba(0,0,0,0.1)' },
                ticks: {
                    callback: function(value) {
                        return value + ' km²';
                    }
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                grid: { drawOnChartArea: false },
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' 人';
                    }
                }
            }
        };
    } else {
        return {
            x: {
                grid: { display: false },
                ticks: { maxTicksLimit: 8 }
            },
            y: {
                beginAtZero: true,
                grid: { color: 'rgba(0,0,0,0.1)' },
                ticks: {
                    callback: function(value) {
                        if (view === 'area') {
                            return value + ' km²';
                        } else {
                            return value.toLocaleString() + ' 人';
                        }
                    }
                }
            }
        };
    }
}

function updateExtentChart(view) {
    if (chartData.extent) {
        createExtentChart();
    }
}





function changeChartType(chartName, type) {
    if (charts[chartName] && chartData[chartName]) {
        // 销毁现有图表
        charts[chartName].destroy();

        // 根据图表类型重新创建
        if (chartName === 'rainfall') {
            createRainfallChart(type);
        } else if (chartName === 'averageElevation') {
            createAverageElevationChart(type);
        } else if (chartName === 'elevation') {
            createElevationChart(type);
        } else if (chartName === 'drainageDensity') {
            createDrainageDensityChart(type);
        } else if (chartName === 'populationDensity') {
            createPopulationDensityChart(type);
        } else if (chartName === 'gdpDensity') {
            createGdpDensityChart(type);
        }
    }
}

function refreshAllData() {
    const $btn = $('#refreshBtn');
    const originalHtml = $btn.html();
    
    $btn.html('<i class="fas fa-spinner fa-spin me-1"></i>刷新中...').prop('disabled', true);
    
    // 清除缓存数据
    chartData = {};
    
    if (currentCity) {
        loadAllCharts().finally(() => {
            setTimeout(() => {
                $btn.html(originalHtml).prop('disabled', false);
                showAlert('数据已刷新', 'success');
            }, 1000);
        });
    } else {
        setTimeout(() => {
            $btn.html(originalHtml).prop('disabled', false);
            showAlert('请先选择城市', 'warning');
        }, 500);
    }
}










function clearAllCharts() {
    Object.values(charts).forEach(chart => {
        if (chart) chart.destroy();
    });
    charts = {};
    chartData = {};

    // 清除所有无数据提示
    document.querySelectorAll('.no-data-message').forEach(element => {
        element.remove();
    });

    // 显示所有canvas
    document.querySelectorAll('.card-body canvas').forEach(canvas => {
        canvas.style.display = 'block';
    });
}

function showLoadingSpinner() {
    // 在图表容器中显示加载动画
    $('.card-body canvas').each(function() {
        const container = $(this).parent();
        container.append(`
            <div class="loading-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `);
    });
}

function hideLoadingSpinner() {
    $('.loading-spinner').remove();
}



// 排水管道密度数据加载
function loadDrainageDensityData() {
    // 将城市ID转换为城市名称
    const cityName = getCityNameById(currentCity);
    return fetch(`/visualization/api/charts/drainage-density?province=${currentProvince}&city=${encodeURIComponent(cityName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                chartData.drainageDensity = data.data;
                showChartCard('drainageDensityChartCard');
                createDrainageDensityChart();
            } else {
                console.warn('排水管密度数据加载失败:', data.error);
                chartData.drainageDensity = null;
                hideChartCard('drainageDensityChartCard', `暂无排水管密度数据: ${data.error || '数据不存在'}`);
            }
        })
        .catch(error => {
            console.error('排水管密度数据请求失败:', error);
            chartData.drainageDensity = null;
            hideChartCard('drainageDensityChartCard', '排水管密度数据加载失败');
        });
}

// 人口密度数据加载
function loadPopulationDensityData() {
    // 将城市ID转换为城市名称
    const cityName = getCityNameById(currentCity);
    return fetch(`/visualization/api/charts/population-density?province=${currentProvince}&city=${encodeURIComponent(cityName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                chartData.populationDensity = data.data;
                showChartCard('populationDensityChartCard');
                createPopulationDensityChart();
            } else {
                console.warn('人口密度数据加载失败:', data.error);
                chartData.populationDensity = null;
                hideChartCard('populationDensityChartCard', `暂无人口密度数据: ${data.error || '数据不存在'}`);
            }
        })
        .catch(error => {
            console.error('人口密度数据请求失败:', error);
            chartData.populationDensity = null;
            hideChartCard('populationDensityChartCard', '人口密度数据加载失败');
        });
}

// 创建排水管道密度图表
function createDrainageDensityChart(chartType = 'line') {
    if (!chartData.drainageDensity) return;

    const ctx = document.getElementById('drainageDensityChart').getContext('2d');

    if (charts.drainageDensity) {
        charts.drainageDensity.destroy();
    }

    const data = chartData.drainageDensity;
    const isTimeSeriesData = data.years !== undefined;
    const labels = isTimeSeriesData ? data.years.map(year => year + '年') : data.regions;

    // 计算Y轴范围
    const axisRange = calculateAxisRange(data.density);

    // 根据图表类型设置不同的颜色和样式
    let dataset = {
        label: isTimeSeriesData ? '排水管道密度趋势' : '排水管道密度',
        data: data.density
    };

    if (chartType === 'bar') {
        dataset.backgroundColor = 'rgba(23, 162, 184, 0.6)';
        dataset.borderColor = 'rgba(23, 162, 184, 1)';
        dataset.borderWidth = 1;
    } else if (chartType === 'area') {
        dataset.backgroundColor = 'rgba(23, 162, 184, 0.2)';
        dataset.borderColor = 'rgba(23, 162, 184, 1)';
        dataset.borderWidth = 2;
        dataset.fill = true;
        dataset.tension = 0.4;
    } else { // line
        dataset.backgroundColor = 'rgba(23, 162, 184, 0.1)';
        dataset.borderColor = 'rgba(23, 162, 184, 1)';
        dataset.borderWidth = 2;
        dataset.fill = false;
        dataset.tension = 0.4;
    }

    charts.drainageDensity = new Chart(ctx, {
        type: chartType === 'area' ? 'line' : chartType,
        data: {
            labels: labels,
            datasets: [dataset]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: isTimeSeriesData ? '排水管道密度变化趋势' : '各区域排水管道密度对比'
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: createYAxisConfig(data, axisRange, data.unit || 'km/km²', 'default'),
                x: createXAxisConfig()
            }
        }
    });
}

// 计算合适的Y轴范围
function calculateAxisRange(values) {
    if (!values || values.length === 0) return { min: 0, max: 100 };

    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    const range = maxValue - minValue;

    // 如果所有值都相同，设置一个合理的范围
    if (range === 0) {
        const baseValue = minValue || 1;
        return {
            min: Math.max(0, baseValue * 0.9),
            max: baseValue * 1.1
        };
    }

    // 计算合适的边距（5-15%）
    const margin = Math.max(range * 0.05, range * 0.15);

    return {
        min: Math.max(0, minValue - margin),
        max: maxValue + margin
    };
}

// 格式化数值显示
function formatValue(value, unit, type = 'default') {
    if (type === 'population') {
        if (value >= 10000) {
            return (value / 10000).toFixed(1) + '万 ' + unit;
        } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'k ' + unit;
        } else if (value < 1) {
            return value.toFixed(2) + ' ' + unit;
        } else {
            return value.toFixed(1) + ' ' + unit;
        }
    } else if (type === 'gdp') {
        if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M ' + unit;
        } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K ' + unit;
        } else {
            return Math.round(value) + ' ' + unit;
        }
    } else {
        return value.toFixed(1) + ' ' + unit;
    }
}

// 创建标准的Y轴配置
function createYAxisConfig(data, axisRange, unit, type = 'default') {
    return {
        beginAtZero: false,
        min: axisRange.min,
        max: axisRange.max,
        title: {
            display: true,
            text: unit
        },
        ticks: {
            callback: function(value) {
                return formatValue(value, unit, type);
            },
            maxTicksLimit: 8,
            precision: type === 'default' ? 1 : 0
        },
        grid: {
            color: 'rgba(0,0,0,0.1)'
        }
    };
}

// 创建标准的X轴配置
function createXAxisConfig() {
    return {
        grid: {
            display: false
        },
        ticks: {
            maxTicksLimit: 10
        }
    };
}

// 创建人口密度图表
function createPopulationDensityChart(chartType = 'line') {
    if (!chartData.populationDensity) return;

    const ctx = document.getElementById('populationDensityChart').getContext('2d');

    if (charts.populationDensity) {
        charts.populationDensity.destroy();
    }

    const data = chartData.populationDensity;
    const isTimeSeriesData = data.years !== undefined;
    const labels = isTimeSeriesData ? data.years.map(year => year + '年') : data.regions;

    // 计算Y轴范围
    const axisRange = calculateAxisRange(data.density);

    // 对于人口密度数据，如果值很小，调整Y轴范围
    if (isTimeSeriesData && data.max < 1) {
        axisRange.min = 0;
        axisRange.max = Math.max(data.max * 1.2, 1);
    }

    // 根据图表类型设置不同的颜色和样式
    let dataset = {
        label: isTimeSeriesData ? '人口密度趋势' : '人口密度',
        data: data.density
    };

    if (isTimeSeriesData) {
        if (chartType === 'bar') {
            dataset.backgroundColor = 'rgba(40, 167, 69, 0.6)';
            dataset.borderColor = 'rgba(40, 167, 69, 1)';
            dataset.borderWidth = 1;
        } else if (chartType === 'area') {
            dataset.backgroundColor = 'rgba(40, 167, 69, 0.2)';
            dataset.borderColor = 'rgba(40, 167, 69, 1)';
            dataset.borderWidth = 2;
            dataset.fill = true;
            dataset.tension = 0.4;
        } else { // line
            dataset.backgroundColor = 'rgba(40, 167, 69, 0.1)';
            dataset.borderColor = 'rgba(40, 167, 69, 1)';
            dataset.borderWidth = 2;
            dataset.fill = false;
            dataset.tension = 0.4;
        }
    } else {
        dataset.backgroundColor = [
            'rgba(40, 167, 69, 0.6)',
            'rgba(255, 193, 7, 0.6)',
            'rgba(220, 53, 69, 0.6)',
            'rgba(0, 123, 255, 0.6)',
            'rgba(108, 117, 125, 0.6)',
            'rgba(255, 99, 132, 0.6)'
        ];
        dataset.borderColor = [
            'rgba(40, 167, 69, 1)',
            'rgba(255, 193, 7, 1)',
            'rgba(220, 53, 69, 1)',
            'rgba(0, 123, 255, 1)',
            'rgba(108, 117, 125, 1)',
            'rgba(255, 99, 132, 1)'
        ];
        dataset.borderWidth = 1;
    }

    charts.populationDensity = new Chart(ctx, {
        type: isTimeSeriesData ? (chartType === 'area' ? 'line' : chartType) : 'doughnut',
        data: {
            labels: labels,
            datasets: [dataset]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: isTimeSeriesData ? '人口密度变化趋势' : '各区域人口密度分布'
                },
                legend: {
                    display: !isTimeSeriesData,
                    position: 'bottom'
                }
            },
            scales: isTimeSeriesData ? {
                y: createYAxisConfig(data, axisRange, '万人/km²', 'population'),
                x: createXAxisConfig()
            } : undefined
        }
    });
}

// 创建GDP密度图表
function createGdpDensityChart(chartType = 'line') {
    if (!chartData.gdpDensity) return;

    const ctx = document.getElementById('gdpDensityChart').getContext('2d');

    if (charts.gdpDensity) {
        charts.gdpDensity.destroy();
    }

    const data = chartData.gdpDensity;
    const isTimeSeriesData = data.years !== undefined;
    const labels = isTimeSeriesData ? data.years.map(year => year + '年') : data.regions;

    // 使用正确的数据字段名
    const values = data.gdp_density || data.density || [];

    // 计算Y轴范围
    const axisRange = calculateAxisRange(values);

    // 根据图表类型设置不同的颜色和样式
    let dataset = {
        label: isTimeSeriesData ? 'GDP密度趋势' : 'GDP密度',
        data: values
    };

    if (chartType === 'area') {
        dataset.fill = true;
        dataset.backgroundColor = 'rgba(255, 193, 7, 0.2)';
        dataset.borderColor = 'rgba(255, 193, 7, 1)';
        dataset.borderWidth = 2;
    } else if (chartType === 'bar') {
        dataset.backgroundColor = 'rgba(255, 193, 7, 0.8)';
        dataset.borderColor = 'rgba(255, 193, 7, 1)';
        dataset.borderWidth = 1;
    } else {
        dataset.borderColor = 'rgba(255, 193, 7, 1)';
        dataset.backgroundColor = 'rgba(255, 193, 7, 0.1)';
        dataset.borderWidth = 2;
        dataset.pointBackgroundColor = 'rgba(255, 193, 7, 1)';
        dataset.pointBorderColor = '#fff';
        dataset.pointBorderWidth = 2;
        dataset.pointRadius = 4;
    }

    // 如果是非时间序列数据，使用不同颜色
    if (!isTimeSeriesData) {
        dataset.backgroundColor = [
            'rgba(255, 193, 7, 0.8)',
            'rgba(40, 167, 69, 0.8)',
            'rgba(220, 53, 69, 0.8)',
            'rgba(0, 123, 255, 0.8)',
            'rgba(108, 117, 125, 0.8)',
            'rgba(255, 99, 132, 0.8)'
        ];
        dataset.borderColor = [
            'rgba(255, 193, 7, 1)',
            'rgba(40, 167, 69, 1)',
            'rgba(220, 53, 69, 1)',
            'rgba(0, 123, 255, 1)',
            'rgba(108, 117, 125, 1)',
            'rgba(255, 99, 132, 1)'
        ];
        dataset.borderWidth = 1;
    }

    charts.gdpDensity = new Chart(ctx, {
        type: isTimeSeriesData ? (chartType === 'area' ? 'line' : chartType) : 'doughnut',
        data: {
            labels: labels,
            datasets: [dataset]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: isTimeSeriesData ? 'GDP密度变化趋势' : '各区域GDP密度分布'
                },
                legend: {
                    display: !isTimeSeriesData,
                    position: 'bottom'
                }
            },
            scales: isTimeSeriesData ? {
                y: createYAxisConfig(data, axisRange, data.unit || '万元/km²', 'gdp'),
                x: createXAxisConfig()
            } : undefined
        }
    });

    // 更新统计信息
    if (isTimeSeriesData && data.average !== undefined) {
        updateGdpDensityStats(data);
    }
}

// 更新GDP密度统计信息
function updateGdpDensityStats(data) {
    const avgElement = document.getElementById('gdpDensityAvg');
    const maxElement = document.getElementById('gdpDensityMax');
    const totalElement = document.getElementById('gdpDensityTotal');

    if (avgElement) avgElement.textContent = data.average + ' ' + (data.unit || '万元/km²');
    if (maxElement) maxElement.textContent = data.max + ' ' + (data.unit || '万元/km²');
    if (totalElement) totalElement.textContent = data.total + ' ' + (data.unit || '万元/km²');
}

// GDP密度数据加载
function loadGdpDensityData() {
    // 将城市ID转换为城市名称
    const cityName = getCityNameById(currentCity);
    return fetch(`/visualization/api/charts/gdp-density?province=${currentProvince}&city=${encodeURIComponent(cityName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                chartData.gdpDensity = data.data;
                showChartCard('gdpDensityChartCard');
                createGdpDensityChart();
            } else {
                console.warn('GDP密度数据加载失败:', data.error);
                chartData.gdpDensity = null;
                hideChartCard('gdpDensityChartCard', `暂无GDP密度数据: ${data.error || '数据不存在'}`);
            }
        })
        .catch(error => {
            console.error('GDP密度数据请求失败:', error);
            chartData.gdpDensity = null;
            hideChartCard('gdpDensityChartCard', 'GDP密度数据加载失败');
        });
}

// 高程数据加载
function loadElevationDataChart() {
    // 将城市ID转换为城市名称
    const cityName = getCityNameById(currentCity);
    return fetch(`/visualization/api/charts/elevation?province=${currentProvince}&city=${encodeURIComponent(cityName)}&year=${currentYear}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                chartData.elevationData = data.data;
                showChartCard('elevationDataChartCard');
                createElevationDataChart();
            } else {
                console.warn('高程数据加载失败:', data.error);
                chartData.elevationData = null;
                hideChartCard('elevationDataChartCard', `暂无高程数据: ${data.error || '数据不存在'}`);
            }
        })
        .catch(error => {
            console.error('高程数据请求失败:', error);
            chartData.elevationData = null;
            hideChartCard('elevationDataChartCard', '高程数据加载失败');
        });
}
</script>
{% endblock %}