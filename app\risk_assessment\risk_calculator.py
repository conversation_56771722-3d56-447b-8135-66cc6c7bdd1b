"""
风险计算引擎
实现基于文档中公式的风险指数计算
"""
import math
from typing import Dict, List, Optional, Tuple
from app.models import RiskWeightConfig, RiskAssessmentData, RiskCalculationResult


class RiskCalculator:
    """风险计算引擎"""
    
    def __init__(self, weight_config: RiskWeightConfig):
        """
        初始化风险计算引擎
        
        Args:
            weight_config: 权重配置对象
        """
        self.weight_config = weight_config
        
    def calculate_likelihood_probability(self, annual_precipitation: float,
                                       drainage_network_density: float, elevation: float) -> float:
        """
        计算发生概率(P)
        公式: P = w_R × X_全年降水量 + w_D × X_排水管网密度 + w_T × X_高程

        Args:
            annual_precipitation: 全年降水量（标准化值 0-1）
            drainage_network_density: 排水管网密度（标准化值 0-1）
            elevation: 平均高程值（标准化值 0-1）

        Returns:
            发生概率得分 (0-1)
        """
        if annual_precipitation is None or drainage_network_density is None or elevation is None:
            return 0.0

        # 使用新的权重配置
        p_score = (self.weight_config.rainfall_weight * annual_precipitation +
                  self.weight_config.drainage_weight * drainage_network_density +
                  self.weight_config.elevation_weight * elevation)

        return max(0.0, min(1.0, p_score))  # 确保结果在0-1范围内
    
    def calculate_impact_degree(self, population_density: float, gdp_per_area: float) -> float:
        """
        计算影响程度(I)
        公式: I = 0.6 × X_GDP密度 + 0.4 × X_人口密度

        Args:
            population_density: 人口密度（标准化值 0-1）
            gdp_per_area: GDP密度（标准化值 0-1）

        Returns:
            影响程度得分 (0-1)
        """
        if population_density is None or gdp_per_area is None:
            return 0.0

        # 使用固定权重：GDP密度0.6，人口密度0.4
        i_score = (0.6 * gdp_per_area + 0.4 * population_density)

        return max(0.0, min(1.0, i_score))
    
    def calculate_risk_index(self, likelihood_probability: float, impact_degree: float) -> float:
        """
        计算综合风险指数(RI)
        基于发生概率和影响程度的计算

        Args:
            likelihood_probability: 发生概率得分 (0-1)
            impact_degree: 影响程度得分 (0-1)

        Returns:
            综合风险指数 (0-1)
        """
        # 风险指数计算：RI = P × I
        ri = likelihood_probability * impact_degree
        return max(0.0, min(1.0, ri))
    
    def calculate_single_assessment(self, assessment_data: RiskAssessmentData) -> Dict:
        """
        计算单个评估数据的风险指数

        Args:
            assessment_data: 风险评估数据对象

        Returns:
            包含所有计算结果的字典
        """
        # 计算发生概率
        likelihood_probability = self.calculate_likelihood_probability(
            assessment_data.annual_precipitation,
            assessment_data.drainage_network_density,
            assessment_data.elevation
        )

        # 计算影响程度
        impact_degree = self.calculate_impact_degree(
            assessment_data.population_density,
            assessment_data.gdp_per_area
        )

        # 计算综合风险指数
        ri = self.calculate_risk_index(likelihood_probability, impact_degree)

        return {
            'likelihood_probability': likelihood_probability,
            'impact_degree': impact_degree,
            'risk_index': ri,
            'weight_config_snapshot': self.weight_config.to_dict()
        }
    
    def calculate_batch_assessments(self, assessment_data_list: List[RiskAssessmentData]) -> List[Dict]:
        """
        批量计算多个评估数据的风险指数
        
        Args:
            assessment_data_list: 风险评估数据列表
            
        Returns:
            计算结果列表
        """
        results = []
        for data in assessment_data_list:
            result = self.calculate_single_assessment(data)
            result['data_id'] = data.id
            results.append(result)
        
        return results
    
    @staticmethod
    def normalize_value(value: float, min_val: float, max_val: float, is_positive: bool = True) -> float:
        """
        将数值标准化到0-1范围

        Args:
            value: 原始值
            min_val: 最小值
            max_val: 最大值
            is_positive: 是否为正向指标（True=正向，False=负向）

        Returns:
            标准化后的值（0-1）
        """
        if max_val == min_val:
            return 0.5  # 如果最大值等于最小值，返回中间值

        if is_positive:
            # 正向指标：X_ij = (x_ij - min(x_j)) / (max(x_j) - min(x_j))
            normalized = (value - min_val) / (max_val - min_val)
        else:
            # 负向指标：X_ij = (max(x_j) - x_ij) / (max(x_j) - min(x_j))
            normalized = (max_val - value) / (max_val - min_val)

        return max(0.0, min(1.0, normalized))
    
    @staticmethod
    def auto_normalize_dataset(assessment_data_list: List[RiskAssessmentData]) -> List[RiskAssessmentData]:
        """
        自动标准化数据集中的所有数值
        根据新的风险评估模型，区分正向指标和负向指标进行标准化

        Args:
            assessment_data_list: 原始评估数据列表

        Returns:
            标准化后的评估数据列表
        """
        if not assessment_data_list:
            return []
        
        # 定义指标及其类型（正向/负向）
        indicator_configs = {
            # 发生概率相关指标
            'annual_precipitation': {'type': 'positive'},  # 正向指标：降水量越多，风险越高
            'drainage_network_density': {'type': 'negative'},  # 负向指标：排水密度越高，风险越低
            'elevation': {'type': 'negative'},  # 负向指标：高程越高，风险越低

            # 影响程度相关指标
            'population_density': {'type': 'positive'},  # 正向指标：人口密度越高，影响越大
            'gdp_per_area': {'type': 'positive'}  # 正向指标：GDP密度越高，影响越大
        }

        # 提取各指标的值
        indicators = {}
        for indicator in indicator_configs.keys():
            indicators[indicator] = [getattr(d, indicator) for d in assessment_data_list
                                   if getattr(d, indicator, None) is not None]
        
        # 计算各指标的最大最小值
        ranges = {}
        for indicator, values in indicators.items():
            if values:
                ranges[indicator] = {'min': min(values), 'max': max(values)}
        
        # 标准化数据
        normalized_data = []
        for data in assessment_data_list:
            # 创建数据副本
            normalized = RiskAssessmentData()
            for attr in ['id', 'project_id', 'data_name', 'location_name', 'coordinates', 
                        'data_source', 'import_batch_id', 'created_at', 'updated_at']:
                if hasattr(data, attr):
                    setattr(normalized, attr, getattr(data, attr))
            
            # 标准化各指标
            for indicator in indicators.keys():
                original_value = getattr(data, indicator)
                if original_value is not None and indicator in ranges:
                    is_positive = indicator_configs[indicator]['type'] == 'positive'
                    normalized_value = RiskCalculator.normalize_value(
                        original_value,
                        ranges[indicator]['min'],
                        ranges[indicator]['max'],
                        is_positive
                    )
                    setattr(normalized, indicator, normalized_value)
                else:
                    setattr(normalized, indicator, original_value)
            
            normalized_data.append(normalized)
        
        return normalized_data
