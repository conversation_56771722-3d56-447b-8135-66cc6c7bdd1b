{% extends "base.html" %}

{% block title %}风险等级划分 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-layer-group text-warning me-2"></i>风险等级划分</h2>
                    <p class="text-muted mb-0">基于多指标权重的省份内涝风险评估与等级划分系统</p>
                </div>
                <div>
                    <a href="{{ url_for('risk_assessment.create_project') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>新建省份评估
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能卡片 -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card h-100 border-primary">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-city fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">省份管理</h5>
                    <p class="card-text">预测省份内涝风险，管理省份风险评估数据和结果</p>
                    <a href="{{ url_for('risk_assessment.projects') }}" class="btn btn-primary">
                        <i class="fas fa-map-marked-alt me-1"></i>省份风险管理
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100 border-success">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-calculator fa-3x text-success"></i>
                    </div>
                    <h5 class="card-title">风险计算</h5>
                    <p class="card-text">基于多指标权重模型计算综合风险指数和等级</p>
                    <button class="btn btn-success" onclick="showRiskCalculationInfo()">
                        <i class="fas fa-info-circle me-1"></i>计算说明
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card h-100 border-warning">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-table fa-3x text-warning"></i>
                    </div>
                    <h5 class="card-title">风险矩阵</h5>
                    <p class="card-text">5×5风险矩阵可视化展示和风险等级判定</p>
                    <button class="btn btn-warning" onclick="showRiskMatrix()">
                        <i class="fas fa-eye me-1"></i>查看矩阵
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统说明 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>系统说明</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-weight-hanging text-primary me-2"></i>新版评估指标权重</h6>
                            <ul class="list-unstyled">
                                <li><strong>发生概率计算 (P)</strong></li>
                                <li class="ms-3">• 全年降水量 (63.3%)</li>
                                <li class="ms-3">• 排水管网密度 (26.0%)</li>
                                <li class="ms-3">• 平均高程值 (10.7%)</li>
                                <li><strong>影响程度计算 (I)</strong></li>
                                <li class="ms-3">
                                    <div class="alert alert-info p-2 mt-2">
                                        <h6><i class="fas fa-info-circle me-2"></i>影响程度权重 (I)</h6>
                                        <p class="mb-2">影响程度计算使用固定权重：</p>
                                        <ul class="mb-2">
                                            <li><strong>经济优先型 (ω₄ = 0.6, ω₅ = 0.4)：</strong><br>
                                                适用于经济发达地区（如产业园区），强调内涝对经济系统的冲击，适合经济损失敏感区域。</li>
                                            <li><strong>均衡型 (ω₄ = 0.5, ω₅ = 0.5)：</strong><br>
                                                适用于经济与人口并重的区域，平衡考虑经济损失，适合一般城市内涝管理。</li>
                                            <li><strong>人口优先型 (ω₄ = 0.4, ω₅ = 0.6)：</strong><br>
                                                适用于人口密集但经济相对落后的地区，强调对人员安全的保护。</li>
                                        </ul>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-formula text-success me-2"></i>新版计算公式</h6>
                            <div class="bg-light p-3 rounded">
                                <p class="mb-2"><strong>发生概率:</strong><br>
                                P = 0.633 × X_降水量 + 0.260 × X_排水密度 + 0.107 × X_高程</p>

                                <p class="mb-2"><strong>影响程度:</strong><br>
                                I = ω₄ × X_GDP密度 + ω₅ × X_人口密度</p>

                                <p class="mb-2"><strong>权重配置:</strong><br>
                                • 经济优先型: ω₄ = 0.6, ω₅ = 0.4<br>
                                • 均衡型: ω₄ = 0.5, ω₅ = 0.5<br>
                                • 人口优先型: ω₄ = 0.4, ω₅ = 0.6</p>

                                <p class="mb-2"><strong>数据标准化:</strong><br>
                                正向指标: X = (x - min) / (max - min)<br>
                                负向指标: X = (max - x) / (max - min)</p>

                                <p class="mb-0"><strong>综合风险指数:</strong><br>
                                RI = P × I</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 风险计算说明模态框 -->
<div class="modal fade" id="riskCalculationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-calculator me-2"></i>风险计算说明</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <h6>计算步骤：</h6>
                        <ol>
                            <li><strong>数据标准化</strong>：区分正向/负向指标，标准化到0-1范围</li>
                            <li><strong>发生概率计算</strong>：基于降水量、排水密度、高程计算概率P</li>
                            <li><strong>影响程度计算</strong>：基于GDP密度、人口密度计算影响I</li>
                            <li><strong>等级判定</strong>：基于5×5风险矩阵确定最终风险等级</li>
                        </ol>
                        
                        <h6 class="mt-4">风险等级：</h6>
                        <div class="d-flex justify-content-center gap-2 flex-wrap">
                            <span class="badge bg-light text-dark fs-6">极低风险</span>
                            <span class="badge bg-success fs-6">低风险</span>
                            <span class="badge bg-warning fs-6">中风险</span>
                            <span class="badge bg-danger fs-6">高风险</span>
                            <span class="badge bg-danger fs-6">极高风险</span>
                        </div>

                        <h6 class="mt-4">概率等级划分：</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>概率等级</th>
                                        <th>描述</th>
                                        <th>数值范围</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>极低（A）</strong></td>
                                        <td>极不可能发生</td>
                                        <td>[0.00, 0.15)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>低（B）</strong></td>
                                        <td>不太可能发生</td>
                                        <td>[0.15, 0.35)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>中等（C）</strong></td>
                                        <td>可能发生</td>
                                        <td>[0.35, 0.60)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>高（D）</strong></td>
                                        <td>很可能发生</td>
                                        <td>[0.60, 0.85)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>极高（E）</strong></td>
                                        <td>极可能发生</td>
                                        <td>[0.85, 1.00]</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <h6 class="mt-4">影响等级划分：</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>后果等级</th>
                                        <th>描述</th>
                                        <th>数值范围</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>轻微</strong></td>
                                        <td>影响轻微</td>
                                        <td>[0.00, 0.10)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>较小</strong></td>
                                        <td>影响较小</td>
                                        <td>[0.10, 0.30)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>中等</strong></td>
                                        <td>影响中等</td>
                                        <td>[0.30, 0.50)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>严重</strong></td>
                                        <td>影响严重</td>
                                        <td>[0.50, 0.75)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>灾难性</strong></td>
                                        <td>影响灾难性</td>
                                        <td>[0.75, 1.00]</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 风险矩阵模态框 -->
<div class="modal fade" id="riskMatrixModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-table me-2"></i>5×5风险矩阵</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="riskMatrixContainer">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载风险矩阵...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showRiskCalculationInfo() {
    const modal = new bootstrap.Modal(document.getElementById('riskCalculationModal'));
    modal.show();
}

function showRiskMatrix() {
    const modal = new bootstrap.Modal(document.getElementById('riskMatrixModal'));
    modal.show();
    
    // 加载风险矩阵数据
    loadRiskMatrix();
}

function loadRiskMatrix() {
    fetch('/risk-assessment/api/risk-matrix')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderRiskMatrix(data);
            } else {
                document.getElementById('riskMatrixContainer').innerHTML = 
                    '<div class="alert alert-danger">加载风险矩阵失败: ' + data.error + '</div>';
            }
        })
        .catch(error => {
            console.error('Error loading risk matrix:', error);
            document.getElementById('riskMatrixContainer').innerHTML = 
                '<div class="alert alert-danger">加载风险矩阵时发生错误</div>';
        });
}

function renderRiskMatrix(data) {
    let html = '<table class="table table-bordered risk-matrix-table">';
    html += '<thead><tr>';
    html += '<th class="text-center bg-light">发生概率 \\ 影响程度</th>';
    
    // 影响程度表头
    data.impact_headers.forEach(header => {
        html += `<th class="text-center bg-light">${header.level}</th>`;
    });
    html += '</tr></thead><tbody>';
    
    // 矩阵内容
    data.matrix_data.forEach(row => {
        html += '<tr>';
        html += `<td class="text-center bg-light"><strong>${row.likelihood_name}（${row.likelihood_code}）</strong></td>`;
        
        row.cells.forEach(cell => {
            const style = cell.style;
            html += `<td class="text-center" style="background-color: ${style.background}; color: ${style.color}; border-color: ${style.border};">
                        <strong>${cell.risk_level}</strong>
                     </td>`;
        });
        html += '</tr>';
    });
    
    html += '</tbody></table>';
    
    // 添加说明
    html += '<div class="mt-4">';
    html += '<h6>5×5风险矩阵说明：</h6>';
    html += '<div class="row">';
    html += '<div class="col-md-6">';
    html += '<h6 class="text-primary">概率等级（A-E）：</h6>';
    html += '<ul class="small">';
    html += '<li><strong>A（极低）</strong>：[0.00, 0.15) - 极不可能发生</li>';
    html += '<li><strong>B（低）</strong>：[0.15, 0.35) - 不太可能发生</li>';
    html += '<li><strong>C（中等）</strong>：[0.35, 0.60) - 可能发生</li>';
    html += '<li><strong>D（高）</strong>：[0.60, 0.85) - 很可能发生</li>';
    html += '<li><strong>E（极高）</strong>：[0.85, 1.00] - 极可能发生</li>';
    html += '</ul>';
    html += '</div>';
    html += '<div class="col-md-6">';
    html += '<h6 class="text-success">影响等级：</h6>';
    html += '<ul class="small">';
    html += '<li><strong>轻微</strong>：[0.00, 0.10) - 影响轻微</li>';
    html += '<li><strong>较小</strong>：[0.10, 0.30) - 影响较小</li>';
    html += '<li><strong>中等</strong>：[0.30, 0.50) - 影响中等</li>';
    html += '<li><strong>严重</strong>：[0.50, 0.75) - 影响严重</li>';
    html += '<li><strong>灾难性</strong>：[0.75, 1.00] - 影响灾难性</li>';
    html += '</ul>';
    html += '</div>';
    html += '</div>';
    html += '<div class="mt-3">';
    html += '<h6 class="text-warning">风险等级颜色说明：</h6>';
    html += '<div class="d-flex justify-content-center gap-3 flex-wrap">';
    html += '<span class="badge bg-light text-dark">极低风险</span>';
    html += '<span class="badge bg-success">低风险</span>';
    html += '<span class="badge bg-warning">中风险</span>';
    html += '<span class="badge bg-danger">高风险</span>';
    html += '<span class="badge bg-danger">极高风险</span>';
    html += '</div>';
    html += '</div>';
    html += '</div>';
    
    document.getElementById('riskMatrixContainer').innerHTML = html;
}
</script>

<style>
.risk-matrix-table {
    font-size: 0.9rem;
}

.risk-matrix-table th,
.risk-matrix-table td {
    vertical-align: middle;
    padding: 0.75rem 0.5rem;
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
{% endblock %}
