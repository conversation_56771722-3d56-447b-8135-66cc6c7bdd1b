#!/usr/bin/env python3
"""
数据库迁移脚本：添加影响程度权重配置字段
为 RiskWeightConfig 模型添加 gdp_weight, population_weight, impact_weight_type 字段
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models import RiskWeightConfig
from sqlalchemy import text

def migrate_impact_weights():
    """迁移影响程度权重配置"""
    app = create_app()
    
    with app.app_context():
        try:
            print("开始迁移影响程度权重配置...")
            
            # 检查字段是否已存在
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('risk_weight_configs')]
            
            # 添加新字段（如果不存在）
            if 'gdp_weight' not in columns:
                print("添加 gdp_weight 字段...")
                with db.engine.connect() as conn:
                    conn.execute(text('ALTER TABLE risk_weight_configs ADD COLUMN gdp_weight FLOAT NOT NULL DEFAULT 0.6'))
                    conn.commit()

            if 'population_weight' not in columns:
                print("添加 population_weight 字段...")
                with db.engine.connect() as conn:
                    conn.execute(text('ALTER TABLE risk_weight_configs ADD COLUMN population_weight FLOAT NOT NULL DEFAULT 0.4'))
                    conn.commit()

            if 'impact_weight_type' not in columns:
                print("添加 impact_weight_type 字段...")
                with db.engine.connect() as conn:
                    conn.execute(text('ALTER TABLE risk_weight_configs ADD COLUMN impact_weight_type VARCHAR(20) NOT NULL DEFAULT "economic"'))
                    conn.commit()
            
            # 更新现有记录
            print("更新现有权重配置记录...")
            configs = RiskWeightConfig.query.all()
            
            for config in configs:
                if not hasattr(config, 'gdp_weight') or config.gdp_weight is None:
                    config.gdp_weight = 0.6
                if not hasattr(config, 'population_weight') or config.population_weight is None:
                    config.population_weight = 0.4
                if not hasattr(config, 'impact_weight_type') or config.impact_weight_type is None:
                    config.impact_weight_type = 'economic'
                
                # 更新描述以反映权重类型
                if config.is_default and '经济优先型' not in config.description:
                    config.description = config.description + '（经济优先型）'
            
            db.session.commit()
            print("✓ 影响程度权重配置迁移完成")
            
            # 验证迁移结果
            print("\n验证迁移结果:")
            for config in RiskWeightConfig.query.all():
                print(f"  配置: {config.config_name}")
                print(f"    GDP权重: {config.gdp_weight}")
                print(f"    人口权重: {config.population_weight}")
                print(f"    权重类型: {config.impact_weight_type}")
                print(f"    权重类型名称: {config.get_impact_weight_type_name()}")
                print()
            
        except Exception as e:
            print(f"❌ 迁移失败: {str(e)}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    migrate_impact_weights()
