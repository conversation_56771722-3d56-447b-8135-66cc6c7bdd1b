{% extends "base.html" %}

{% block title %}地图展示 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-map me-2"></i>省份内涝风险地图
                    </h2>
                    <p class="text-muted mb-0">各省份内城市内涝灾害风险可视化展示</p>
                </div>
                <div>
                    <span class="badge bg-info">实时数据</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 控制面板 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <!-- 城市选择 -->
                        <div class="col-md-3">
                            <label for="citySelect" class="form-label mb-1">选择城市</label>
                            <select class="form-select form-select-sm" id="citySelect">
                                <option value="">请选择城市...</option>
                            </select>
                        </div>
                        


                        <!-- DEM图层控制 -->
                        <div class="col-md-2">
                            <label class="form-label mb-1">地形数据</label>
                            <div class="d-flex flex-column gap-1">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="demLayerToggle">
                                    <label class="form-check-label" for="demLayerToggle">
                                        <small>DEM图层</small>
                                    </label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="elevationQuery" checked>
                                    <label class="form-check-label" for="elevationQuery">
                                        <small>高程查询</small>
                                    </label>
                                </div>


                            </div>
                        </div>

                        <!-- 地图控制 -->
                        <div class="col-md-2">
                            <label class="form-label mb-1">地图控制</label>
                            <div class="d-flex gap-1">
                                <button class="btn btn-sm btn-outline-primary" id="fullscreenBtn">
                                    <i class="fas fa-expand"></i> 全屏
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" id="refreshBtn">
                                    <i class="fas fa-sync"></i> 刷新
                                </button>
                            </div>
                        </div>
                        

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 地图容器 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body p-0">
                    <div id="mapContainer" style="height: 600px; position: relative;">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 高程信息显示区域 -->
    <div class="row mt-3" id="elevationInfoPanel" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-mountain me-2"></i>高程信息
                        <button type="button" class="btn-close float-end" id="closeElevationPanel"></button>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="mb-1" id="currentElevation">--</h5>
                                <small class="text-muted">当前高程 (米)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="mb-1" id="currentCoords">--</h6>
                                <small class="text-muted">坐标位置</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="border-end">
                                        <small class="text-muted d-block">最低海拔</small>
                                        <span id="minElevation">55m</span>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border-end">
                                        <small class="text-muted d-block">平均海拔</small>
                                        <span id="avgElevation">237m</span>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div>
                                        <small class="text-muted d-block">最高海拔</small>
                                        <span id="maxElevation">1471m</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/map-controls.css') }}" />
<style>
#mapContainer {
    border-radius: 0.375rem;
    overflow: hidden;
    background-color: #ffffff !important;
}

/* 确保Leaflet地图容器背景为白色 */
.leaflet-container {
    background-color: #ffffff !important;
}

/* 确保瓦片容器背景为白色 */
.leaflet-tile-pane {
    background-color: #ffffff !important;
}

.leaflet-popup-content-wrapper {
    border-radius: 8px;
}

.leaflet-popup-content {
    margin: 15px;
    line-height: 1.4;
}

.risk-marker {
    border: 2px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.risk-marker.high {
    background-color: #dc3545;
}

.risk-marker.medium {
    background-color: #ffc107;
}

.risk-marker.low {
    background-color: #28a745;
}

.risk-info {
    max-width: 300px;
}

.risk-factors {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
}

.risk-factor {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 11px;
    color: #6c757d;
}

.fullscreen-map {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: white;
}

.fullscreen-map .card-body {
    padding: 0 !important;
}

.fullscreen-map #mapContainer {
    height: 100vh !important;
    border-radius: 0 !important;
}

/* 全屏模式下的退出按钮 */
.fullscreen-exit-btn {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    z-index: 10000 !important;
    background: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid #ddd !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2) !important;
}

.fullscreen-exit-btn:hover {
    background: white !important;
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/map-controls.js') }}"></script>
<script>
// 地图相关变量
let map = null;
let currentCity = null;
let riskMarkers = [];
let markerLayer = null;
let isMapInitialized = false;
let currentTileLayer = null;
let whiteBaseLayer = null;

// DEM相关变量
let demLayer = null;
let elevationQueryEnabled = true;
let demData = null;
let loadingTiles = 0;
let totalTiles = 0;

// 风险等级配置
const riskConfig = {
    very_high: { color: '#dc3545', label: '极高风险' },
    high: { color: '#fd7e14', label: '高风险' },
    medium: { color: '#ffc107', label: '中风险' },
    low: { color: '#28a745', label: '低风险' },
    very_low: { color: '#d4edda', label: '极低风险' }
};

// 城市配置 - 从API动态获取
let cityConfig = {};

// 获取城市中文名称
function getCityName(cityCode) {
    return cityConfig[cityCode] ? cityConfig[cityCode].name : cityCode;
}

// 切换到指定城市
function switchToCity(cityId) {
    try {
        console.log('切换到城市:', cityId);

        if (!cityConfig[cityId]) {
            console.error('未知的城市ID:', cityId);
            showAlert(`未知的城市: ${cityId}`, 'danger');
            return;
        }

        const city = cityConfig[cityId];
        currentCity = cityId;

        console.log('城市配置:', city);

        // 设置地图视图
        if (map) {
            map.setView(city.center, city.zoom);
            console.log('地图视图已设置:', city.center, city.zoom);
        }

        // 如果DEM图层已开启，需要重新加载对应城市的DEM数据
        if (demLayer && document.getElementById('demLayerToggle').checked) {
            console.log('重新加载DEM图层...');
            // 移除当前DEM图层
            map.removeLayer(demLayer);
            demLayer = null;

            // 重新启用DEM图层
            setTimeout(() => {
                toggleDemLayer(true);
            }, 100);
        }

        showAlert(`已切换到${city.name}`, 'success');

    } catch (error) {
        console.error('切换城市时发生错误:', error);
        showAlert('切换城市失败', 'danger');
    }
}

// 地图缓存管理
const mapCache = {
    cities: null,
    floodRisks: {},
    isLoading: {}
};

// 高德地图瓦片源配置
const gaodeMapUrl = 'https://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}';
const gaodeMapAttribution = '© 高德地图';

// 强制设置地图背景为纯白色的函数
function setWhiteBackground() {
    if (!map) return;

    const mapContainer = map.getContainer();
    mapContainer.style.backgroundColor = '#ffffff';

    // 设置所有相关元素的背景色
    const leafletContainer = mapContainer.querySelector('.leaflet-container');
    if (leafletContainer) {
        leafletContainer.style.backgroundColor = '#ffffff';
    }

    const tilePane = mapContainer.querySelector('.leaflet-tile-pane');
    if (tilePane) {
        tilePane.style.backgroundColor = '#ffffff';
    }

    console.log('地图背景已设置为纯白色');
}

$(document).ready(function() {
    initializeMap();
    preloadData();
    bindEvents();
});

// 初始化地图
function initializeMap() {
    if (isMapInitialized) {
        return;
    }

    // 创建地图实例
    map = L.map('mapContainer', {
        center: [35.0, 110.0], // 中国中心位置
        zoom: 5,
        zoomControl: true,
        attributionControl: true
    });

    // 创建纯白色底图（用于DEM模式）
    const whitePixelBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP///8/AAX+Av7czFnnAAAAAElFTkSuQmCC';
    whiteBaseLayer = L.tileLayer(whitePixelBase64, {
        attribution: 'DEM地形图',
        opacity: 1.0,
        zIndex: 0
    });

    // 添加高德地图底图图层
    const tileLayer = L.tileLayer(gaodeMapUrl, {
        attribution: gaodeMapAttribution,
        maxZoom: 18
    });

    // 监听瓦片加载成功
    tileLayer.on('tileload', function() {
        console.log('高德地图瓦片加载成功');
    });

    // 监听瓦片加载错误
    tileLayer.on('tileerror', function(error) {
        console.warn('高德地图瓦片加载失败:', error);
        showAlert('地图加载失败，请检查网络连接', 'warning');
    });

    tileLayer.addTo(map);
    currentTileLayer = tileLayer;

    // 创建标记图层组
    markerLayer = L.layerGroup().addTo(map);

    // 地图容器准备完成
    map.whenReady(function() {
        isMapInitialized = true;
        console.log('地图初始化完成');

        // 添加指北针和比例尺控件
        if (typeof initMapControls === 'function') {
            initMapControls(map, {
                compass: true,
                scale: true,
                compassPosition: 'topright',
                scalePosition: 'bottomleft'
            });
        }

        // 初始设置白色背景
        setTimeout(() => {
            setWhiteBackground();
        }, 200);

        // 添加地图点击事件用于高程查询
        map.on('click', function(e) {
            if (elevationQueryEnabled) {
                queryElevationAtPoint(e.latlng.lat, e.latlng.lng);
            }
        });

        // 监听地图移动和缩放事件，确保背景始终为白色（仅在DEM模式下）
        map.on('moveend zoomend', function() {
            if ($('#demLayerToggle').is(':checked')) {
                setTimeout(() => {
                    setWhiteBackground();
                }, 50);
            }
        });
    });
}

// 预加载数据
function preloadData() {
    // 预加载城市列表
    loadCities();

    // 预加载常用城市的风险数据
    const popularCities = ['xian', 'zhengzhou', 'xiamen'];
    popularCities.forEach(cityId => {
        preloadCityRisks(cityId);
    });
}

// 预加载城市风险数据
function preloadCityRisks(cityId) {
    if (mapCache.floodRisks[cityId] || mapCache.isLoading[cityId]) {
        return;
    }

    mapCache.isLoading[cityId] = true;
    GISFloodSystem.api.get(`/flood-risks/${cityId}`)
        .then(response => {
            if (response.success) {
                mapCache.floodRisks[cityId] = response.data;
            }
        })
        .catch(error => {
            console.warn(`预加载城市 ${cityId} 数据失败:`, error);
        })
        .finally(() => {
            mapCache.isLoading[cityId] = false;
        });
}

// 加载城市列表
function loadCities() {
    // 从API获取城市配置数据
    fetch('/api/cities')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                cityConfig = data.cities;
                const cities = Object.keys(cityConfig).map(code => ({
                    id: code,
                    name: cityConfig[code].name
                }));
                mapCache.cities = cities;
                populateCitySelect(cities);
            } else {
                console.error('获取城市列表失败:', data.error);
                showAlert('获取城市列表失败', 'danger');
            }
        })
        .catch(error => {
            console.error('加载城市列表失败:', error);
            showAlert('加载城市列表失败', 'danger');
        });
}

// 填充城市选择框
function populateCitySelect(cities) {
    const citySelect = $('#citySelect');
    citySelect.empty().append('<option value="">请选择城市...</option>');

    cities.forEach(city => {
        citySelect.append(`<option value="${city.id}">${city.name}</option>`);
    });
}

// 绑定事件
function bindEvents() {
    // 城市选择变化
    $('#citySelect').on('change', function() {
        const cityId = $(this).val();
        if (cityId) {
            // 切换到选中的城市
            switchToCity(cityId);
            loadCityData(cityId);
        } else {
            clearMap();
        }
    });



    // DEM图层切换
    $('#demLayerToggle').on('change', function() {
        toggleDemLayer($(this).is(':checked'));
    });

    // 高程查询开关
    $('#elevationQuery').on('change', function() {
        elevationQueryEnabled = $(this).is(':checked');
        if (!elevationQueryEnabled) {
            $('#elevationInfoPanel').hide();
        }
    });

    // 关闭高程信息面板
    $('#closeElevationPanel').on('click', function() {
        $('#elevationInfoPanel').hide();
    });





    // 全屏按钮
    $('#fullscreenBtn').on('click', function() {
        toggleFullscreen();
    });

    // 刷新按钮
    $('#refreshBtn').on('click', function() {
        const $btn = $(this);
        const originalHtml = $btn.html();

        // 显示刷新状态
        $btn.html('<i class="fas fa-spinner fa-spin"></i> 刷新中...').prop('disabled', true);

        const cityId = $('#citySelect').val();
        if (cityId) {
            // 清除该城市的缓存，强制重新加载
            delete mapCache.floodRisks[cityId];
            mapCache.isLoading[cityId] = false;

            // 重新加载城市数据
            loadCityData(cityId);

            // 恢复按钮状态
            setTimeout(() => {
                $btn.html(originalHtml).prop('disabled', false);
                showAlert('数据已刷新', 'success');
            }, 1000);
        } else {
            // 如果没有选择城市，刷新城市列表
            mapCache.cities = null;
            loadCities();

            // 恢复按钮状态
            setTimeout(() => {
                $btn.html(originalHtml).prop('disabled', false);
                showAlert('城市列表已刷新', 'success');
            }, 500);
        }
    });


}

// 设置城市地图视图
function setCityMapView(city) {
    if (city.bounds && city.bounds.length === 2) {
        // 如果有边界信息，使用fitBounds来自动调整视图
        const bounds = L.latLngBounds(city.bounds[0], city.bounds[1]);
        map.fitBounds(bounds, {
            padding: [20, 20],  // 添加边距
            maxZoom: city.zoom || 12  // 限制最大缩放级别
        });
    } else {
        // 如果没有边界信息，使用传统的中心点和缩放级别
        map.setView(city.center, city.zoom || 11);
    }
}

// 加载城市数据
function loadCityData(cityId) {
    // 检查是否有缓存的城市信息
    const city = mapCache.cities ? mapCache.cities.find(c => c.id === cityId) : null;
    if (!city) {
        showAlert('城市信息未找到', 'warning');
        return;
    }

    currentCity = city;

    // 所有城市默认使用真实数据模式
    const cacheKey = cityId + '_real';

    // 检查是否有缓存的风险数据
    if (mapCache.floodRisks[cacheKey]) {
        // 使用缓存数据，快速显示
        setCityMapView(city);
        displayRiskMarkers(mapCache.floodRisks[cacheKey]);
        return;
    }

    // 如果正在加载中，等待加载完成但不显示地图加载动画
    if (mapCache.isLoading[cacheKey]) {
        // 等待加载完成
        const checkLoading = setInterval(() => {
            if (!mapCache.isLoading[cacheKey]) {
                clearInterval(checkLoading);
                if (mapCache.floodRisks[cacheKey]) {
                    setCityMapView(city);
                    displayRiskMarkers(mapCache.floodRisks[cacheKey]);
                }
            }
        }, 100);
        return;
    }

    // 需要加载新数据，但不显示地图加载动画
    mapCache.isLoading[cacheKey] = true;

    // 设置地图视图到城市范围
    setCityMapView(city);

    // 构建API URL（默认使用真实数据）
    let apiUrl = `/flood-risks/${cityId}?real_data=true`;

    // 加载内涝风险数据
    GISFloodSystem.api.get(apiUrl)
        .then(response => {
            if (response && response.success) {
                mapCache.floodRisks[cacheKey] = response.data;
                displayRiskMarkers(response.data);

                // 显示数据模式信息
                if (response.data_mode === 'real') {
                    showAlert('已切换到基于DEM的真实数据模式', 'success');
                }
            }
        })
        .catch(error => {
            console.error('加载城市数据失败:', error);
            showAlert('加载城市数据失败', 'danger');
        })
        .finally(() => {
            mapCache.isLoading[cacheKey] = false;
        });
}

// 显示风险标记
function displayRiskMarkers(risks) {
    // 清除现有标记
    markerLayer.clearLayers();
    riskMarkers = [];

    risks.forEach(risk => {
        const marker = createRiskMarker(risk);
        riskMarkers.push({
            marker: marker,
            data: risk
        });
        markerLayer.addLayer(marker);
    });
}

// 创建风险标记
function createRiskMarker(risk) {
    const config = riskConfig[risk.risk_level];

    // 创建自定义图标
    const icon = L.divIcon({
        className: 'risk-marker',
        html: `<div style="background-color: ${config.color}; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
        iconSize: [24, 24],
        iconAnchor: [12, 12]
    });

    // 创建标记
    const marker = L.marker([risk.lat, risk.lng], { icon: icon });

    // 创建弹窗内容
    const popupContent = createPopupContent(risk);
    marker.bindPopup(popupContent, {
        maxWidth: 350,
        className: 'risk-popup'
    });

    // 添加悬停效果
    marker.on('mouseover', function() {
        this.openPopup();
    });

    return marker;
}

// 创建弹窗内容
function createPopupContent(risk) {
    const config = riskConfig[risk.risk_level];
    const factorTags = risk.risk_factors.map(factor =>
        `<span class="risk-factor">${factor}</span>`
    ).join('');

    return `
        <div class="risk-info">
            <div class="d-flex align-items-center mb-2">
                <div class="me-2" style="width: 16px; height: 16px; background-color: ${config.color}; border-radius: 50%;"></div>
                <h6 class="mb-0">${risk.title}</h6>
                <span class="badge bg-${risk.risk_level === 'very_high' ? 'danger' : risk.risk_level === 'high' ? 'danger' : risk.risk_level === 'medium' ? 'warning' : risk.risk_level === 'low' ? 'success' : 'light'} ms-2">${config.label}</span>
            </div>
            <p class="mb-2 text-muted small">${risk.description}</p>
            <div class="row g-2 mb-2">
                <div class="col-6">
                    <small class="text-muted">影响范围:</small>
                    <div class="fw-bold">${risk.affected_area}</div>
                </div>
                <div class="col-6">
                    <small class="text-muted">最近内涝:</small>
                    <div class="fw-bold">${risk.last_flood}</div>
                </div>
            </div>
            <div class="mb-0">
                <small class="text-muted">风险因素:</small>
                <div class="risk-factors">${factorTags}</div>
            </div>
        </div>
    `;
}







// 清除地图
function clearMap() {
    markerLayer.clearLayers();
    riskMarkers = [];
    currentCity = null;

    // 重置地图视图
    map.setView([35.0, 110.0], 5);
}

// 切换全屏
function toggleFullscreen() {
    const mapCard = $('#mapContainer').closest('.card');
    const btn = $('#fullscreenBtn');

    if (mapCard.hasClass('fullscreen-map')) {
        // 退出全屏
        exitFullscreen();
    } else {
        // 进入全屏
        enterFullscreen();
    }
}

// 进入全屏模式
function enterFullscreen() {
    const mapCard = $('#mapContainer').closest('.card');
    const btn = $('#fullscreenBtn');

    // 添加全屏样式
    mapCard.addClass('fullscreen-map');
    btn.html('<i class="fas fa-compress"></i> 退出');

    // 创建全屏模式下的退出按钮
    const exitBtn = $(`
        <button id="fullscreenExitBtn" class="btn btn-outline-secondary fullscreen-exit-btn" title="退出全屏">
            <i class="fas fa-times"></i> 退出全屏
        </button>
    `);

    // 添加退出按钮到页面
    $('body').append(exitBtn);

    // 绑定退出按钮事件
    $('#fullscreenExitBtn').on('click', exitFullscreen);

    // 绑定ESC键退出全屏
    $(document).on('keydown.fullscreen', function(e) {
        if (e.key === 'Escape') {
            exitFullscreen();
        }
    });

    // 重新调整地图大小
    setTimeout(() => {
        map.invalidateSize();
    }, 100);

    console.log('进入全屏模式');
}

// 退出全屏模式
function exitFullscreen() {
    const mapCard = $('#mapContainer').closest('.card');
    const btn = $('#fullscreenBtn');

    // 移除全屏样式
    mapCard.removeClass('fullscreen-map');
    btn.html('<i class="fas fa-expand"></i> 全屏');

    // 移除全屏退出按钮
    $('#fullscreenExitBtn').remove();

    // 解绑ESC键事件
    $(document).off('keydown.fullscreen');

    // 重新调整地图大小
    setTimeout(() => {
        map.invalidateSize();
    }, 100);

    console.log('退出全屏模式');
}





// DEM相关功能函数

// 切换DEM图层显示
function toggleDemLayer(show) {
    if (show) {
        const selectedCity = document.getElementById('citySelect').value;
        if (!selectedCity) {
            showAlert('请先选择一个城市', 'warning');
            document.getElementById('demLayerToggle').checked = false;
            return;
        }

        // 显示加载提示
        showAlert(`正在加载${getCityName(selectedCity)}DEM地形数据...`, 'info');

        // 切换到白色底图
        if (map.hasLayer(currentTileLayer)) {
            map.removeLayer(currentTileLayer);
        }
        if (!map.hasLayer(whiteBaseLayer)) {
            whiteBaseLayer.addTo(map);
        }

        // 创建DEM瓦片图层 - 支持多城市
        const demTileUrl = `/api/tiles/dem/${selectedCity}/{z}/{x}/{y}.png`;

        demLayer = L.tileLayer(demTileUrl, {
            opacity: 0.9,  // 提高透明度到90%，使DEM图层更明显
            zIndex: 1000,  // 确保在底图之上
            attribution: `${getCityName(selectedCity)}DEM数据`
        });

        // 添加到地图
        demLayer.addTo(map);

        // 强制设置白色背景
        setTimeout(() => {
            setWhiteBackground();
        }, 100);

        // 监听图层加载事件
        let loadingTiles = 0;
        let totalTiles = 0;

        demLayer.on('loading', function() {
            console.log('DEM图层开始加载');
            loadingTiles = 0;
            totalTiles = 0;
        });

        demLayer.on('tileloadstart', function() {
            totalTiles++;
            loadingTiles++;
            updateDemLoadingProgress();
        });

        demLayer.on('tileload', function() {
            loadingTiles--;
            updateDemLoadingProgress();

            if (loadingTiles === 0) {
                setTimeout(() => {
                    showAlert('DEM图层加载完成', 'success');
                }, 500);
            }
        });

        demLayer.on('tileerror', function(e) {
            loadingTiles--;
            console.warn('DEM瓦片加载失败:', e.tile.src);
            updateDemLoadingProgress();
        });

        // 加载DEM基本信息
        loadDemInfo();

        // 添加超时检查
        setTimeout(() => {
            if (loadingTiles > 0) {
                console.warn('DEM图层加载超时');
                showAlert('DEM图层加载较慢，请耐心等待...', 'warning');
            }
        }, 10000); // 10秒超时提醒

    } else {
        // 移除DEM图层
        if (demLayer) {
            map.removeLayer(demLayer);
            demLayer = null;
        }

        // 切换回普通地图底图
        if (map.hasLayer(whiteBaseLayer)) {
            map.removeLayer(whiteBaseLayer);
        }
        if (!map.hasLayer(currentTileLayer)) {
            currentTileLayer.addTo(map);
        }

        showAlert('已切换回普通地图模式', 'info');
    }
}

// 加载DEM基本信息
function loadDemInfo() {
    if (demData) {
        return; // 已经加载过了
    }

    GISFloodSystem.api.get('/dem/zhengzhou')
        .then(response => {
            if (response.success) {
                demData = response.data;
                updateElevationStats(demData);
                console.log('DEM数据加载成功:', demData);
            }
        })
        .catch(error => {
            console.error('加载DEM数据失败:', error);
        });
}

// 更新高程统计信息显示
function updateElevationStats(data) {
    if (data && data.elevation_info) {
        $('#minElevation').text(Math.round(data.elevation_info.min_elevation) + 'm');
        $('#avgElevation').text(Math.round(data.elevation_info.mean_elevation) + 'm');
        $('#maxElevation').text(Math.round(data.elevation_info.max_elevation) + 'm');
    }
}

// 查询指定点的高程
function queryElevationAtPoint(lat, lng) {
    const selectedCity = document.getElementById('citySelect').value;
    if (!selectedCity) {
        showAlert('请先选择一个城市', 'warning');
        return;
    }

    // 检查是否在选中城市范围内
    if (!isInCityArea(lat, lng, selectedCity)) {
        showAlert(`当前位置超出${getCityName(selectedCity)}DEM数据范围`, 'warning');
        return;
    }

    // 显示加载状态
    $('#currentElevation').text('查询中...');
    $('#currentCoords').text(`${lat.toFixed(4)}, ${lng.toFixed(4)}`);
    $('#elevationInfoPanel').show();

    // 调用API查询高程
    const params = new URLSearchParams({
        lat: lat,
        lng: lng
    });

    GISFloodSystem.api.get(`/dem/${selectedCity}/elevation?${params}`)
        .then(response => {
            if (response.success) {
                const elevation = response.data.elevation;
                $('#currentElevation').text(elevation + 'm');

                // 根据高程设置颜色
                const elevationElement = $('#currentElevation');
                if (elevation < 100) {
                    elevationElement.removeClass().addClass('text-primary');
                } else if (elevation < 300) {
                    elevationElement.removeClass().addClass('text-warning');
                } else {
                    elevationElement.removeClass().addClass('text-danger');
                }
            } else {
                $('#currentElevation').text('查询失败');
                showAlert('高程查询失败: ' + response.error, 'danger');
            }
        })
        .catch(error => {
            $('#currentElevation').text('查询失败');
            console.error('高程查询失败:', error);
            showAlert('高程查询失败', 'danger');
        });
}

// 检查坐标是否在指定城市范围内
function isInCityArea(lat, lng, cityId) {
    if (!cityConfig[cityId]) {
        return false;
    }

    const bounds = cityConfig[cityId].bounds;
    return lat >= bounds[0][0] && lat <= bounds[1][0] &&
           lng >= bounds[0][1] && lng <= bounds[1][1];
}

// 检查坐标是否在郑州市范围内（保持向后兼容）
function isInZhengzhouArea(lat, lng) {
    return isInCityArea(lat, lng, 'zhengzhou');
}

// 更新DEM加载进度
function updateDemLoadingProgress() {
    if (totalTiles > 0) {
        const progress = Math.round(((totalTiles - loadingTiles) / totalTiles) * 100);

        if (loadingTiles > 0) {
            // 更新DEM图层开关的标签显示进度
            const demToggleLabel = $('label[for="demLayerToggle"]');
            demToggleLabel.html(`<small>DEM图层 (${progress}%)</small>`);
        } else {
            // 加载完成，恢复原始标签
            const demToggleLabel = $('label[for="demLayerToggle"]');
            demToggleLabel.html('<small>DEM图层</small>');
        }
    }
}

// 处理DEM图层错误
function handleDemLayerError(error) {
    console.error('DEM图层错误:', error);

    // 如果图层加载失败，自动关闭开关
    $('#demLayerToggle').prop('checked', false);

    // 移除图层
    if (demLayer) {
        map.removeLayer(demLayer);
        demLayer = null;
    }

    // 隐藏透明度控制
    $('#demOpacityContainer').hide();

    // 显示错误信息
    showAlert('DEM图层加载失败，请检查网络连接或稍后重试', 'danger');
}

// 显示提示信息
function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 10000; min-width: 300px;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('body').append(alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 3000);
}
</script>
{% endblock %}
