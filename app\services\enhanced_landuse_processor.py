"""
增强的土地利用数据处理服务
实现上传后自动处理、地图生成和TIFF图片展示
"""

import os
import logging
import numpy as np
from typing import Optional, Dict, List, Tuple
from flask import current_app
from app.models import GISLayer, Dataset, db
from app.services.landuse_auto_processor import LanduseAutoProcessor
from app.visualization.pyecharts_maps import PyEchartsMapGenerator

logger = logging.getLogger(__name__)

class EnhancedLanduseProcessor(LanduseAutoProcessor):
    """增强的土地利用数据处理器"""
    
    # CLCD土地利用分类颜色映射
    CLCD_COLOR_MAPPING = {
        1: {'name': '耕地', 'color': '#FFD700', 'rgb': (255, 215, 0)},
        2: {'name': '林地', 'color': '#228B22', 'rgb': (34, 139, 34)},
        3: {'name': '草地', 'color': '#90EE90', 'rgb': (144, 238, 144)},
        4: {'name': '灌木地', 'color': '#9ACD32', 'rgb': (154, 205, 50)},
        5: {'name': '湿地', 'color': '#20B2AA', 'rgb': (32, 178, 170)},
        6: {'name': '水域', 'color': '#4169E1', 'rgb': (65, 105, 225)},
        7: {'name': '苔原', 'color': '#DDA0DD', 'rgb': (221, 160, 221)},
        8: {'name': '建设用地', 'color': '#808080', 'rgb': (128, 128, 128)},
        9: {'name': '裸地', 'color': '#D2B48C', 'rgb': (210, 180, 140)},
        10: {'name': '冰雪', 'color': '#F0F8FF', 'rgb': (240, 248, 255)}
    }
    
    @classmethod
    def process_uploaded_landuse_file(cls, file_path: str, original_filename: str, user_id: int = 1, step_collector: List = None) -> Optional[Dict]:
        """
        处理上传的土地利用文件（增强版本）

        Args:
            file_path: 文件路径
            original_filename: 原始文件名
            user_id: 用户ID
            step_collector: 步骤收集器列表

        Returns:
            处理结果字典，包含图层信息、地图URL、图片URL等
        """
        if step_collector is None:
            step_collector = []

        try:
            step_msg = f"开始增强处理土地利用文件: {original_filename}"
            logger.info(step_msg)
            step_collector.append(f"INFO: {step_msg}")

            # 1. 基础处理：创建GISLayer
            step_collector.append("INFO: 开始基础处理，创建GIS图层...")
            layer = cls.process_uploaded_file(file_path, original_filename, user_id)
            if not layer:
                step_collector.append("ERROR: 基础处理失败，无法创建GIS图层")
                return None

            step_collector.append(f"INFO: 成功创建土地利用图层: {layer.name} (ID: {layer.id})")

            # 2. 标准化省份名称
            if layer.province and not layer.province.endswith('省'):
                original_province = layer.province
                if layer.province in ['北京', '天津', '上海', '重庆']:
                    layer.province = f"{layer.province}市"
                elif layer.province in ['内蒙古', '广西', '西藏', '宁夏', '新疆']:
                    layer.province = f"{layer.province}自治区"
                elif layer.province in ['香港', '澳门']:
                    layer.province = f"{layer.province}特别行政区"
                else:
                    layer.province = f"{layer.province}省"

                db.session.commit()
                step_msg = f"标准化省份名称: {original_province} -> {layer.province}"
                logger.info(step_msg)
                step_collector.append(f"INFO: {step_msg}")

            # 3. 生成PNG图片并保存路径到数据库
            step_collector.append("INFO: 开始生成TIFF图片...")
            image_url = cls.generate_tiff_image(layer, step_collector)
            if image_url:
                # 将图片URL保存到图层的metadata中
                cls._save_png_metadata(layer, image_url)
                step_msg = f"PNG图片路径已保存到数据库: {image_url}"
                logger.info(step_msg)
                step_collector.append(f"INFO: {step_msg}")

            # 4. 处理完成，返回结果
            result = {
                'layer_id': layer.id,
                'layer_name': layer.name,
                'province': layer.province,
                'file_path': layer.file_path,
                'image_url': image_url,
                'processing_status': 'completed',
                'processing_steps': step_collector.copy()
            }

            step_msg = f"增强处理完成: {layer.name}"
            logger.info(step_msg)
            step_collector.append(f"INFO: {step_msg}")
            return result

        except Exception as e:
            error_msg = f"增强处理土地利用文件失败: {e}"
            logger.error(error_msg)
            step_collector.append(f"ERROR: {error_msg}")
            return None
    
    @classmethod
    def generate_tiff_image(cls, layer: GISLayer, step_collector: List = None) -> Optional[str]:
        """
        使用GDAL将TIFF文件转换为PNG图片

        Args:
            layer: GIS图层对象
            step_collector: 步骤收集器列表

        Returns:
            图片URL，失败返回None
        """
        if step_collector is None:
            step_collector = []

        try:
            from osgeo import gdal
            from PIL import Image

            step_msg = f"使用GDAL生成TIFF图片: {layer.name}"
            logger.info(step_msg)
            step_collector.append(f"INFO: {step_msg}")

            # 确保图片目录存在
            images_dir = os.path.join(current_app.static_folder, 'images', 'landuse')
            os.makedirs(images_dir, exist_ok=True)

            # 生成图片文件名
            base_name = os.path.splitext(os.path.basename(layer.file_path))[0]
            image_filename = f"{base_name}_{layer.id}.png"
            image_path = os.path.join(images_dir, image_filename)

            # 使用GDAL打开TIFF文件
            step_collector.append("INFO: 正在使用GDAL打开TIFF文件...")
            dataset = gdal.Open(layer.file_path, gdal.GA_ReadOnly)
            if dataset is None:
                error_msg = f"无法打开TIFF文件: {layer.file_path}"
                logger.error(error_msg)
                step_collector.append(f"ERROR: {error_msg}")
                return None

            # 读取栅格数据
            step_collector.append("INFO: 正在读取栅格数据...")
            band = dataset.GetRasterBand(1)
            data = band.ReadAsArray()
            nodata_value = band.GetNoDataValue()

            data_info_msg = f"TIFF数据信息: {data.shape}, 数据类型: {data.dtype}"
            logger.info(data_info_msg)
            step_collector.append(f"INFO: {data_info_msg}")

            # 获取唯一值
            step_collector.append("INFO: 正在分析土地利用类型...")
            if nodata_value is not None:
                unique_values = np.unique(data[data != nodata_value])
            else:
                unique_values = np.unique(data)

            unique_msg = f"唯一值: {unique_values}"
            logger.info(unique_msg)
            step_collector.append(f"INFO: {unique_msg}")

            # 创建RGB图像
            step_collector.append("INFO: 正在创建RGB图像...")
            height, width = data.shape
            rgb_image = np.zeros((height, width, 3), dtype=np.uint8)

            # 为每个土地利用类型分配颜色
            step_collector.append("INFO: 正在为土地利用类型分配颜色...")
            for value in unique_values:
                if value in cls.CLCD_COLOR_MAPPING:
                    color_info = cls.CLCD_COLOR_MAPPING[value]
                    rgb_color = color_info['rgb']
                    color_msg = f"类型 {value} ({color_info['name']}): {rgb_color}"
                    logger.info(color_msg)
                    step_collector.append(f"INFO: {color_msg}")
                else:
                    rgb_color = (128, 128, 128)  # 灰色
                    unknown_msg = f"未知类型 {value}: {rgb_color}"
                    logger.info(unknown_msg)
                    step_collector.append(f"INFO: {unknown_msg}")

                # 应用颜色
                mask = (data == value)
                rgb_image[mask] = rgb_color

            # 处理无数据值（设为透明或白色）
            if nodata_value is not None:
                nodata_mask = (data == nodata_value)
                rgb_image[nodata_mask] = [255, 255, 255]  # 白色背景

            # 使用PIL保存为PNG
            step_collector.append("INFO: 正在保存PNG图片...")
            pil_image = Image.fromarray(rgb_image, 'RGB')
            pil_image.save(image_path, 'PNG', optimize=True)

            # 清理GDAL资源
            dataset = None

            success_msg = f"GDAL TIFF转PNG成功: {image_path}"
            logger.info(success_msg)
            step_collector.append(f"INFO: {success_msg}")

            size_msg = f"图片大小: {os.path.getsize(image_path) / 1024:.1f} KB"
            logger.info(size_msg)
            step_collector.append(f"INFO: {size_msg}")

            return f"/static/images/landuse/{image_filename}"

        except ImportError:
            logger.warning("GDAL库不可用，尝试使用备用方法")
            return cls._generate_tiff_image_fallback(layer)
        except Exception as e:
            logger.error(f"GDAL生成TIFF图片失败: {e}")
            return cls._generate_tiff_image_fallback(layer)

    @classmethod
    def _generate_tiff_image_fallback(cls, layer: GISLayer) -> Optional[str]:
        """
        备用的TIFF图片生成方法

        Args:
            layer: GIS图层对象

        Returns:
            图片URL，失败返回None
        """
        try:
            import rasterio
            import matplotlib.pyplot as plt
            import matplotlib.colors as mcolors

            logger.info(f"使用备用方法生成TIFF图片: {layer.name}")

            # 确保图片目录存在
            images_dir = os.path.join(current_app.static_folder, 'images', 'landuse')
            os.makedirs(images_dir, exist_ok=True)

            # 生成图片文件名
            base_name = os.path.splitext(os.path.basename(layer.file_path))[0]
            image_filename = f"{base_name}_{layer.id}_fallback.png"
            image_path = os.path.join(images_dir, image_filename)

            with rasterio.open(layer.file_path) as src:
                # 读取数据
                data = src.read(1)

                # 创建颜色映射
                unique_values = np.unique(data[data != src.nodata]) if src.nodata is not None else np.unique(data)
                colors = []
                labels = []

                for value in unique_values:
                    if value in cls.CLCD_COLOR_MAPPING:
                        mapping = cls.CLCD_COLOR_MAPPING[value]
                        colors.append(np.array(mapping['rgb']) / 255.0)
                        labels.append(mapping['name'])
                    else:
                        colors.append([0.5, 0.5, 0.5])  # 灰色
                        labels.append(f'未知({value})')

                # 创建自定义颜色映射
                cmap = mcolors.ListedColormap(colors)
                bounds = list(unique_values) + [max(unique_values) + 1]
                norm = mcolors.BoundaryNorm(bounds, cmap.N)

                # 创建图片
                plt.figure(figsize=(12, 10))
                plt.imshow(data, cmap=cmap, norm=norm)
                plt.title(f'{layer.province or ""}Land Use Distribution', fontsize=16, pad=20)

                # 添加颜色条
                cbar = plt.colorbar(shrink=0.8)
                cbar.set_ticks(unique_values)
                cbar.set_ticklabels(labels)

                plt.axis('off')
                plt.tight_layout()
                plt.savefig(image_path, dpi=150, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
                plt.close()

                logger.info(f"备用方法TIFF图片生成成功: {image_path}")
                return f"/static/images/landuse/{image_filename}"

        except Exception as e:
            logger.error(f"备用方法生成TIFF图片失败: {e}")
            return None
    
    
    @classmethod
    def analyze_landuse_statistics(cls, layer: GISLayer) -> Dict:
        """
        分析土地利用统计信息
        
        Args:
            layer: GIS图层对象
            
        Returns:
            统计信息字典
        """
        try:
            import rasterio
            
            with rasterio.open(layer.file_path) as src:
                data = src.read(1)
                valid_data = data[data != src.nodata] if src.nodata is not None else data
                
                unique_values, counts = np.unique(valid_data, return_counts=True)
                total_pixels = len(valid_data)
                
                statistics = {
                    'total_pixels': int(total_pixels),
                    'landuse_types': {},
                    'dominant_type': None,
                    'diversity_index': len(unique_values)
                }
                
                max_count = 0
                dominant_type = None
                
                for value, count in zip(unique_values, counts):
                    percentage = (count / total_pixels) * 100
                    
                    if value in cls.CLCD_COLOR_MAPPING:
                        type_info = cls.CLCD_COLOR_MAPPING[value]
                        type_name = type_info['name']
                    else:
                        type_name = f'未知类型({value})'
                    
                    statistics['landuse_types'][type_name] = {
                        'value': int(value),
                        'pixels': int(count),
                        'percentage': round(percentage, 2)
                    }
                    
                    if count > max_count:
                        max_count = count
                        dominant_type = type_name
                
                statistics['dominant_type'] = dominant_type
                
                return statistics
                
        except Exception as e:
            logger.error(f"分析土地利用统计失败: {e}")
            return {}
    
    @classmethod
    def _save_png_metadata(cls, layer: GISLayer, png_url: str):
        """
        保存PNG图片元数据到数据库

        Args:
            layer: GIS图层对象
            png_url: PNG图片URL
        """
        try:
            # 创建或更新extra_metadata
            extra_metadata = {}
            if layer.extra_metadata and isinstance(layer.extra_metadata, dict):
                extra_metadata = layer.extra_metadata.copy()

            extra_metadata['png_image_url'] = png_url
            extra_metadata['png_generated_at'] = str(layer.updated_at or layer.created_at)

            # 保存到数据库
            layer.extra_metadata = extra_metadata
            db.session.commit()

            logger.info(f"PNG元数据已保存: {png_url}")

        except Exception as e:
            logger.error(f"保存PNG元数据失败: {e}")

    @classmethod
    def get_existing_png_image(cls, layer: GISLayer) -> Optional[str]:
        """
        获取已生成的PNG图片URL

        Args:
            layer: GIS图层对象

        Returns:
            PNG图片URL，如果不存在返回None
        """
        try:
            # 处理不同类型的extra_metadata
            extra_metadata = layer.extra_metadata
            if not extra_metadata:
                return None

            # 如果extra_metadata是字典类型
            if isinstance(extra_metadata, dict):
                png_url = extra_metadata.get('png_image_url')
            # 如果extra_metadata是其他类型，尝试转换
            else:
                try:
                    import json
                    if hasattr(extra_metadata, '__dict__'):
                        metadata_dict = extra_metadata.__dict__
                    else:
                        metadata_dict = json.loads(str(extra_metadata))
                    png_url = metadata_dict.get('png_image_url')
                except:
                    logger.warning(f"无法解析extra_metadata: {type(extra_metadata)}")
                    return None

            if not png_url:
                return None

            # 检查PNG文件是否仍然存在
            if png_url.startswith('/static/'):
                # 构建正确的文件路径
                from flask import current_app
                png_path = os.path.join(current_app.static_folder, png_url[8:])  # 去掉'/static/'
                if os.path.exists(png_path):
                    logger.info(f"找到已生成的PNG图片: {png_url}")
                    return png_url
                else:
                    logger.warning(f"PNG文件不存在，需要重新生成: {png_path}")
                    return None

            return png_url

        except Exception as e:
            logger.error(f"获取已生成PNG图片失败: {e}")
            return None

    @classmethod
    def get_or_generate_png_image(cls, layer: GISLayer) -> Optional[str]:
        """
        获取或生成PNG图片（优先使用已生成的）

        Args:
            layer: GIS图层对象

        Returns:
            PNG图片URL，失败返回None
        """
        try:
            # 1. 首先尝试获取已生成的PNG图片
            existing_png = cls.get_existing_png_image(layer)
            if existing_png:
                logger.info(f"使用已生成的PNG图片: {existing_png}")
                return existing_png

            # 2. 如果没有已生成的图片，则生成新的
            logger.info(f"未找到已生成的PNG图片，开始生成新图片: {layer.name}")
            new_png = cls.generate_tiff_image(layer)

            if new_png:
                # 保存到数据库
                cls._save_png_metadata(layer, new_png)
                logger.info(f"新PNG图片已生成并保存: {new_png}")

            return new_png

        except Exception as e:
            logger.error(f"获取或生成PNG图片失败: {e}")
            return None

    @classmethod
    def fix_province_names(cls):
        """修复数据库中的省份名称不一致问题"""
        try:
            layers = GISLayer.query.filter_by(data_category='landuse').all()

            for layer in layers:
                if layer.province == '河南':
                    layer.province = '河南省'
                    logger.info(f"修复省份名称: {layer.name} -> 河南省")

            db.session.commit()
            logger.info("省份名称修复完成")

        except Exception as e:
            logger.error(f"修复省份名称失败: {e}")


# 创建全局实例
enhanced_landuse_processor = EnhancedLanduseProcessor()
