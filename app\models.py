"""
城市内涝风险评估系统 - 数据模型定义
"""
import json
from datetime import datetime, timezone, timedelta
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app.extensions import db


def get_china_time():
    """获取中国本地时间（UTC+8）"""
    return datetime.now(timezone(timedelta(hours=8)))


class Role(db.Model):
    """用户角色模型"""
    __tablename__ = 'roles'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(20), unique=True, nullable=False)
    description = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=get_china_time)

    # 关系
    users = db.relationship('User', backref='role', lazy='dynamic')

    def __repr__(self):
        return f'<Role {self.name}>'


class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False, index=True)
    email = db.Column(db.String(100), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    salt = db.Column(db.String(32))
    is_active = db.Column(db.Boolean, default=True)
    last_login = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=get_china_time)
    updated_at = db.Column(db.DateTime, default=get_china_time, onupdate=get_china_time)

    # 外键
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'))

    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        """检查是否为管理员"""
        return self.role and self.role.name == 'admin'

    def __repr__(self):
        return f'<User {self.username}>'


class Dataset(db.Model):
    """数据集模型"""
    __tablename__ = 'datasets'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    data_type = db.Column(db.String(50), nullable=False)  # 'land_use', 'rivers', 'drainage', 'raster', 'dem', 'vector', 'geojson', 'other'
    file_path = db.Column(db.String(255))
    feature_count = db.Column(db.Integer, default=0)
    coordinate_system = db.Column(db.String(20), default='EPSG:4326')
    bounds = db.Column(db.String(100))  # JSON格式存储边界框
    file_size = db.Column(db.Integer)  # 文件大小（字节）

    # 栅格数据专用字段
    is_raster = db.Column(db.Boolean, default=False)  # 是否为栅格数据
    raster_width = db.Column(db.Integer)  # 栅格宽度（像素）
    raster_height = db.Column(db.Integer)  # 栅格高度（像素）
    pixel_size_x = db.Column(db.Float)  # X方向像素大小
    pixel_size_y = db.Column(db.Float)  # Y方向像素大小
    data_format = db.Column(db.String(20))  # 数据格式 (AIG, GTiff, etc.)

    # DEM专用统计字段
    min_elevation = db.Column(db.Float)  # 最小高程
    max_elevation = db.Column(db.Float)  # 最大高程
    mean_elevation = db.Column(db.Float)  # 平均高程
    std_elevation = db.Column(db.Float)  # 高程标准差
    elevation_stats = db.Column(db.Text)  # JSON格式存储详细统计信息

    # 文件处理状态字段
    processing_status = db.Column(db.String(20), default='uploaded')  # 'uploaded', 'processing', 'processed', 'error'
    processing_log = db.Column(db.Text)  # 处理日志
    original_filename = db.Column(db.String(255))  # 原始文件名
    file_extension = db.Column(db.String(10))  # 文件扩展名

    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=get_china_time)
    updated_at = db.Column(db.DateTime, default=get_china_time, onupdate=get_china_time)

    # 关系
    creator = db.relationship('User', backref='datasets')

    def __repr__(self):
        return f'<Dataset {self.name}>'

    def to_dict(self):
        """转换为字典格式"""
        result = {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'data_type': self.data_type,
            'file_path': self.file_path,
            'coordinate_system': self.coordinate_system,
            'file_size': self.file_size,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

        # 添加栅格数据特有信息
        if self.is_raster:
            result.update({
                'is_raster': True,
                'raster_width': self.raster_width,
                'raster_height': self.raster_height,
                'pixel_size_x': self.pixel_size_x,
                'pixel_size_y': self.pixel_size_y,
                'data_format': self.data_format
            })

            # 添加DEM统计信息
            if self.data_type == 'dem':
                result.update({
                    'min_elevation': self.min_elevation,
                    'max_elevation': self.max_elevation,
                    'mean_elevation': self.mean_elevation,
                    'std_elevation': self.std_elevation
                })

        return result


class SystemLog(db.Model):
    """系统日志模型"""
    __tablename__ = 'system_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    action = db.Column(db.String(100), nullable=False)  # 操作类型
    target = db.Column(db.String(200))  # 操作目标
    details = db.Column(db.Text)  # 详细信息
    ip_address = db.Column(db.String(45))  # IP地址
    user_agent = db.Column(db.String(500))  # 用户代理
    created_at = db.Column(db.DateTime, default=get_china_time)

    # 关系
    user = db.relationship('User', backref='logs')

    def __repr__(self):
        return f'<SystemLog {self.action}>'

    def to_dict(self):
        try:
            username = self.user.username if self.user else None
        except:
            username = None

        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': username,
            'action': self.action,
            'target': self.target,
            'details': self.details,
            'ip_address': self.ip_address,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class GISLayer(db.Model):
    """GIS图层模型 - 专门用于管理上传的地理数据图层"""
    __tablename__ = 'gis_layers'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 图层名称
    display_name = db.Column(db.String(100))  # 显示名称
    description = db.Column(db.Text)  # 图层描述

    # 数据类型和分类
    layer_type = db.Column(db.String(20), nullable=False)  # 'vector' 或 'raster'
    data_category = db.Column(db.String(50), nullable=False)  # 'administrative' (区划) 或 'landuse' (土地利用)
    province = db.Column(db.String(50))  # 所属省份

    # 文件信息
    file_path = db.Column(db.String(255), nullable=False)  # 主文件路径
    related_files = db.Column(db.Text)  # JSON格式存储相关文件路径（如shapefile的.dbf, .prj等）
    original_filename = db.Column(db.String(255))  # 原始文件名
    file_size = db.Column(db.Integer)  # 文件大小（字节）

    # 地理信息
    coordinate_system = db.Column(db.String(50), default='EPSG:4326')  # 坐标系
    bounds = db.Column(db.Text)  # JSON格式存储边界框 [minx, miny, maxx, maxy]

    # 矢量数据专用字段
    feature_count = db.Column(db.Integer)  # 要素数量
    geometry_type = db.Column(db.String(20))  # 几何类型：Point, LineString, Polygon等
    attributes = db.Column(db.Text)  # JSON格式存储属性字段信息

    # 栅格数据专用字段
    raster_width = db.Column(db.Integer)  # 栅格宽度（像素）
    raster_height = db.Column(db.Integer)  # 栅格高度（像素）
    pixel_size_x = db.Column(db.Float)  # X方向像素大小
    pixel_size_y = db.Column(db.Float)  # Y方向像素大小
    band_count = db.Column(db.Integer)  # 波段数量
    data_type = db.Column(db.String(20))  # 数据类型：uint8, float32等

    # 显示设置
    default_style = db.Column(db.Text)  # JSON格式存储默认样式
    is_visible = db.Column(db.Boolean, default=True)  # 是否默认可见
    opacity = db.Column(db.Float, default=1.0)  # 默认透明度
    z_index = db.Column(db.Integer, default=0)  # 图层层级

    # 处理状态
    processing_status = db.Column(db.String(20), default='uploaded')  # 'uploaded', 'processing', 'processed', 'error'
    processing_log = db.Column(db.Text)  # 处理日志

    # 扩展元数据
    extra_metadata = db.Column(db.JSON)  # JSON格式存储扩展元数据（如PNG图片URL等）

    # 基础元数据
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=get_china_time)
    updated_at = db.Column(db.DateTime, default=get_china_time, onupdate=get_china_time)

    # 关系
    creator = db.relationship('User', backref='gis_layers')

    def __repr__(self):
        return f'<GISLayer {self.name}>'

    def to_dict(self):
        """转换为字典格式"""
        result = {
            'id': self.id,
            'name': self.name,
            'display_name': self.display_name,
            'description': self.description,
            'layer_type': self.layer_type,
            'data_category': self.data_category,
            'province': self.province,
            'coordinate_system': self.coordinate_system,
            'is_visible': self.is_visible,
            'opacity': self.opacity,
            'z_index': self.z_index,
            'processing_status': self.processing_status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

        # 解析边界框
        if self.bounds:
            try:
                result['bounds'] = json.loads(self.bounds)
            except:
                result['bounds'] = None

        # 添加矢量数据信息
        if self.layer_type == 'vector':
            result.update({
                'feature_count': self.feature_count,
                'geometry_type': self.geometry_type
            })

            # 解析属性信息
            if self.attributes:
                try:
                    result['attributes'] = json.loads(self.attributes)
                except:
                    result['attributes'] = []

        # 添加栅格数据信息
        elif self.layer_type == 'raster':
            result.update({
                'raster_width': self.raster_width,
                'raster_height': self.raster_height,
                'pixel_size_x': self.pixel_size_x,
                'pixel_size_y': self.pixel_size_y,
                'band_count': self.band_count,
                'data_type': self.data_type
            })

        # 解析默认样式
        if self.default_style:
            try:
                result['default_style'] = json.loads(self.default_style)
            except:
                result['default_style'] = {}

        return result

    def get_bounds_list(self):
        """获取边界框列表格式"""
        if self.bounds:
            try:
                return json.loads(self.bounds)
            except:
                return None
        return None

    def set_bounds(self, bounds_list):
        """设置边界框"""
        if bounds_list and len(bounds_list) == 4:
            self.bounds = json.dumps(bounds_list)

    def get_attributes_list(self):
        """获取属性字段列表"""
        if self.attributes:
            try:
                return json.loads(self.attributes)
            except:
                return []
        return []

    def set_attributes(self, attributes_list):
        """设置属性字段"""
        if attributes_list:
            self.attributes = json.dumps(attributes_list)

    def get_related_files_list(self):
        """获取相关文件列表"""
        if self.related_files:
            try:
                return json.loads(self.related_files)
            except:
                return []
        return []

    def set_related_files(self, files_list):
        """设置相关文件"""
        if files_list:
            self.related_files = json.dumps(files_list)


class AnalysisHistory(db.Model):
    """分析历史记录模型"""
    __tablename__ = 'analysis_history'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    analysis_type = db.Column(db.String(50), nullable=False)  # 分析类型
    parameters = db.Column(db.Text)  # JSON格式的参数
    input_datasets = db.Column(db.Text)  # JSON格式的输入数据集
    results = db.Column(db.Text)  # JSON格式的结果
    status = db.Column(db.String(20), default='completed')  # 状态：running, completed, failed
    execution_time = db.Column(db.Float)  # 执行时间（秒）
    created_at = db.Column(db.DateTime, default=get_china_time)

    # 关系
    user = db.relationship('User', backref='analysis_history')

    def __repr__(self):
        return f'<AnalysisHistory {self.analysis_type}>'

    def to_dict(self):
        import json
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            'analysis_type': self.analysis_type,
            'parameters': json.loads(self.parameters) if self.parameters else None,
            'input_datasets': json.loads(self.input_datasets) if self.input_datasets else None,
            'results': json.loads(self.results) if self.results else None,
            'status': self.status,
            'execution_time': self.execution_time,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class SystemMetrics(db.Model):
    """系统指标记录模型"""
    __tablename__ = 'system_metrics'

    id = db.Column(db.Integer, primary_key=True)
    cpu_usage = db.Column(db.Float)  # CPU使用率
    memory_usage = db.Column(db.Float)  # 内存使用率
    disk_usage = db.Column(db.Float)  # 磁盘使用率
    active_users = db.Column(db.Integer)  # 活跃用户数
    total_datasets = db.Column(db.Integer)  # 数据集总数
    storage_used = db.Column(db.BigInteger)  # 已使用存储空间（字节）
    created_at = db.Column(db.DateTime, default=get_china_time)

    def __repr__(self):
        return f'<SystemMetrics {self.created_at}>'

    def to_dict(self):
        return {
            'id': self.id,
            'cpu_usage': self.cpu_usage,
            'memory_usage': self.memory_usage,
            'disk_usage': self.disk_usage,
            'active_users': self.active_users,
            'total_datasets': self.total_datasets,
            'storage_used': self.storage_used,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class DataProcessingHistory(db.Model):
    """数据处理历史记录模型"""
    __tablename__ = 'data_processing_history'

    id = db.Column(db.Integer, primary_key=True)
    dataset_id = db.Column(db.Integer, db.ForeignKey('datasets.id', ondelete='CASCADE'), nullable=False)
    operation_type = db.Column(db.String(50), nullable=False)  # clean, transform, preprocess
    operation_name = db.Column(db.String(100), nullable=False)  # 操作名称
    status = db.Column(db.String(20), nullable=False, default='pending')  # pending, processing, completed, failed
    progress = db.Column(db.Integer, default=0)  # 进度百分比 0-100

    # 处理参数和结果
    input_parameters = db.Column(db.Text)  # JSON格式的输入参数
    output_results = db.Column(db.Text)  # JSON格式的输出结果
    error_message = db.Column(db.Text)  # 错误信息
    processing_log = db.Column(db.Text)  # 处理日志

    # 性能指标
    start_time = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    end_time = db.Column(db.DateTime)
    duration_seconds = db.Column(db.Float)  # 处理耗时（秒）
    memory_usage_mb = db.Column(db.Float)  # 内存使用量（MB）

    # 数据质量指标
    input_record_count = db.Column(db.Integer)  # 输入记录数
    output_record_count = db.Column(db.Integer)  # 输出记录数
    quality_score_before = db.Column(db.Float)  # 处理前质量分数
    quality_score_after = db.Column(db.Float)  # 处理后质量分数

    # 用户信息
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    dataset = db.relationship('Dataset', backref=db.backref('processing_history', lazy=True))
    user = db.relationship('User', backref=db.backref('processing_history', lazy=True))

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'dataset_id': self.dataset_id,
            'dataset_name': self.dataset.name if self.dataset else None,
            'operation_type': self.operation_type,
            'operation_name': self.operation_name,
            'status': self.status,
            'progress': self.progress,
            'input_parameters': json.loads(self.input_parameters) if self.input_parameters else None,
            'output_results': json.loads(self.output_results) if self.output_results else None,
            'error_message': self.error_message,
            'processing_log': self.processing_log,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration_seconds': self.duration_seconds,
            'memory_usage_mb': self.memory_usage_mb,
            'input_record_count': self.input_record_count,
            'output_record_count': self.output_record_count,
            'quality_score_before': self.quality_score_before,
            'quality_score_after': self.quality_score_after,
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def update_progress(self, progress: int, log_message: str = None):
        """更新处理进度"""
        self.progress = max(0, min(100, progress))
        self.updated_at = datetime.utcnow()

        if log_message:
            if self.processing_log:
                self.processing_log += f"\n{log_message}"
            else:
                self.processing_log = log_message

        db.session.commit()

    def mark_completed(self, results: dict = None, quality_after: float = None):
        """标记为完成"""
        self.status = 'completed'
        self.end_time = datetime.utcnow()
        self.progress = 100

        if self.start_time and self.end_time:
            self.duration_seconds = (self.end_time - self.start_time).total_seconds()

        if results:
            self.output_results = json.dumps(results, ensure_ascii=False)

        if quality_after is not None:
            self.quality_score_after = quality_after

        self.updated_at = datetime.utcnow()
        db.session.commit()

    def mark_failed(self, error_message: str):
        """标记为失败"""
        self.status = 'failed'
        self.end_time = datetime.utcnow()
        self.error_message = error_message

        if self.start_time and self.end_time:
            self.duration_seconds = (self.end_time - self.start_time).total_seconds()

        self.updated_at = datetime.utcnow()
        db.session.commit()

    def __repr__(self):
        return f'<DataProcessingHistory {self.operation_name} for Dataset {self.dataset_id}>'


class DataProcessingConfig(db.Model):
    """数据处理配置模型"""
    __tablename__ = 'data_processing_config'

    id = db.Column(db.Integer, primary_key=True)
    config_name = db.Column(db.String(100), nullable=False, unique=True)
    config_type = db.Column(db.String(50), nullable=False)  # clean, transform, preprocess, batch
    description = db.Column(db.Text)

    # 配置参数
    config_parameters = db.Column(db.Text, nullable=False)  # JSON格式的配置参数
    is_default = db.Column(db.Boolean, default=False)  # 是否为默认配置
    is_active = db.Column(db.Boolean, default=True)  # 是否激活

    # 适用范围
    data_types = db.Column(db.String(200))  # 适用的数据类型，逗号分隔
    cities = db.Column(db.String(200))  # 适用的城市，逗号分隔

    # 用户信息
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    creator = db.relationship('User', backref=db.backref('processing_configs', lazy=True))

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'config_name': self.config_name,
            'config_type': self.config_type,
            'description': self.description,
            'config_parameters': json.loads(self.config_parameters) if self.config_parameters else None,
            'is_default': self.is_default,
            'is_active': self.is_active,
            'data_types': self.data_types.split(',') if self.data_types else [],
            'cities': self.cities.split(',') if self.cities else [],
            'created_by': self.created_by,
            'creator_name': self.creator.username if self.creator else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def get_parameters(self):
        """获取配置参数"""
        try:
            return json.loads(self.config_parameters) if self.config_parameters else {}
        except:
            return {}

    def set_parameters(self, parameters: dict):
        """设置配置参数"""
        self.config_parameters = json.dumps(parameters, ensure_ascii=False)
        self.updated_at = datetime.utcnow()

    def is_applicable_to(self, data_type: str, city: str = None) -> bool:
        """检查配置是否适用于指定的数据类型和城市"""
        if not self.is_active:
            return False

        # 检查数据类型
        if self.data_types:
            applicable_types = [t.strip() for t in self.data_types.split(',')]
            if data_type not in applicable_types:
                return False

        # 检查城市
        if city and self.cities:
            applicable_cities = [c.strip() for c in self.cities.split(',')]
            if city not in applicable_cities:
                return False

        return True

    def __repr__(self):
        return f'<DataProcessingConfig {self.config_name}>'


class RiskWeightConfig(db.Model):
    """风险评估权重配置模型"""
    __tablename__ = 'risk_weight_configs'

    id = db.Column(db.Integer, primary_key=True)
    config_name = db.Column(db.String(100), nullable=False)  # 配置方案名称
    description = db.Column(db.Text)  # 配置描述

    # 发生概率权重配置
    rainfall_weight = db.Column(db.Float, nullable=False, default=0.633)  # w_R 降雨权重
    drainage_weight = db.Column(db.Float, nullable=False, default=0.260)  # w_D 排水能力权重
    elevation_weight = db.Column(db.Float, nullable=False, default=0.107)  # w_T 地形权重

    # 影响程度权重配置
    gdp_weight = db.Column(db.Float, nullable=False, default=0.6)  # ω₄ GDP密度权重
    population_weight = db.Column(db.Float, nullable=False, default=0.4)  # ω₅ 人口密度权重
    impact_weight_type = db.Column(db.String(20), nullable=False, default='economic')  # 权重类型：economic, balanced, population

    is_default = db.Column(db.Boolean, default=False)  # 是否为默认配置
    is_active = db.Column(db.Boolean, default=True)  # 是否激活
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))  # 创建者
    created_at = db.Column(db.DateTime, default=get_china_time)
    updated_at = db.Column(db.DateTime, default=get_china_time, onupdate=get_china_time)

    # 关系
    creator = db.relationship('User', backref='risk_weight_configs')

    def validate_weights(self):
        """验证权重配置是否有效（新版本）"""
        # 检查发生概率权重之和是否为1
        total_likelihood = self.rainfall_weight + self.drainage_weight + self.elevation_weight
        if abs(total_likelihood - 1.0) > 0.001:
            return False, f"发生概率权重之和应为1.0，当前为{total_likelihood:.3f}"

        # 检查影响程度权重之和是否为1
        total_impact = self.gdp_weight + self.population_weight
        if abs(total_impact - 1.0) > 0.001:
            return False, f"影响程度权重之和应为1.0，当前为{total_impact:.3f}"

        return True, "权重配置有效"

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'config_name': self.config_name,
            'description': self.description,
            'rainfall_weight': self.rainfall_weight,
            'drainage_weight': self.drainage_weight,
            'elevation_weight': self.elevation_weight,
            'gdp_weight': self.gdp_weight,
            'population_weight': self.population_weight,
            'impact_weight_type': self.impact_weight_type,
            'is_default': self.is_default,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def set_impact_weight_type(self, weight_type: str):
        """设置影响程度权重类型"""
        if weight_type == 'economic':
            self.gdp_weight = 0.6
            self.population_weight = 0.4
            self.impact_weight_type = 'economic'
        elif weight_type == 'balanced':
            self.gdp_weight = 0.5
            self.population_weight = 0.5
            self.impact_weight_type = 'balanced'
        elif weight_type == 'population':
            self.gdp_weight = 0.4
            self.population_weight = 0.6
            self.impact_weight_type = 'population'
        else:
            raise ValueError(f"不支持的权重类型: {weight_type}")

    def get_impact_weight_type_name(self):
        """获取影响程度权重类型名称"""
        type_names = {
            'economic': '经济优先型',
            'balanced': '均衡型',
            'population': '人口优先型'
        }
        return type_names.get(self.impact_weight_type, '未知类型')

    def __repr__(self):
        return f'<RiskWeightConfig {self.config_name}>'


class RiskAssessmentProject(db.Model):
    """风险评估项目模型"""
    __tablename__ = 'risk_assessment_projects'

    id = db.Column(db.Integer, primary_key=True)
    project_name = db.Column(db.String(100), nullable=False)  # 项目名称
    description = db.Column(db.Text)  # 项目描述
    location = db.Column(db.String(100))  # 评估区域/位置
    assessment_year = db.Column(db.Integer)  # 评估年份

    # 关联的权重配置
    weight_config_id = db.Column(db.Integer, db.ForeignKey('risk_weight_configs.id'))

    # 项目状态
    status = db.Column(db.String(20), default='draft')  # 'draft', 'calculating', 'completed', 'archived'

    # 项目元数据
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=get_china_time)
    updated_at = db.Column(db.DateTime, default=get_china_time, onupdate=get_china_time)
    completed_at = db.Column(db.DateTime)  # 完成时间

    # 关系
    creator = db.relationship('User', backref='risk_assessment_projects')
    weight_config = db.relationship('RiskWeightConfig', backref='assessment_projects')

    def __repr__(self):
        return f'<RiskAssessmentProject {self.project_name}>'


class RiskAssessmentData(db.Model):
    """风险评估数据模型"""
    __tablename__ = 'risk_assessment_data'

    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('risk_assessment_projects.id'), nullable=False)

    # 数据标识
    data_name = db.Column(db.String(100))  # 数据点名称/标识
    location_name = db.Column(db.String(100))  # 位置名称
    coordinates = db.Column(db.String(100))  # 坐标信息（JSON格式）

    # 风险评估指标
    # 发生概率相关指标
    annual_precipitation = db.Column(db.Float)  # 全年降水量（正向指标）
    drainage_network_density = db.Column(db.Float)  # 排水管网密度（负向指标）
    elevation = db.Column(db.Float)  # 平均高程值（负向指标）

    # 影响程度相关指标
    population_density = db.Column(db.Float)  # 人口密度（正向指标）
    gdp_per_area = db.Column(db.Float)  # GDP密度（正向指标）

    # 数据来源信息
    data_source = db.Column(db.String(50), default='manual')  # 'manual', 'file_import', 'api'
    import_batch_id = db.Column(db.String(50))  # 批量导入批次ID

    created_at = db.Column(db.DateTime, default=get_china_time)
    updated_at = db.Column(db.DateTime, default=get_china_time, onupdate=get_china_time)

    # 关系
    project = db.relationship('RiskAssessmentProject', backref='assessment_data')

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'project_id': self.project_id,
            'data_name': self.data_name,
            'location_name': self.location_name,
            'coordinates': self.coordinates,
            # 风险评估指标
            'annual_precipitation': self.annual_precipitation,
            'drainage_network_density': self.drainage_network_density,
            'elevation': self.elevation,
            'population_density': self.population_density,
            'gdp_per_area': self.gdp_per_area,
            'data_source': self.data_source,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<RiskAssessmentData {self.data_name}>'


class RiskCalculationResult(db.Model):
    """风险计算结果模型"""
    __tablename__ = 'risk_calculation_results'

    id = db.Column(db.Integer, primary_key=True)
    data_id = db.Column(db.Integer, db.ForeignKey('risk_assessment_data.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('risk_assessment_projects.id'), nullable=False)

    # 计算结果
    likelihood_probability = db.Column(db.Float)  # P - 发生概率
    impact_degree = db.Column(db.Float)  # I - 影响程度
    risk_index = db.Column(db.Float)  # RI - 综合风险指数

    # 风险矩阵相关（新版本4×4矩阵）
    likelihood_level = db.Column(db.String(20))  # 发生概率等级：'A', 'B', 'C', 'D'
    impact_level = db.Column(db.String(20))  # 影响程度等级：'轻微', '中等', '严重', '灾难'
    final_risk_level = db.Column(db.String(20))  # 最终风险等级：'低风险', '中风险', '高风险', '极高风险'

    # 计算元数据
    calculation_method = db.Column(db.String(50), default='weighted_sum')  # 计算方法
    weight_config_snapshot = db.Column(db.Text)  # 计算时使用的权重配置快照（JSON格式）

    calculated_at = db.Column(db.DateTime, default=get_china_time)
    calculated_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # 关系
    assessment_data = db.relationship('RiskAssessmentData', backref='calculation_results')
    project = db.relationship('RiskAssessmentProject', backref='calculation_results')
    calculator = db.relationship('User', backref='risk_calculations')

    def get_risk_level_color(self):
        """获取风险等级对应的颜色"""
        color_map = {
            '极低风险': '#d4edda',  # 浅绿色
            '低风险': '#28a745',    # 绿色
            '中风险': '#ffc107',    # 黄色
            '高风险': '#fd7e14',    # 橙色
            '极高风险': '#dc3545'   # 红色
        }
        return color_map.get(self.final_risk_level, '#6c757d')

    def get_risk_level_badge_class(self):
        """获取风险等级对应的Bootstrap徽章样式类"""
        class_map = {
            '极低风险': 'success',
            '低风险': 'success',
            '中风险': 'warning',
            '高风险': 'high-risk',  # 使用自定义样式类
            '极高风险': 'danger'
        }
        return class_map.get(self.final_risk_level, 'secondary')

    def to_dict(self):
        """转换为字典格式"""
        result_dict = {
            'id': self.id,
            'data_id': self.data_id,
            'project_id': self.project_id,
            # 计算结果
            'likelihood_probability': self.likelihood_probability,
            'impact_degree': self.impact_degree,
            'risk_index': self.risk_index,
            # 风险等级
            'likelihood_level': self.likelihood_level,
            'impact_level': self.impact_level,
            'final_risk_level': self.final_risk_level,
            'risk_level_color': self.get_risk_level_color(),
            'risk_level_badge_class': self.get_risk_level_badge_class(),
            'calculated_at': self.calculated_at.isoformat() if self.calculated_at else None
        }

        # 如果有关联的评估数据，添加位置信息
        if hasattr(self, 'assessment_data') and self.assessment_data:
            result_dict.update({
                'data_name': self.assessment_data.data_name,
                'location_name': self.assessment_data.location_name,
                'coordinates': self.assessment_data.coordinates
            })

        return result_dict

    def __repr__(self):
        return f'<RiskCalculationResult {self.final_risk_level}>'


class RiskMatrix(db.Model):
    """风险矩阵配置模型"""
    __tablename__ = 'risk_matrices'

    id = db.Column(db.Integer, primary_key=True)
    matrix_name = db.Column(db.String(100), nullable=False)  # 矩阵名称
    description = db.Column(db.Text)  # 矩阵描述

    # 矩阵配置（5x5矩阵的风险等级映射）
    matrix_config = db.Column(db.Text, nullable=False)  # JSON格式存储矩阵配置

    # 发生概率等级定义
    likelihood_levels = db.Column(db.Text)  # JSON格式存储概率等级定义

    # 影响程度等级定义
    impact_levels = db.Column(db.Text)  # JSON格式存储影响程度等级定义

    is_default = db.Column(db.Boolean, default=False)  # 是否为默认矩阵
    is_active = db.Column(db.Boolean, default=True)  # 是否激活

    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=get_china_time)
    updated_at = db.Column(db.DateTime, default=get_china_time, onupdate=get_china_time)

    # 关系
    creator = db.relationship('User', backref='risk_matrices')

    def get_matrix_config(self):
        """获取矩阵配置"""
        if self.matrix_config:
            return json.loads(self.matrix_config)
        return None

    def set_matrix_config(self, config):
        """设置矩阵配置"""
        self.matrix_config = json.dumps(config, ensure_ascii=False)

    def get_likelihood_levels(self):
        """获取发生概率等级定义"""
        if self.likelihood_levels:
            return json.loads(self.likelihood_levels)
        return None

    def set_likelihood_levels(self, levels):
        """设置发生概率等级定义"""
        self.likelihood_levels = json.dumps(levels, ensure_ascii=False)

    def get_impact_levels(self):
        """获取影响程度等级定义"""
        if self.impact_levels:
            return json.loads(self.impact_levels)
        return None

    def set_impact_levels(self, levels):
        """设置影响程度等级定义"""
        self.impact_levels = json.dumps(levels, ensure_ascii=False)

    def __repr__(self):
        return f'<RiskMatrix {self.matrix_name}>'

