"""
地理边界数据管理
基于pyecharts提供省份和城市的地理边界数据，用于风险区划图的区域着色
"""
import json
import os
from typing import Dict, List, Optional, Tuple
from pyecharts import options as opts
from pyecharts.charts import Map, Geo
from pyecharts.globals import ChartType
from pyecharts.commons.utils import JsCode
import tempfile


class GeoBoundariesManager:
    """基于pyecharts的地理边界数据管理器"""
    
    def __init__(self):
        """初始化地理边界管理器"""
        self._boundaries_cache = {}
        self._map_cache = {}

        # 风险等级颜色映射
        self.risk_colors = {
            '未评估': '#e9ecef',      # 浅灰色
            '极低风险': '#d4edda',    # 浅绿色
            '低风险': '#28a745',      # 绿色
            '中风险': '#ffc107',      # 黄色
            '高风险': '#fd7e14',      # 橙色
            '极高风险': '#dc3545'     # 红色
        }

        # 省份到pyecharts地图名称的映射 - 支持所有34个省级行政区
        self.province_map_names = {
            # 直辖市
            '北京市': '北京', '北京': '北京',
            '天津市': '天津', '天津': '天津',
            '上海市': '上海', '上海': '上海',
            '重庆市': '重庆', '重庆': '重庆',
            # 省份
            '河北省': '河北', '河北': '河北',
            '山西省': '山西', '山西': '山西',
            '辽宁省': '辽宁', '辽宁': '辽宁',
            '吉林省': '吉林', '吉林': '吉林',
            '黑龙江省': '黑龙江', '黑龙江': '黑龙江',
            '江苏省': '江苏', '江苏': '江苏',
            '浙江省': '浙江', '浙江': '浙江',
            '安徽省': '安徽', '安徽': '安徽',
            '福建省': '福建', '福建': '福建',
            '江西省': '江西', '江西': '江西',
            '山东省': '山东', '山东': '山东',
            '河南省': '河南', '河南': '河南',
            '湖北省': '湖北', '湖北': '湖北',
            '湖南省': '湖南', '湖南': '湖南',
            '广东省': '广东', '广东': '广东',
            '海南省': '海南', '海南': '海南',
            '四川省': '四川', '四川': '四川',
            '贵州省': '贵州', '贵州': '贵州',
            '云南省': '云南', '云南': '云南',
            '陕西省': '陕西', '陕西': '陕西',
            '甘肃省': '甘肃', '甘肃': '甘肃',
            '青海省': '青海', '青海': '青海',
            # 自治区
            '内蒙古自治区': '内蒙古', '内蒙古': '内蒙古',
            '广西壮族自治区': '广西', '广西': '广西',
            '西藏自治区': '西藏', '西藏': '西藏',
            '宁夏回族自治区': '宁夏', '宁夏': '宁夏',
            '新疆维吾尔自治区': '新疆', '新疆': '新疆',
            # 特别行政区
            '香港特别行政区': '香港', '香港': '香港',
            '澳门特别行政区': '澳门', '澳门': '澳门',
            # 台湾省
            '台湾省': '台湾', '台湾': '台湾'
        }

        # 省份内主要城市映射（用于风险数据匹配）- 使用统一配置
        self.province_cities = self._get_all_province_cities()

    def _get_all_province_cities(self) -> Dict[str, List[str]]:
        """获取所有省份的城市列表"""
        return {
            # 直辖市
            '北京': ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区',
                   '通州区', '顺义区', '大兴区', '昌平区', '平谷区', '怀柔区', '密云区', '延庆区'],
            '天津': ['和平区', '河东区', '河西区', '南开区', '河北区', '红桥区', '东丽区', '西青区',
                   '津南区', '北辰区', '武清区', '宝坻区', '滨海新区', '宁河区', '静海区', '蓟州区'],
            '上海': ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '闵行区',
                   '宝山区', '嘉定区', '浦东新区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区'],
            '重庆': ['万州区', '涪陵区', '渝中区', '大渡口区', '江北区', '沙坪坝区', '九龙坡区', '南岸区',
                   '北碚区', '綦江区', '大足区', '渝北区', '巴南区', '黔江区', '长寿区', '江津区', '合川区',
                   '永川区', '南川区', '璧山区', '铜梁区', '潼南区', '荣昌区', '开州区', '梁平县', '武隆县',
                   '城口县', '丰都县', '垫江县', '忠县', '云阳县', '奉节县', '巫山县', '巫溪县', '石柱土家族自治县',
                   '秀山土家族苗族自治县', '酉阳土家族苗族自治县', '彭水苗族土家族自治县'],

            # 现有省份数据（从pyecharts_maps.py复制）
            '河南': ['郑州市', '洛阳市', '开封市', '南阳市', '安阳市', '商丘市', '新乡市', '平顶山市',
                   '许昌市', '焦作市', '周口市', '信阳市', '驻马店市', '濮阳市', '三门峡市', '漯河市', '鹤壁市', '济源市'],
            '湖北': ['武汉市', '黄石市', '十堰市', '宜昌市', '襄阳市', '鄂州市', '荆门市', '孝感市',
                   '荆州市', '黄冈市', '咸宁市', '神农架林区','天门市','仙桃市','潜江市','随州市', '恩施土家族苗族自治州'],
            '湖南': ['长沙市', '株洲市', '湘潭市', '衡阳市', '邵阳市', '岳阳市', '常德市', '张家界市',
                   '益阳市', '郴州市', '永州市', '怀化市', '娄底市', '湘西土家族苗族自治州'],
            '广东': ['广州市', '深圳市', '珠海市', '汕头市', '佛山市', '韶关市', '湛江市', '肇庆市',
                   '江门市', '茂名市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市',
                   '东莞市', '中山市', '潮州市', '揭阳市', '云浮市'],
            '江苏': ['南京市', '无锡市', '徐州市', '常州市', '苏州市', '南通市', '连云港市', '淮安市',
                   '盐城市', '扬州市', '镇江市', '泰州市', '宿迁市'],
            '浙江': ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市',
                   '舟山市', '台州市', '丽水市'],
            '山东': ['济南市', '青岛市', '淄博市', '枣庄市', '东营市', '烟台市', '潍坊市', '济宁市',
                   '泰安市', '威海市', '日照市', '临沂市', '德州市', '聊城市', '滨州市', '菏泽市'],
            '四川': ['成都市', '自贡市', '攀枝花市', '泸州市', '德阳市', '绵阳市', '广元市', '遂宁市',
                   '内江市', '乐山市', '南充市', '眉山市', '宜宾市', '广安市', '达州市', '雅安市',
                   '巴中市', '资阳市', '阿坝藏族羌族自治州', '甘孜藏族自治州', '凉山彝族自治州'],
            '福建': ['福州市', '厦门市', '莆田市', '三明市', '泉州市', '漳州市', '南平市', '龙岩市', '宁德市'],

            # 添加其他省份的基本城市数据
            '河北': ['石家庄市', '唐山市', '秦皇岛市', '邯郸市', '邢台市', '保定市', '张家口市', '承德市',
                   '沧州市', '廊坊市', '衡水市'],
            '山西': ['太原市', '大同市', '阳泉市', '长治市', '晋城市', '朔州市', '晋中市', '运城市',
                   '忻州市', '临汾市', '吕梁市'],
            '辽宁': ['沈阳市', '大连市', '鞍山市', '抚顺市', '本溪市', '丹东市', '锦州市', '营口市',
                   '阜新市', '辽阳市', '盘锦市', '铁岭市', '朝阳市', '葫芦岛市'],
            '吉林': ['长春市', '吉林市', '四平市', '辽源市', '通化市', '白山市', '松原市', '白城市',
                   '延边朝鲜族自治州'],
            '黑龙江': ['哈尔滨市', '齐齐哈尔市', '鸡西市', '鹤岗市', '双鸭山市', '大庆市', '伊春市', '佳木斯市',
                     '七台河市', '牡丹江市', '黑河市', '绥化市', '大兴安岭地区']
        }

    def get_province_boundaries(self, province_name: str) -> Optional[Dict]:
        """
        获取省份内各城市的地理边界数据（基于pyecharts）
        
        Args:
            province_name: 省份名称
            
        Returns:
            包含各城市边界的字典，格式兼容原有的GeoJSON格式
        """
        if province_name in self._boundaries_cache:
            return self._boundaries_cache[province_name]
        
        # 根据省份生成边界数据
        boundaries = self._generate_pyecharts_boundaries(province_name)
        self._boundaries_cache[province_name] = boundaries
        
        return boundaries
    
    def _generate_pyecharts_boundaries(self, province_name: str) -> Dict:
        """基于pyecharts生成省份边界数据"""
        
        # 标准化省份名称
        map_name = self.province_map_names.get(province_name, province_name.replace('省', ''))
        
        if map_name not in self.province_cities:
            return {
                "type": "FeatureCollection",
                "features": []
            }
        
        # 获取该省份的城市列表
        cities = self.province_cities[map_name]
        
        # 生成GeoJSON格式的特征集合（模拟pyecharts的地理数据）
        features = []
        
        for i, city in enumerate(cities):
            # 为每个城市生成一个特征
            feature = {
                "type": "Feature",
                "properties": {
                    "name": city,
                    "city_code": self._get_city_code(city),
                    "province": province_name,
                    "map_name": map_name
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [self._get_city_approximate_bounds(city, map_name)]
                }
            }
            features.append(feature)
        
        return {
            "type": "FeatureCollection",
            "features": features
        }

    def create_pyecharts_map(self, province_name: str, risk_data: Dict, year: int = None) -> str:
        """
        使用pyecharts创建省份风险地图

        Args:
            province_name: 省份名称
            risk_data: 风险数据字典，格式为 {城市名: 风险等级}
            year: 年份

        Returns:
            生成的HTML文件路径
        """
        try:
            from pyecharts.charts import Map
            from pyecharts import options as opts

            # 标准化省份名称
            map_name = self.province_map_names.get(province_name, province_name.replace('省', ''))

            # 风险等级到数值的映射（用于PyEcharts显示）
            risk_level_values = {
                '未评估': 0,
                '极低风险': 1,
                '低风险': 2,
                '中风险': 3,
                '高风险': 4,
                '极高风险': 5
            }

            # 准备数据
            data_pairs = []
            for city, risk_info in risk_data.items():
                if isinstance(risk_info, dict):
                    risk_level = risk_info.get('risk_level', '未评估')
                    risk_index = risk_info.get('risk_index', 0)
                else:
                    risk_level = risk_info
                    risk_index = 0

                # 标准化城市名称（去掉"市"）
                city_clean = city.replace('市', '')

                # 修复：使用实际的风险指数而不是等级映射值
                # 确保风险指数在0-1范围内
                if risk_index is None:
                    risk_index = 0.0
                elif risk_index > 1.0:
                    risk_index = 1.0
                elif risk_index < 0.0:
                    risk_index = 0.0

                data_pairs.append([city_clean, risk_index])

            # 创建地图
            map_chart = (
                Map()
                .add(
                    series_name="风险指数",
                    data_pair=data_pairs,
                    maptype=map_name,
                    is_roam=True,
                )
                .set_global_opts(
                    title_opts=opts.TitleOpts(
                        title=f"{province_name}内涝风险区划图",
                        subtitle=f"数据年份: {year}" if year else "最新数据"
                    ),
                    visualmap_opts=opts.VisualMapOpts(
                        min_=0.0,  # 修复：使用0-1范围
                        max_=1.0,  # 修复：使用0-1范围
                        range_text=["极高风险", "极低风险"],
                        is_calculable=True,
                        range_color=["#d4edda", "#28a745", "#ffc107", "#fd7e14", "#dc3545"],
                        pieces=[
                            {"min": 0.0, "max": 0.2, "label": "极低风险", "color": "#d4edda"},
                            {"min": 0.2, "max": 0.4, "label": "低风险", "color": "#28a745"},
                            {"min": 0.4, "max": 0.6, "label": "中风险", "color": "#ffc107"},
                            {"min": 0.6, "max": 0.8, "label": "高风险", "color": "#fd7e14"},
                            {"min": 0.8, "max": 1.0, "label": "极高风险", "color": "#dc3545"},
                        ]
                    ),
                    legend_opts=opts.LegendOpts(is_show=False)
                )
                .set_series_opts(
                    label_opts=opts.LabelOpts(is_show=True),
                    tooltip_opts=opts.TooltipOpts(
                        trigger="item",
                        formatter=JsCode("function(params) { var riskIndex = params.value; var riskLevel = ''; if (riskIndex >= 0.8) { riskLevel = '极高风险'; } else if (riskIndex >= 0.6) { riskLevel = '高风险'; } else if (riskIndex >= 0.4) { riskLevel = '中风险'; } else if (riskIndex >= 0.2) { riskLevel = '低风险'; } else { riskLevel = '极低风险'; } return params.name + '<br/>风险指数: ' + (riskIndex * 100).toFixed(1) + '%<br/>风险等级: ' + riskLevel; }")
                    )
                )
            )

            # 生成HTML文件
            temp_dir = tempfile.gettempdir()
            # 使用时间戳确保文件名唯一，避免缓存问题
            import time
            timestamp = int(time.time())
            filename = f"risk_map_{province_name.replace('省', '')}_{year or 'latest'}_{timestamp}.html"
            file_path = os.path.join(temp_dir, filename)

            map_chart.render(file_path)

            return file_path

        except Exception as e:
            print(f"创建pyecharts地图失败: {e}")
            return None

    def get_risk_level_color(self, risk_level: str) -> Optional[str]:
        """根据风险等级获取对应的颜色"""
        # 直接从字典中获取颜色，如果找不到则返回'未评估'的颜色
        return self.risk_colors.get(risk_level, self.risk_colors.get('未评估'))

    def get_risk_level_opacity(self, risk_level: str) -> float:
        """
        获取风险等级对应的透明度

        Args:
            risk_level: 风险等级

        Returns:
            透明度值 (0-1)
        """
        opacity_mapping = {
            '极低风险': 0.3,
            '低风险': 0.4,
            '中风险': 0.6,
            '高风险': 0.7,
            '极高风险': 0.8,
            # 英文版本
            'very_low': 0.3,
            'low': 0.4,
            'medium': 0.6,
            'high': 0.7,
            'very_high': 0.8
        }

        return opacity_mapping.get(risk_level, 0.5)


# 全局实例
geo_boundaries_manager = GeoBoundariesManager()
