"""
GIS城市雨洪灾害风险预警系统 - Flask应用工厂
"""
from flask import Flask, request
from config import config
from app.extensions import init_extensions, db
from app.online_users import online_tracker


def create_app(config_name='default'):
    """应用工厂函数"""
    import os
    # 获取项目根目录
    basedir = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))

    # 指定模板和静态文件目录
    app = Flask(__name__,
                template_folder=os.path.join(basedir, 'templates'),
                static_folder=os.path.join(basedir, 'static'))

    # 加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # 初始化扩展
    init_extensions(app)

    # 注册蓝图
    register_blueprints(app)

    # 注册错误处理器
    register_error_handlers(app)

    # 注册上下文处理器
    register_context_processors(app)

    # 注册请求处理器
    register_request_handlers(app)

    # 初始化数据库和默认数据
    with app.app_context():
        auto_init_database()

    return app


def register_blueprints(app):
    """注册蓝图"""
    # 主页蓝图
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)

    # 认证蓝图
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    # API蓝图
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')

    # 风险评估蓝图
    from app.risk_assessment.routes import bp as risk_assessment_bp
    app.register_blueprint(risk_assessment_bp)

    # 可视化模块蓝图
    from app.visualization import bp as visualization_bp
    app.register_blueprint(visualization_bp)


def register_error_handlers(app):
    """注册错误处理器"""

    @app.errorhandler(404)
    def not_found_error(error):
        from flask import render_template
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        from flask import render_template
        db.session.rollback()
        return render_template('errors/500.html'), 500

    @app.errorhandler(403)
    def forbidden_error(error):
        from flask import render_template
        return render_template('errors/403.html'), 403


def register_context_processors(app):
    """注册上下文处理器"""

    @app.context_processor
    def inject_global_vars():
        """注入全局模板变量"""
        return {
            'app_name': '城市内涝风险评估系统',
            'version': '1.0.0',
            'online_users_count': online_tracker.get_online_count()
        }


def register_request_handlers(app):
    """注册请求处理器"""

    @app.before_request
    def track_user_activity():
        """跟踪用户活动"""
        from flask_login import current_user

        # 获取客户端IP
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if ip_address:
            ip_address = ip_address.split(',')[0].strip()

        # 更新用户活动
        if current_user.is_authenticated:
            online_tracker.update_user_activity(
                user_id=current_user.id,
                username=current_user.username,
                ip_address=ip_address
            )
        else:
            online_tracker.update_user_activity(ip_address=ip_address)


def auto_init_database():
    """自动检查并初始化数据库"""
    import os
    from flask import current_app

    # 获取数据库路径
    db_uri = current_app.config.get('SQLALCHEMY_DATABASE_URI', '')

    # 检查是否为SQLite数据库
    if not db_uri.startswith('sqlite:///'):
        print("⚠️ 非SQLite数据库，跳过自动初始化")
        init_database()  # 对于非SQLite数据库，使用简单初始化
        return

    # 获取数据库文件路径
    db_path = db_uri.replace('sqlite:///', '')

    # 检查数据库文件是否存在
    if not os.path.exists(db_path):
        print("未找到数据库文件，开始自动初始化...")
        run_full_database_init()
    else:
        # 数据库存在，检查是否有必要的表和数据
        try:
            from app.models import User, Role, RiskMatrix

            # 检查关键表是否存在且有数据
            user_count = User.query.count()
            role_count = Role.query.count()

            try:
                matrix_count = RiskMatrix.query.count()
            except:
                matrix_count = 0

            if user_count == 0 or role_count == 0 or matrix_count == 0:
                print("数据库不完整，开始补充初始化...")
                run_full_database_init()
            else:
                print("数据库已存在且完整")

        except Exception as e:
            print(f"数据库检查失败，开始重新初始化: {e}")
            run_full_database_init()


def run_full_database_init():
    """运行完整的数据库初始化"""
    try:
        print("开始完整数据库初始化...")

        # 直接导入并运行初始化函数，避免subprocess的编码问题
        import sys
        import os
        from pathlib import Path

        # 添加scripts目录到路径
        project_root = Path(__file__).parent.parent
        scripts_path = project_root / 'scripts'

        if str(scripts_path) not in sys.path:
            sys.path.insert(0, str(scripts_path))

        try:
            # 导入初始化模块
            from init_database import (
                create_default_roles, create_default_users,
                create_default_risk_matrix, create_default_risk_weight_config,
                create_default_processing_configs
            )

            # 创建所有表
            db.create_all()
            print("数据库表创建完成")

            # 创建角色
            admin_role, user_role = create_default_roles()

            # 创建用户
            admin_user = create_default_users(admin_role, user_role)

            # 创建风险权重配置
            create_default_risk_weight_config(admin_user)

            # 创建风险矩阵
            create_default_risk_matrix(admin_user)

            # 创建数据处理配置
            create_default_processing_configs(admin_user)

            print("完整数据库初始化成功")
            print("默认账户: admin/admin123, demo/demo123")

        except ImportError as e:
            print(f"无法导入初始化模块: {e}")
            print("使用简单初始化作为备选方案...")
            init_database()
        except Exception as e:
            print(f"完整初始化过程中出错: {e}")
            print("使用简单初始化作为备选方案...")
            init_database()

    except Exception as e:
        print(f"运行完整初始化时出错: {e}")
        print("使用简单初始化作为备选方案...")
        init_database()


def init_database():
    """简单的数据库初始化（备选方案）"""
    from app.models import User, Role

    # 创建所有表
    db.create_all()

    # 创建默认角色
    if not Role.query.filter_by(name='admin').first():
        admin_role = Role(name='admin', description='系统管理员')
        db.session.add(admin_role)
        print("✓ 创建管理员角色")

    if not Role.query.filter_by(name='user').first():
        user_role = Role(name='user', description='普通用户')
        db.session.add(user_role)
        print("✓ 创建普通用户角色")

    # 创建默认管理员账户
    if not User.query.filter_by(username='admin').first():
        admin_role = Role.query.filter_by(name='admin').first()
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            role=admin_role,
            is_active=True
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
        print("✓ 创建默认管理员账户 (admin/admin123)")

    # 创建演示用户
    if not User.query.filter_by(username='demo').first():
        user_role = Role.query.filter_by(name='user').first()
        demo_user = User(
            username='demo',
            email='<EMAIL>',
            role=user_role,
            is_active=True
        )
        demo_user.set_password('demo123')
        db.session.add(demo_user)
        print("✓ 创建演示用户账户 (demo/demo123)")

    try:
        db.session.commit()
        print("✓ 基础数据创建完成")

    except Exception as e:
        db.session.rollback()
        print(f"✗ 基础数据创建失败: {e}")