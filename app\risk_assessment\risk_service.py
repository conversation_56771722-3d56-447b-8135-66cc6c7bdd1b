"""
风险评估服务
整合风险计算和矩阵处理功能
"""
from typing import Dict, List, Optional
from datetime import datetime
from app import db
from app.models import (
    RiskWeightConfig, RiskAssessmentProject, RiskAssessmentData, 
    RiskCalculationResult, RiskMatrix
)
from .risk_calculator import RiskCalculator
from .risk_matrix import RiskMatrixProcessor
import json


class RiskAssessmentService:
    """风险评估服务"""
    
    def __init__(self):
        """初始化风险评估服务"""
        pass
    
    def get_default_weight_config(self) -> Optional[RiskWeightConfig]:
        """获取默认权重配置"""
        return RiskWeightConfig.query.filter_by(is_default=True, is_active=True).first()
    
    def get_default_risk_matrix(self) -> Optional[RiskMatrix]:
        """获取默认风险矩阵"""
        return RiskMatrix.query.filter_by(is_default=True, is_active=True).first()

    def get_effective_weight_config(self, project: RiskAssessmentProject) -> Optional[RiskWeightConfig]:
        """
        获取有效的权重配置
        优先级：会话应用权重 > 项目权重配置 > 默认权重配置
        """
        from flask import session

        # 1. 检查会话中是否有应用的权重配置
        applied_weights = session.get('applied_weights')
        if applied_weights:
            # 创建临时权重配置对象
            temp_config = RiskWeightConfig(
                config_name='临时应用权重',
                rainfall_weight=applied_weights['rainfall_weight'],
                drainage_weight=applied_weights['drainage_weight'],
                elevation_weight=applied_weights['elevation_weight'],
                gdp_weight=applied_weights.get('gdp_weight', 0.6),
                population_weight=applied_weights.get('population_weight', 0.4)
            )
            return temp_config

        # 2. 使用项目的权重配置
        if project.weight_config:
            return project.weight_config

        # 3. 使用默认权重配置
        return self.get_default_weight_config()
    
    def create_assessment_project(self, project_name: str, description: str = None,
                                location: str = None, user_id: int = None,
                                weight_config_id: int = None, assessment_year: int = None) -> RiskAssessmentProject:
        """
        创建风险评估项目
        
        Args:
            project_name: 项目名称
            description: 项目描述
            location: 评估区域
            user_id: 创建用户ID
            weight_config_id: 权重配置ID
            
        Returns:
            创建的项目对象
        """
        # 如果没有指定权重配置，使用默认配置
        if not weight_config_id:
            default_config = self.get_default_weight_config()
            weight_config_id = default_config.id if default_config else None
        
        project = RiskAssessmentProject(
            project_name=project_name,
            description=description,
            location=location,
            assessment_year=assessment_year,
            weight_config_id=weight_config_id,
            created_by=user_id,
            status='draft'
        )
        
        db.session.add(project)
        db.session.commit()
        
        return project

    def create_multi_year_projects(self, province_name: str, years_data: Dict,
                                 user_id: int = None, weight_config_id: int = None) -> Dict:
        """
        创建多年份风险评估项目

        Args:
            province_name: 省份名称
            years_data: {year: data_list} 格式的数据
            user_id: 创建用户ID
            weight_config_id: 权重配置ID

        Returns:
            创建结果
        """
        results = {
            'created_projects': [],
            'failed_projects': [],
            'total_records': 0
        }

        # 如果没有指定权重配置，使用默认配置
        if not weight_config_id:
            default_config = self.get_default_weight_config()
            weight_config_id = default_config.id if default_config else None

        for year, data_list in years_data.items():
            try:
                # 检查是否已存在该年份的项目
                existing_project = RiskAssessmentProject.query.filter_by(
                    location=province_name,
                    assessment_year=year
                ).first()

                if existing_project:
                    # 更新现有项目
                    project = existing_project
                    project.updated_at = datetime.now()
                    # 删除旧数据
                    RiskAssessmentData.query.filter_by(project_id=project.id).delete()
                    RiskCalculationResult.query.filter_by(project_id=project.id).delete()
                else:
                    # 创建新项目
                    project = self.create_assessment_project(
                        project_name=f"{province_name}{year}年内涝风险评估",
                        description=f"{province_name}各市{year}年内涝风险评估项目",
                        location=province_name,
                        assessment_year=year,
                        user_id=user_id,
                        weight_config_id=weight_config_id
                    )

                # 添加评估数据
                created_data = self.add_assessment_data(project.id, data_list)

                results['created_projects'].append({
                    'project_id': project.id,
                    'project_name': project.project_name,
                    'year': year,
                    'data_count': len(created_data)
                })
                results['total_records'] += len(created_data)

            except Exception as e:
                results['failed_projects'].append({
                    'year': year,
                    'error': str(e)
                })

        return results
    
    def add_assessment_data(self, project_id: int, data_list: List[Dict]) -> List[RiskAssessmentData]:
        """
        添加评估数据到项目
        
        Args:
            project_id: 项目ID
            data_list: 数据列表
            
        Returns:
            创建的数据对象列表
        """
        created_data = []
        
        for data_dict in data_list:
            assessment_data = RiskAssessmentData(
                project_id=project_id,
                data_name=data_dict.get('data_name'),
                location_name=data_dict.get('location_name'),
                coordinates=data_dict.get('coordinates'),
                annual_precipitation=data_dict.get('annual_precipitation'),
                drainage_network_density=data_dict.get('drainage_network_density'),
                elevation=data_dict.get('elevation'),
                population_density=data_dict.get('population_density'),
                gdp_per_area=data_dict.get('gdp_per_area'),
                data_source=data_dict.get('data_source', 'manual'),
                import_batch_id=data_dict.get('import_batch_id')
            )
            
            db.session.add(assessment_data)
            created_data.append(assessment_data)
        
        db.session.commit()
        return created_data
    
    def calculate_project_risks(self, project_id: int, user_id: int = None) -> List[RiskCalculationResult]:
        """
        计算项目的风险评估结果
        
        Args:
            project_id: 项目ID
            user_id: 计算用户ID
            
        Returns:
            计算结果列表
        """
        # 获取项目和相关数据
        project = RiskAssessmentProject.query.get(project_id)
        if not project:
            raise ValueError(f"项目 {project_id} 不存在")
        
        # 获取权重配置 - 优先使用会话中的应用权重
        weight_config = self.get_effective_weight_config(project)
        if not weight_config:
            raise ValueError("未找到有效的权重配置")
        
        # 获取风险矩阵
        risk_matrix = self.get_default_risk_matrix()
        if not risk_matrix:
            raise ValueError("未找到有效的风险矩阵配置")
        
        # 获取项目的评估数据
        assessment_data_list = RiskAssessmentData.query.filter_by(project_id=project_id).all()
        if not assessment_data_list:
            raise ValueError("项目中没有评估数据")
        
        # 标准化数据
        normalized_data = RiskCalculator.auto_normalize_dataset(assessment_data_list)
        
        # 初始化计算器和矩阵处理器
        calculator = RiskCalculator(weight_config)
        matrix_processor = RiskMatrixProcessor(risk_matrix)
        
        # 删除旧的计算结果
        RiskCalculationResult.query.filter_by(project_id=project_id).delete()
        
        # 计算风险指数
        calculation_results = []
        
        for i, data in enumerate(normalized_data):
            # 计算风险指数
            calc_result = calculator.calculate_single_assessment(data)
            
            # 处理风险矩阵
            matrix_result = matrix_processor.process_risk_assessment(
                calc_result['likelihood_probability'],
                calc_result['impact_degree'],
                data.gdp_per_area
            )
            
            # 创建计算结果记录
            result = RiskCalculationResult(
                data_id=assessment_data_list[i].id,  # 使用原始数据的ID
                project_id=project_id,
                likelihood_probability=calc_result.get('likelihood_probability'),
                impact_degree=calc_result.get('impact_degree'),
                risk_index=calc_result['risk_index'],
                likelihood_level=matrix_result['likelihood_level'],
                impact_level=matrix_result['impact_level'],
                final_risk_level=matrix_result['final_risk_level'],
                weight_config_snapshot=json.dumps(calc_result['weight_config_snapshot'], ensure_ascii=False),
                calculated_by=user_id
            )
            
            db.session.add(result)
            calculation_results.append(result)
        
        # 更新项目状态
        project.status = 'completed'
        project.completed_at = datetime.now()
        
        db.session.commit()
        
        return calculation_results
    
    def get_project_results(self, project_id: int) -> Dict:
        """
        获取项目的计算结果统计

        Args:
            project_id: 项目ID

        Returns:
            结果统计字典
        """
        # 查询结果时包含关联的评估数据
        results = db.session.query(RiskCalculationResult).join(
            RiskAssessmentData, RiskCalculationResult.data_id == RiskAssessmentData.id
        ).filter(RiskCalculationResult.project_id == project_id).all()
        
        if not results:
            return {
                'total_count': 0,
                'risk_level_distribution': {},
                'average_scores': {},
                'results': []
            }
        
        # 统计风险等级分布
        risk_level_distribution = {}
        for result in results:
            level = result.final_risk_level
            risk_level_distribution[level] = risk_level_distribution.get(level, 0) + 1
        
        # 计算平均得分
        total_count = len(results)
        average_scores = {
            'likelihood_probability': sum(r.likelihood_probability or 0 for r in results) / total_count,
            'impact_degree': sum(r.impact_degree or 0 for r in results) / total_count,
            'risk_index': sum(r.risk_index or 0 for r in results) / total_count
        }
        
        return {
            'total_count': total_count,
            'risk_level_distribution': risk_level_distribution,
            'average_scores': average_scores,
            'results': [result.to_dict() for result in results]
        }
    
    def update_weight_config(self, config_id: int, weight_data: Dict) -> RiskWeightConfig:
        """
        更新权重配置
        
        Args:
            config_id: 配置ID
            weight_data: 权重数据
            
        Returns:
            更新后的配置对象
        """
        config = RiskWeightConfig.query.get(config_id)
        if not config:
            raise ValueError(f"权重配置 {config_id} 不存在")
        
        # 更新权重值
        for key, value in weight_data.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        # 验证权重配置
        is_valid, error_msg = config.validate_weights()
        if not is_valid:
            raise ValueError(f"权重配置无效: {error_msg}")
        
        db.session.commit()
        return config
    
    def create_custom_weight_config(self, config_name: str, weight_data: Dict, 
                                  user_id: int = None) -> RiskWeightConfig:
        """
        创建自定义权重配置
        
        Args:
            config_name: 配置名称
            weight_data: 权重数据
            user_id: 创建用户ID
            
        Returns:
            创建的配置对象
        """
        config = RiskWeightConfig(
            config_name=config_name,
            created_by=user_id,
            is_default=False,
            is_active=True
        )
        
        # 设置权重值
        for key, value in weight_data.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        # 验证权重配置
        is_valid, error_msg = config.validate_weights()
        if not is_valid:
            raise ValueError(f"权重配置无效: {error_msg}")
        
        db.session.add(config)
        db.session.commit()
        
        return config
