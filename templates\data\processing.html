{% extends "base.html" %}

{% block title %}数据处理 - 城市内涝风险评估系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-cogs me-2"></i>数据处理
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">仪表板</a></li>
                <li class="breadcrumb-item active">数据处理</li>
            </ol>
        </nav>
    </div>

    <!-- 处理功能说明 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>数据处理功能</h5>
                <ul class="mb-0">
                    <li><strong>数据清洗</strong>：去除重复记录、修复几何错误、处理缺失值</li>
                    <li><strong>投影变换</strong>：统一坐标系，支持常用投影系统转换</li>
                    <li><strong>数据预处理</strong>：数据标准化、属性字段统一、格式转换</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 数据集选择 -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-database me-2"></i>选择数据集
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="datasetSelect" class="form-label">数据集列表</label>
                        <select class="form-select" id="datasetSelect" onchange="loadDatasetInfo()">
                            <option value="">请选择数据集...</option>
                        </select>
                    </div>
                    
                    <!-- 数据集信息 -->
                    <div id="datasetInfo" style="display: none;">
                        <div class="border rounded p-3 bg-light">
                            <h6 class="mb-2">数据集信息</h6>
                            <div class="row g-2">
                                <div class="col-6">
                                    <small class="text-muted">名称：</small>
                                    <div id="datasetName" class="fw-bold"></div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">类型：</small>
                                    <div id="datasetType" class="fw-bold"></div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">大小：</small>
                                    <div id="datasetSize" class="fw-bold"></div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">状态：</small>
                                    <div id="datasetStatus" class="fw-bold"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 处理操作 -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tools me-2"></i>处理操作
                    </h6>
                </div>
                <div class="card-body">
                    <!-- 处理选项卡 -->
                    <ul class="nav nav-tabs" id="processingTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="clean-tab" data-bs-toggle="tab" 
                                    data-bs-target="#clean" type="button" role="tab">
                                <i class="fas fa-broom me-1"></i>数据清洗
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="transform-tab" data-bs-toggle="tab" 
                                    data-bs-target="#transform" type="button" role="tab">
                                <i class="fas fa-exchange-alt me-1"></i>投影变换
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="preprocess-tab" data-bs-toggle="tab" 
                                    data-bs-target="#preprocess" type="button" role="tab">
                                <i class="fas fa-filter me-1"></i>数据预处理
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content mt-3" id="processingTabContent">
                        <!-- 数据清洗 -->
                        <div class="tab-pane fade show active" id="clean" role="tabpanel">
                            <div class="mb-3">
                                <h6>清洗选项</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="removeDuplicates" checked>
                                    <label class="form-check-label" for="removeDuplicates">
                                        去除重复记录
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="fixGeometry" checked>
                                    <label class="form-check-label" for="fixGeometry">
                                        修复几何错误
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="handleMissing" checked>
                                    <label class="form-check-label" for="handleMissing">
                                        处理缺失值
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="validateData" checked>
                                    <label class="form-check-label" for="validateData">
                                        数据验证
                                    </label>
                                </div>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="startCleaning()">
                                <i class="fas fa-play me-1"></i>开始清洗
                            </button>
                        </div>

                        <!-- 投影变换 -->
                        <div class="tab-pane fade" id="transform" role="tabpanel">
                            <div class="mb-3">
                                <label for="targetCRS" class="form-label">目标坐标系</label>
                                <select class="form-select" id="targetCRS">
                                    <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                    <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                    <option value="EPSG:4490">中国2000 (EPSG:4490)</option>
                                    <option value="EPSG:4214">北京54 (EPSG:4214)</option>
                                    <option value="EPSG:4610">西安80 (EPSG:4610)</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">
                                    当前坐标系：<span id="currentCRS" class="fw-bold">未知</span>
                                </small>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="startTransform()">
                                <i class="fas fa-play me-1"></i>开始变换
                            </button>
                        </div>

                        <!-- 数据预处理 -->
                        <div class="tab-pane fade" id="preprocess" role="tabpanel">
                            <div class="mb-3">
                                <h6>预处理选项</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="standardize">
                                    <label class="form-check-label" for="standardize">
                                        数据标准化
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="normalizeAttributes">
                                    <label class="form-check-label" for="normalizeAttributes">
                                        属性字段标准化
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="formatConversion">
                                    <label class="form-check-label" for="formatConversion">
                                        数据格式转换
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="qualityCheck">
                                    <label class="form-check-label" for="qualityCheck">
                                        数据质量检查
                                    </label>
                                </div>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="startPreprocess()">
                                <i class="fas fa-play me-1"></i>开始预处理
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 处理进度和日志 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list-alt me-2"></i>处理进度和日志
                    </h6>
                </div>
                <div class="card-body">
                    <!-- 处理进度 -->
                    <div id="processingProgress" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">处理进度</h6>
                            <span id="processingStatus" class="badge bg-info">准备中...</span>
                        </div>
                        <div class="progress mb-3">
                            <div id="processingProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>

                    <!-- 处理日志 -->
                    <div id="processingLog">
                        <h6>处理日志</h6>
                        <div id="logContent" class="border rounded p-3 bg-light" style="height: 200px; overflow-y: auto;">
                            <small class="text-muted">暂无处理记录</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    loadDatasets();
});

// 加载数据集列表
function loadDatasets() {
    fetch('/api/datasets')
        .then(response => response.json())
        .then(data => {
            console.log('API响应:', data);

            const select = document.getElementById('datasetSelect');
            select.innerHTML = '<option value="">请选择数据集...</option>';

            // 检查响应格式
            const datasets = data.success ? data.data : data;

            if (Array.isArray(datasets)) {
                datasets.forEach(dataset => {
                    const option = document.createElement('option');
                    option.value = dataset.id;
                    option.textContent = `${dataset.name} (${getTypeLabel(dataset.data_type)})`;
                    select.appendChild(option);
                });

                if (datasets.length === 0) {
                    const option = document.createElement('option');
                    option.value = '';
                    option.textContent = '暂无可用数据集';
                    option.disabled = true;
                    select.appendChild(option);
                }
            } else {
                console.error('数据格式错误:', datasets);
                const option = document.createElement('option');
                option.value = '';
                option.textContent = '加载数据集失败';
                option.disabled = true;
                select.appendChild(option);
            }
        })
        .catch(error => {
            console.error('加载数据集失败:', error);
            const select = document.getElementById('datasetSelect');
            select.innerHTML = '<option value="">加载失败，请刷新重试</option>';
        });
}

// 加载数据集信息
function loadDatasetInfo() {
    const datasetId = document.getElementById('datasetSelect').value;
    const infoDiv = document.getElementById('datasetInfo');
    
    if (!datasetId) {
        infoDiv.style.display = 'none';
        return;
    }
    
    fetch(`/api/datasets/${datasetId}/info`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const dataset = data.data;
                document.getElementById('datasetName').textContent = dataset.name;
                document.getElementById('datasetType').textContent = dataset.data_type;
                document.getElementById('datasetSize').textContent = formatFileSize(dataset.file_size);
                document.getElementById('datasetStatus').textContent = dataset.processing_status || '未处理';
                document.getElementById('currentCRS').textContent = dataset.coordinate_system || '未知';
                
                infoDiv.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('加载数据集信息失败:', error);
        });
}

// 开始数据清洗
function startCleaning() {
    const datasetId = document.getElementById('datasetSelect').value;
    if (!datasetId) {
        alert('请先选择数据集');
        return;
    }

    // 获取清洗选项
    const options = {
        remove_duplicates: document.getElementById('removeDuplicates').checked,
        fix_geometry: document.getElementById('fixGeometry').checked,
        handle_missing_values: document.getElementById('handleMissing').checked,
        quality_assessment: document.getElementById('validateData').checked,
        missing_strategy: 'interpolate'
    };

    showProcessingProgress('数据清洗中...');

    fetch(`/api/datasets/${datasetId}/clean`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            options: options
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showProcessingResult('数据清洗完成', data.log, data.results);
        } else {
            showProcessingError('数据清洗失败', data.error);
        }
    })
    .catch(error => {
        showProcessingError('数据清洗失败', error.message);
    });
}

// 开始投影变换
function startTransform() {
    const datasetId = document.getElementById('datasetSelect').value;
    const targetCRS = document.getElementById('targetCRS').value;
    
    if (!datasetId) {
        alert('请先选择数据集');
        return;
    }
    
    showProcessingProgress('投影变换中...');
    
    fetch(`/api/datasets/${datasetId}/transform`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            target_crs: targetCRS
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showProcessingResult('投影变换完成', data.log);
            loadDatasetInfo(); // 刷新数据集信息
        } else {
            showProcessingError('投影变换失败', data.error);
        }
    })
    .catch(error => {
        showProcessingError('投影变换失败', error.message);
    });
}

// 开始数据预处理
function startPreprocess() {
    const datasetId = document.getElementById('datasetSelect').value;
    if (!datasetId) {
        alert('请先选择数据集');
        return;
    }
    
    const options = {
        standardize: document.getElementById('standardize').checked,
        normalize_attributes: document.getElementById('normalizeAttributes').checked,
        format_conversion: document.getElementById('formatConversion').checked,
        quality_check: document.getElementById('qualityCheck').checked
    };
    
    showProcessingProgress('数据预处理中...');
    
    fetch(`/api/datasets/${datasetId}/preprocess`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            options: options
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showProcessingResult('数据预处理完成', data.log);
        } else {
            showProcessingError('数据预处理失败', data.error);
        }
    })
    .catch(error => {
        showProcessingError('数据预处理失败', error.message);
    });
}

// 显示处理进度
function showProcessingProgress(message) {
    const progressDiv = document.getElementById('processingProgress');
    const statusSpan = document.getElementById('processingStatus');
    const progressBar = document.getElementById('processingProgressBar');
    
    progressDiv.style.display = 'block';
    statusSpan.textContent = message;
    statusSpan.className = 'badge bg-warning';
    progressBar.style.width = '50%';
    
    addLogEntry(message, 'info');
}

// 显示处理结果
function showProcessingResult(message, logs, results) {
    const statusSpan = document.getElementById('processingStatus');
    const progressBar = document.getElementById('processingProgressBar');

    statusSpan.textContent = message;
    statusSpan.className = 'badge bg-success';
    progressBar.style.width = '100%';

    addLogEntry(message, 'success');

    // 显示处理结果详情
    if (results) {
        if (results.duplicates_removed) {
            addLogEntry(`去除重复记录: ${results.duplicates_removed} 条`, 'info');
        }
        if (results.geometry_fixed) {
            addLogEntry(`修复几何错误: ${results.geometry_fixed} 个`, 'info');
        }
        if (results.missing_values_handled) {
            addLogEntry(`处理缺失值: ${results.missing_values_handled} 个`, 'info');
        }
        if (results.quality_issues && results.quality_issues.length > 0) {
            addLogEntry(`发现质量问题: ${results.quality_issues.length} 个`, 'warning');
        }
    }

    if (logs && Array.isArray(logs)) {
        logs.forEach(log => addLogEntry(log, 'info'));
    }
}

// 显示处理错误
function showProcessingError(message, error) {
    const statusSpan = document.getElementById('processingStatus');
    const progressBar = document.getElementById('processingProgressBar');
    
    statusSpan.textContent = message;
    statusSpan.className = 'badge bg-danger';
    progressBar.style.width = '100%';
    progressBar.className = 'progress-bar bg-danger';
    
    addLogEntry(`${message}: ${error}`, 'error');
}

// 添加日志条目
function addLogEntry(message, type) {
    const logContent = document.getElementById('logContent');
    const timestamp = new Date().toLocaleTimeString();
    
    const entry = document.createElement('div');
    entry.className = 'mb-1';
    
    let icon = 'fas fa-info-circle';
    let color = 'text-info';
    
    if (type === 'success') {
        icon = 'fas fa-check-circle';
        color = 'text-success';
    } else if (type === 'error') {
        icon = 'fas fa-exclamation-circle';
        color = 'text-danger';
    }
    
    entry.innerHTML = `
        <small class="${color}">
            <i class="${icon} me-1"></i>
            [${timestamp}] ${message}
        </small>
    `;
    
    logContent.appendChild(entry);
    logContent.scrollTop = logContent.scrollHeight;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取数据类型标签
function getTypeLabel(type) {
    const labels = {
        'land_use': '土地利用',
        'rivers': '河流水系',
        'drainage': '排水系统',
        'raster': '栅格数据',
        'dem': '数字高程模型',
        'vector': '矢量数据',
        'geojson': 'GeoJSON数据',
        'unknown': '未知类型'
    };
    return labels[type] || type;
}
</script>
{% endblock %}
