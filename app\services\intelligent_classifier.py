"""
智能数据分类服务
自动识别栅格数据的省份、类型和其他属性
"""
import os
import re
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

# 创建logger实例
logger = logging.getLogger(__name__)

# GIS库导入
try:
    import rasterio
    import numpy as np
    from rasterio.features import shapes
    from rasterio.warp import transform_bounds
    from shapely.geometry import box, Point
    import geopandas as gpd
    GIS_LIBS_AVAILABLE = True
    logger.info("智能分类器: GIS库加载成功")
except ImportError as e:
    GIS_LIBS_AVAILABLE = False
    logger.warning(f"智能分类器: GIS库不可用: {e}")
    # 创建虚拟模块以避免NameError
    rasterio = None
    np = None


class IntelligentClassifier:
    """智能数据分类器"""
    
    # 省份边界框（使用统一配置）
    @classmethod
    def _get_province_bounds(cls):
        """获取省份边界数据"""
        try:
            from app.config import PROVINCES_CONFIG
            bounds = {}
            for province_name, config in PROVINCES_CONFIG.items():
                # 使用短名称作为键，保持兼容性
                short_name = config['short_name']
                bounds[short_name] = config['bounds']
                # 同时支持完整名称
                bounds[province_name] = config['bounds']
            return bounds
        except ImportError:
            # 如果导入失败，使用原有的简化边界数据作为备用
            return {
                '河南': {'min_lon': 110.21, 'max_lon': 116.39, 'min_lat': 31.23, 'max_lat': 36.22},
                '湖北': {'min_lon': 108.21, 'max_lon': 116.07, 'min_lat': 29.01, 'max_lat': 33.20},
                '福建': {'min_lon': 115.50, 'max_lon': 120.40, 'min_lat': 23.50, 'max_lat': 28.20},
                '广东': {'min_lon': 109.39, 'max_lon': 117.19, 'min_lat': 20.13, 'max_lat': 25.31},
                '江苏': {'min_lon': 116.18, 'max_lon': 121.57, 'min_lat': 30.45, 'max_lat': 35.20},
                '浙江': {'min_lon': 118.01, 'max_lon': 123.10, 'min_lat': 27.02, 'max_lat': 31.11},
                '山东': {'min_lon': 114.47, 'max_lon': 122.43, 'min_lat': 34.22, 'max_lat': 38.24},
                '四川': {'min_lon': 97.21, 'max_lon': 108.12, 'min_lat': 26.03, 'max_lat': 34.19},
                '湖南': {'min_lon': 108.47, 'max_lon': 114.15, 'min_lat': 24.38, 'max_lat': 30.08},
                '安徽': {'min_lon': 114.54, 'max_lon': 119.37, 'min_lat': 29.41, 'max_lat': 34.38},
                '江西': {'min_lon': 113.34, 'max_lon': 118.28, 'min_lat': 24.29, 'max_lat': 30.04},
                '北京': {'min_lon': 115.25, 'max_lon': 117.30, 'min_lat': 39.26, 'max_lat': 41.03},
                '上海': {'min_lon': 120.52, 'max_lon': 122.12, 'min_lat': 30.40, 'max_lat': 31.53},
                '天津': {'min_lon': 116.43, 'max_lon': 118.04, 'min_lat': 38.34, 'max_lat': 40.15},
                '重庆': {'min_lon': 105.11, 'max_lon': 110.11, 'min_lat': 28.10, 'max_lat': 32.13}
            }

    # 动态获取省份边界
    @property
    def PROVINCE_BOUNDS(self):
        if not hasattr(self, '_province_bounds'):
            self._province_bounds = self._get_province_bounds()
        return self._province_bounds
    
    # 数据类型特征模式
    DATA_TYPE_PATTERNS = {
        'landuse': {
            'filename_keywords': [
                'clcd', 'landuse', 'land_use', 'landcover', 'land_cover',
                '土地利用', '土地覆盖', '地类', '用地', 'lulc'
            ],
            'value_range': (0, 50),  # 土地利用类型通常是0-20之间的整数
            'typical_values': [1, 2, 3, 4, 5, 6, 10, 11, 12, 20, 21, 22],
            'description': '土地利用/覆盖数据'
        },
        'administrative': {
            'filename_keywords': [
                'admin', 'administrative', 'boundary', 'border', 'district',
                'county', 'city', 'province', 'region', 'division',
                '行政区划', '行政边界', '区划', '边界', '行政区', '区县',
                '市县', '省市', '地级市', '县级市', '区域', '行政'
            ],
            'value_range': (0, 10000),  # 行政区划代码范围
            'typical_values': [100000, 110000, 120000, 130000, 140000, 150000],  # 省级代码示例
            'description': '行政区划数据'
        },
        'elevation': {
            'filename_keywords': [
                'dem', 'elevation', 'height', 'altitude', 'dtm', 'dsm',
                '高程', '海拔', '地形', '高度', 'elev'
            ],
            'value_range': (-500, 9000),  # 中国海拔范围
            'continuous': True,
            'description': '数字高程模型'
        },
        'precipitation': {
            'filename_keywords': [
                'precipitation', 'rainfall', 'rain', 'precip', 'prcp',
                '降水', '降雨', '雨量', '降水量'
            ],
            'value_range': (0, 3000),  # 年降水量范围(mm)
            'continuous': True,
            'description': '降水数据'
        },
        'temperature': {
            'filename_keywords': [
                'temperature', 'temp', 'celsius', 'kelvin',
                '温度', '气温', '温度场'
            ],
            'value_range': (-50, 50),  # 温度范围(°C)
            'continuous': True,
            'description': '温度数据'
        },
        'population': {
            'filename_keywords': [
                'population', 'pop', 'density', 'people',
                '人口', '人口密度', '人口分布'
            ],
            'value_range': (0, 50000),  # 人口密度范围
            'continuous': True,
            'description': '人口数据'
        },
        'ndvi': {
            'filename_keywords': [
                'ndvi', 'vegetation', 'greenness',
                '植被指数', '归一化植被指数'
            ],
            'value_range': (-1, 1),  # NDVI范围
            'continuous': True,
            'description': '植被指数'
        }
    }
    
    @classmethod
    def classify_raster_data(cls, file_path: str, filename: str) -> Dict[str, Any]:
        """
        智能分类栅格数据
        
        Args:
            file_path: 文件路径
            filename: 文件名
            
        Returns:
            分类结果字典
        """
        result = {
            'province': None,
            'data_type': 'other',
            'confidence': 0.0,
            'description': '',
            'metadata': {},
            'analysis_methods': []
        }
        
        try:
            # 1. 文件名分析
            filename_analysis = cls._analyze_filename(filename)
            result.update(filename_analysis)
            result['analysis_methods'].append('filename_analysis')
            
            if not GIS_LIBS_AVAILABLE:
                logger.warning("GIS库不可用，仅使用文件名分析")
                return result
            
            # 2. 地理边界分析
            boundary_analysis = cls._analyze_geographic_bounds(file_path)
            if boundary_analysis['province']:
                result['province'] = boundary_analysis['province']
                result['confidence'] = max(result['confidence'], boundary_analysis['confidence'])
                result['analysis_methods'].append('boundary_analysis')
            
            # 3. 数据内容分析
            content_analysis = cls._analyze_raster_content(file_path)
            if content_analysis['data_type'] != 'other':
                result['data_type'] = content_analysis['data_type']
                result['confidence'] = max(result['confidence'], content_analysis['confidence'])
                result['description'] = content_analysis['description']
                result['analysis_methods'].append('content_analysis')
            
            # 4. 合并元数据
            result['metadata'].update(boundary_analysis.get('metadata', {}))
            result['metadata'].update(content_analysis.get('metadata', {}))
            
        except Exception as e:
            logger.error(f"数据分类失败: {e}")
            result['error'] = str(e)
        
        return result
    
    @classmethod
    def _analyze_filename(cls, filename: str) -> Dict[str, Any]:
        """分析文件名获取分类信息"""
        result = {
            'province': None,
            'data_type': 'other',
            'confidence': 0.0,
            'description': '基于文件名的分析'
        }
        
        filename_lower = filename.lower()
        
        # 省份识别
        province_keywords = {
            '河南': ['henan', '河南', 'hn'],
            '湖北': ['hubei', '湖北', 'hb'],
            '福建': ['fujian', '福建', 'fj'],
            '广东': ['guangdong', '广东', 'gd'],
            '江苏': ['jiangsu', '江苏', 'js'],
            '浙江': ['zhejiang', '浙江', 'zj'],
            '山东': ['shandong', '山东', 'sd'],
            '四川': ['sichuan', '四川', 'sc'],
            '湖南': ['hunan', '湖南', 'hn2'],
            '安徽': ['anhui', '安徽', 'ah'],
            '江西': ['jiangxi', '江西', 'jx'],
            '北京': ['beijing', '北京', 'bj'],
            '上海': ['shanghai', '上海', 'sh'],
            '天津': ['tianjin', '天津', 'tj'],
            '重庆': ['chongqing', '重庆', 'cq']
        }
        
        for province, keywords in province_keywords.items():
            for keyword in keywords:
                if keyword in filename_lower:
                    result['province'] = province
                    result['confidence'] = max(result['confidence'], 0.7)
                    break
        
        # 数据类型识别
        for data_type, patterns in cls.DATA_TYPE_PATTERNS.items():
            for keyword in patterns['filename_keywords']:
                if keyword in filename_lower:
                    result['data_type'] = data_type
                    result['description'] = patterns['description']
                    result['confidence'] = max(result['confidence'], 0.8)
                    break
        
        return result
    
    @classmethod
    def _analyze_geographic_bounds(cls, file_path: str) -> Dict[str, Any]:
        """分析地理边界确定省份"""
        result = {
            'province': None,
            'confidence': 0.0,
            'metadata': {}
        }
        
        try:
            with rasterio.open(file_path) as src:
                bounds = src.bounds
                crs = src.crs
                
                # 转换到WGS84坐标系进行分析
                if crs and crs != 'EPSG:4326':
                    try:
                        bounds_wgs84 = transform_bounds(crs, 'EPSG:4326', *bounds)
                    except:
                        bounds_wgs84 = bounds
                else:
                    bounds_wgs84 = bounds
                
                result['metadata'] = {
                    'bounds': list(bounds),
                    'bounds_wgs84': list(bounds_wgs84),
                    'crs': str(crs) if crs else None
                }
                
                # 计算数据中心点
                center_lon = (bounds_wgs84[0] + bounds_wgs84[2]) / 2
                center_lat = (bounds_wgs84[1] + bounds_wgs84[3]) / 2
                
                # 与省份边界进行匹配
                best_match = None
                best_overlap = 0.0
                
                province_bounds = cls._get_province_bounds()
                for province, bounds_info in province_bounds.items():
                    # 计算重叠面积比例
                    overlap = cls._calculate_bounds_overlap(bounds_wgs84, bounds_info)
                    
                    if overlap > best_overlap:
                        best_overlap = overlap
                        best_match = province
                
                if best_match and best_overlap > 0.3:  # 至少30%重叠
                    result['province'] = best_match
                    result['confidence'] = min(best_overlap, 0.9)
                
        except Exception as e:
            logger.error(f"地理边界分析失败: {e}")
        
        return result

    @classmethod
    def _analyze_raster_content(cls, file_path: str) -> Dict[str, Any]:
        """分析栅格数据内容确定数据类型"""
        result = {
            'data_type': 'other',
            'confidence': 0.0,
            'description': '未知数据类型',
            'metadata': {}
        }

        try:
            with rasterio.open(file_path) as src:
                # 读取第一个波段的样本数据
                band1 = src.read(1, masked=True)

                # 计算基本统计信息
                valid_data = band1.compressed()  # 去除NoData值
                if len(valid_data) == 0:
                    return result

                stats = {
                    'min': float(np.min(valid_data)),
                    'max': float(np.max(valid_data)),
                    'mean': float(np.mean(valid_data)),
                    'std': float(np.std(valid_data)),
                    'unique_count': len(np.unique(valid_data)),
                    'data_type': str(band1.dtype)
                }

                result['metadata']['statistics'] = stats

                # 基于数值特征判断数据类型
                data_range = stats['max'] - stats['min']
                unique_ratio = stats['unique_count'] / len(valid_data) if len(valid_data) > 0 else 0

                best_match = None
                best_confidence = 0.0

                for data_type, patterns in cls.DATA_TYPE_PATTERNS.items():
                    confidence = cls._calculate_content_confidence(stats, patterns, unique_ratio)

                    if confidence > best_confidence:
                        best_confidence = confidence
                        best_match = data_type

                if best_match and best_confidence > 0.5:
                    result['data_type'] = best_match
                    result['confidence'] = best_confidence
                    result['description'] = cls.DATA_TYPE_PATTERNS[best_match]['description']

        except Exception as e:
            logger.error(f"栅格内容分析失败: {e}")

        return result

    @classmethod
    def _calculate_content_confidence(cls, stats: Dict, patterns: Dict, unique_ratio: float) -> float:
        """计算内容匹配的置信度"""
        confidence = 0.0

        # 检查数值范围
        if 'value_range' in patterns:
            min_val, max_val = patterns['value_range']
            if min_val <= stats['min'] and stats['max'] <= max_val:
                confidence += 0.4
            elif min_val <= stats['mean'] <= max_val:
                confidence += 0.2

        # 检查是否为连续数据
        if patterns.get('continuous', False):
            if unique_ratio > 0.1:  # 连续数据应该有较多唯一值
                confidence += 0.3
        else:
            if unique_ratio < 0.01:  # 分类数据应该有较少唯一值
                confidence += 0.3

        # 检查典型值
        if 'typical_values' in patterns:
            typical_values = set(patterns['typical_values'])
            data_values = set(np.unique(np.round(np.linspace(stats['min'], stats['max'], 20))))
            overlap = len(typical_values.intersection(data_values))
            if overlap > 0:
                confidence += 0.3 * (overlap / len(typical_values))

        return min(confidence, 1.0)

    @classmethod
    def _calculate_bounds_overlap(cls, bounds1: Tuple, bounds_dict: Dict) -> float:
        """计算两个边界框的重叠比例"""
        try:
            # bounds1: (min_lon, min_lat, max_lon, max_lat)
            # bounds_dict: {'min_lon': x, 'max_lon': x, 'min_lat': y, 'max_lat': y}

            bounds2 = (bounds_dict['min_lon'], bounds_dict['min_lat'],
                      bounds_dict['max_lon'], bounds_dict['max_lat'])

            # 计算重叠区域
            overlap_min_lon = max(bounds1[0], bounds2[0])
            overlap_min_lat = max(bounds1[1], bounds2[1])
            overlap_max_lon = min(bounds1[2], bounds2[2])
            overlap_max_lat = min(bounds1[3], bounds2[3])

            # 检查是否有重叠
            if overlap_min_lon >= overlap_max_lon or overlap_min_lat >= overlap_max_lat:
                return 0.0

            # 计算重叠面积
            overlap_area = (overlap_max_lon - overlap_min_lon) * (overlap_max_lat - overlap_min_lat)

            # 计算数据边界框面积
            data_area = (bounds1[2] - bounds1[0]) * (bounds1[3] - bounds1[1])

            # 返回重叠比例
            return overlap_area / data_area if data_area > 0 else 0.0

        except Exception as e:
            logger.error(f"边界重叠计算失败: {e}")
            return 0.0

    @classmethod
    def get_enhanced_classification_info(cls, file_path: str, filename: str) -> Dict[str, Any]:
        """获取增强的分类信息，包含详细的分析结果"""
        classification = cls.classify_raster_data(file_path, filename)

        # 添加建议的标签和描述
        enhanced_info = {
            'classification': classification,
            'suggested_tags': cls._generate_tags(classification),
            'suggested_description': cls._generate_description(classification, filename),
            'quality_score': cls._calculate_quality_score(classification),
            'recommendations': cls._generate_recommendations(classification)
        }

        return enhanced_info

    @classmethod
    def _generate_tags(cls, classification: Dict) -> List[str]:
        """生成建议的标签"""
        tags = []

        if classification.get('province'):
            tags.append(classification['province'])

        if classification.get('data_type') != 'other':
            tags.append(classification['data_type'])

        # 根据数据类型添加相关标签
        data_type = classification.get('data_type')
        if data_type == 'landuse':
            tags.extend(['土地利用', '遥感', '分类数据'])
        elif data_type == 'elevation':
            tags.extend(['地形', 'DEM', '高程'])
        elif data_type == 'precipitation':
            tags.extend(['气象', '降水', '气候'])
        elif data_type == 'temperature':
            tags.extend(['气象', '温度', '气候'])
        elif data_type == 'population':
            tags.extend(['人口', '社会经济', '密度'])
        elif data_type == 'ndvi':
            tags.extend(['植被', '遥感指数', '生态'])

        return list(set(tags))  # 去重

    @classmethod
    def _generate_description(cls, classification: Dict, filename: str) -> str:
        """生成建议的描述"""
        parts = []

        if classification.get('province'):
            parts.append(f"{classification['province']}省")

        if classification.get('description'):
            parts.append(classification['description'])

        if classification.get('metadata', {}).get('statistics'):
            stats = classification['metadata']['statistics']
            parts.append(f"数值范围: {stats['min']:.2f} - {stats['max']:.2f}")

        methods = classification.get('analysis_methods', [])
        if methods:
            method_names = {
                'filename_analysis': '文件名分析',
                'boundary_analysis': '地理边界分析',
                'content_analysis': '数据内容分析'
            }
            used_methods = [method_names.get(m, m) for m in methods]
            parts.append(f"分析方法: {', '.join(used_methods)}")

        if not parts:
            parts.append(f"栅格数据文件: {filename}")

        return " | ".join(parts)

    @classmethod
    def _calculate_quality_score(cls, classification: Dict) -> float:
        """计算分类质量评分"""
        score = 0.0

        # 基础分数
        score += 0.3

        # 省份识别加分
        if classification.get('province'):
            score += 0.3

        # 数据类型识别加分
        if classification.get('data_type') != 'other':
            score += 0.3

        # 置信度加分
        confidence = classification.get('confidence', 0.0)
        score += confidence * 0.1

        return min(score, 1.0)

    @classmethod
    def _generate_recommendations(cls, classification: Dict) -> List[str]:
        """生成改进建议"""
        recommendations = []

        if classification.get('confidence', 0.0) < 0.5:
            recommendations.append("建议检查文件名是否包含更多描述性信息")

        if not classification.get('province'):
            recommendations.append("建议在文件名中包含省份信息以便自动识别")

        if classification.get('data_type') == 'other':
            recommendations.append("建议在文件名中包含数据类型关键词（如DEM、土地利用等）")

        if not classification.get('analysis_methods'):
            recommendations.append("建议检查文件格式和数据完整性")

        return recommendations
