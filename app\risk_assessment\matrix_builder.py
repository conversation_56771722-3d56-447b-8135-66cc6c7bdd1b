"""
风险矩阵构建器
专门用于构建和管理5×5风险矩阵
"""
from typing import Dict, List, Tuple
import json
from app import db
from app.models import RiskMatrix


class RiskMatrixBuilder:
    """风险矩阵构建器"""
    
    @staticmethod
    def create_default_matrix() -> Dict:
        """
        创建默认的5×5风险矩阵（基于新的风险评估模型）

        Returns:
            默认矩阵配置字典
        """
        return {
            'A': {  # 极低概率 [0.00, 0.15)
                '轻微': '极低风险',
                '较小': '极低风险',
                '中等': '低风险',
                '严重': '低风险',
                '灾难性': '中风险'
            },
            'B': {  # 低概率 [0.15, 0.35)
                '轻微': '极低风险',
                '较小': '低风险',
                '中等': '低风险',
                '严重': '中风险',
                '灾难性': '高风险'
            },
            'C': {  # 中等概率 [0.35, 0.60)
                '轻微': '低风险',
                '较小': '中风险',
                '中等': '中风险',
                '严重': '高风险',
                '灾难性': '极高风险'
            },
            'D': {  # 高概率 [0.60, 0.85)
                '轻微': '中风险',
                '较小': '高风险',
                '中等': '高风险',
                '严重': '极高风险',
                '灾难性': '极高风险'
            },
            'E': {  # 极高概率 [0.85, 1.00]
                '轻微': '高风险',
                '较小': '极高风险',
                '中等': '极高风险',
                '严重': '极高风险',
                '灾难性': '极高风险'
            }
        }
    
    @staticmethod
    def create_likelihood_levels() -> Dict:
        """
        创建发生概率等级定义（5级分类）

        Returns:
            概率等级定义字典
        """
        return {
            'A': {
                'name': '极低',
                'description': '极不可能发生',
                'range': '0.00-0.15',
                'color': '#d4edda'
            },
            'B': {
                'name': '低',
                'description': '不太可能发生',
                'range': '0.15-0.35',
                'color': '#28a745'
            },
            'C': {
                'name': '中等',
                'description': '可能发生',
                'range': '0.35-0.60',
                'color': '#ffc107'
            },
            'D': {
                'name': '高',
                'description': '很可能发生',
                'range': '0.60-0.85',
                'color': '#fd7e14'
            },
            'E': {
                'name': '极高',
                'description': '极可能发生',
                'range': '0.85-1.00',
                'color': '#dc3545'
            }
        }
    
    @staticmethod
    def create_impact_levels() -> Dict:
        """
        创建影响程度等级定义（5级分类）

        Returns:
            影响程度等级定义字典
        """
        return {
            '轻微': {
                'description': '影响轻微',
                'range': '0.00-0.10',
                'color': '#d4edda'
            },
            '较小': {
                'description': '影响较小',
                'range': '0.10-0.30',
                'color': '#28a745'
            },
            '中等': {
                'description': '影响中等',
                'range': '0.30-0.50',
                'color': '#ffc107'
            },
            '严重': {
                'description': '影响严重',
                'range': '0.50-0.75',
                'color': '#fd7e14'
            },
            '灾难性': {
                'description': '影响灾难性',
                'range': '0.75-1.00',
                'color': '#dc3545'
            }
        }

    @staticmethod
    def get_default_likelihood_levels() -> Dict:
        """
        获取默认的发生概率等级定义

        Returns:
            默认概率等级定义字典
        """
        return {
            'A': {'name': '极低', 'description': '极不可能发生', 'range': '0.00-0.15'},
            'B': {'name': '低', 'description': '不太可能发生', 'range': '0.15-0.35'},
            'C': {'name': '中等', 'description': '可能发生', 'range': '0.35-0.60'},
            'D': {'name': '高', 'description': '很可能发生', 'range': '0.60-0.85'},
            'E': {'name': '极高', 'description': '极可能发生', 'range': '0.85-1.00'}
        }

    @staticmethod
    def get_default_impact_levels() -> Dict:
        """
        获取默认的影响程度等级定义

        Returns:
            默认影响程度等级定义字典
        """
        return {
            '轻微': {'name': '轻微', 'description': '局部积水，影响有限'},
            '较小': {'name': '较小', 'description': '小范围积水，简单提示'},
            '中等': {'name': '中等', 'description': '区域积水，交通受阻'},
            '严重': {'name': '严重', 'description': '大面积积水，财产损失'},
            '灾难性': {'name': '灾难性', 'description': '重大人员伤亡，系统瘫痪'}
        }

    @staticmethod
    def get_risk_level_styles() -> Dict:
        """
        获取风险等级的样式配置
        
        Returns:
            风险等级样式字典
        """
        return {
            '极低风险': {
                'color': '#155724',
                'background': '#f0f9f0',
                'border': '#c3e6cb',
                'badge_class': 'light'
            },
            '低风险': {
                'color': '#28a745',
                'background': '#d4edda',
                'border': '#c3e6cb',
                'badge_class': 'success'
            },
            '中风险': {
                'color': '#856404',
                'background': '#fff3cd',
                'border': '#ffeaa7',
                'badge_class': 'warning'
            },
            '高风险': {
                'color': '#721c24',
                'background': '#f8d7da',
                'border': '#f5c6cb',
                'badge_class': 'danger'
            },
            '极高风险': {
                'color': '#ffffff',
                'background': '#dc3545',
                'border': '#dc3545',
                'badge_class': 'danger'
            }
        }
    
    @staticmethod
    def build_matrix_html_table(matrix_config: Dict, likelihood_levels: Dict, 
                               impact_levels: Dict) -> str:
        """
        构建风险矩阵的HTML表格
        
        Args:
            matrix_config: 矩阵配置
            likelihood_levels: 概率等级定义
            impact_levels: 影响程度等级定义
            
        Returns:
            HTML表格字符串
        """
        risk_styles = RiskMatrixBuilder.get_risk_level_styles()
        
        # 表头
        html = '<table class="table table-bordered risk-matrix-table">\n'
        html += '<thead>\n<tr>\n'
        html += '<th class="matrix-header">发生概率\\影响程度</th>\n'
        
        # 影响程度列标题
        impact_order = ['轻微', '较小', '中等', '严重', '灾难性']
        for impact in impact_order:
            html += f'<th class="matrix-header text-center">{impact}</th>\n'
        html += '</tr>\n</thead>\n<tbody>\n'

        # 表格内容
        likelihood_order = ['A', 'B', 'C', 'D', 'E']
        for likelihood in likelihood_order:
            likelihood_name = likelihood_levels.get(likelihood, {}).get('name', likelihood)
            html += f'<tr>\n<td class="matrix-row-header"><strong>{likelihood_name}（{likelihood}）</strong></td>\n'
            
            for impact in impact_order:
                risk_level = matrix_config.get(likelihood, {}).get(impact, '中风险')
                style = risk_styles.get(risk_level, risk_styles['中风险'])
                
                html += f'''<td class="matrix-cell text-center" 
                           style="background-color: {style['background']}; 
                                  border-color: {style['border']}; 
                                  color: {style['color']};">
                           <strong>{risk_level}</strong>
                           </td>\n'''
            
            html += '</tr>\n'
        
        html += '</tbody>\n</table>\n'
        return html
    
    @staticmethod
    def build_matrix_json_for_frontend(matrix_config: Dict, likelihood_levels: Dict,
                                     impact_levels: Dict) -> Dict:
        """
        构建用于前端显示的矩阵JSON数据

        Args:
            matrix_config: 矩阵配置
            likelihood_levels: 概率等级定义
            impact_levels: 影响程度等级定义

        Returns:
            前端矩阵数据字典
        """
        # 防护性检查：确保参数是字典类型
        if not isinstance(matrix_config, dict):
            print(f"警告：matrix_config 不是字典类型，而是 {type(matrix_config)}")
            matrix_config = RiskMatrixBuilder.create_default_matrix()

        if not isinstance(likelihood_levels, dict):
            print(f"警告：likelihood_levels 不是字典类型，而是 {type(likelihood_levels)}")
            likelihood_levels = RiskMatrixBuilder.get_default_likelihood_levels()

        if not isinstance(impact_levels, dict):
            print(f"警告：impact_levels 不是字典类型，而是 {type(impact_levels)}")
            impact_levels = RiskMatrixBuilder.get_default_impact_levels()

        risk_styles = RiskMatrixBuilder.get_risk_level_styles()

        # 构建矩阵数据
        matrix_data = []
        likelihood_order = ['A', 'B', 'C', 'D', 'E']
        impact_order = ['轻微', '较小', '中等', '严重', '灾难性']

        for likelihood in likelihood_order:
            row_data = {
                'likelihood_code': likelihood,
                'likelihood_name': likelihood_levels.get(likelihood, {}).get('name', likelihood),
                'likelihood_description': likelihood_levels.get(likelihood, {}).get('description', ''),
                'cells': []
            }

            for impact in impact_order:
                risk_level = matrix_config.get(likelihood, {}).get(impact, '中风险')
                style = risk_styles.get(risk_level, risk_styles['中风险'])

                cell_data = {
                    'impact_level': impact,
                    'risk_level': risk_level,
                    'style': style
                }
                row_data['cells'].append(cell_data)

            matrix_data.append(row_data)
        
        return {
            'matrix_data': matrix_data,
            'impact_headers': [
                {
                    'level': impact,
                    'description': impact_levels.get(impact, {}).get('description', impact)
                }
                for impact in impact_order
            ],
            'risk_level_styles': risk_styles,
            'likelihood_levels': likelihood_levels,
            'impact_levels': impact_levels
        }
    
    @staticmethod
    def validate_matrix_structure(matrix_config: Dict) -> Tuple[bool, str]:
        """
        验证矩阵结构是否完整
        
        Args:
            matrix_config: 矩阵配置
            
        Returns:
            (是否有效, 错误信息)
        """
        required_likelihood = ['A', 'B', 'C', 'D']
        required_impact = ['轻微', '中等', '严重', '灾难']
        valid_risk_levels = ['低风险', '中风险', '高风险', '极高风险']
        
        if not isinstance(matrix_config, dict):
            return False, "矩阵配置必须是字典类型"
        
        # 检查概率等级
        for likelihood in required_likelihood:
            if likelihood not in matrix_config:
                return False, f"缺少发生概率等级: {likelihood}"
            
            if not isinstance(matrix_config[likelihood], dict):
                return False, f"概率等级 {likelihood} 的配置必须是字典类型"
            
            # 检查影响程度等级
            for impact in required_impact:
                if impact not in matrix_config[likelihood]:
                    return False, f"缺少影响程度等级: {likelihood}-{impact}"
                
                risk_level = matrix_config[likelihood][impact]
                if risk_level not in valid_risk_levels:
                    return False, f"无效的风险等级: {risk_level}，应为 {valid_risk_levels} 之一"
        
        return True, "矩阵结构有效"
    
    @staticmethod
    def create_matrix_statistics(matrix_config: Dict) -> Dict:
        """
        创建矩阵统计信息
        
        Args:
            matrix_config: 矩阵配置
            
        Returns:
            统计信息字典
        """
        risk_level_count = {}
        total_cells = 0
        
        for likelihood_data in matrix_config.values():
            for risk_level in likelihood_data.values():
                risk_level_count[risk_level] = risk_level_count.get(risk_level, 0) + 1
                total_cells += 1
        
        # 计算百分比
        risk_level_percentage = {}
        for risk_level, count in risk_level_count.items():
            risk_level_percentage[risk_level] = round((count / total_cells) * 100, 1)
        
        return {
            'total_cells': total_cells,
            'risk_level_count': risk_level_count,
            'risk_level_percentage': risk_level_percentage,
            'most_common_risk': max(risk_level_count.items(), key=lambda x: x[1])[0] if risk_level_count else None
        }
