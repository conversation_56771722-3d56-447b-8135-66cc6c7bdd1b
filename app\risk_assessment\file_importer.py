"""
文件导入工具
处理CSV和Excel文件的导入和解析
"""
import os
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import tempfile

# 尝试导入pandas，如果失败则设置标志
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
    DataFrame = pd.DataFrame
except ImportError:
    PANDAS_AVAILABLE = False
    pd = None
    # 创建一个虚拟的DataFrame类用于类型注解
    class DataFrame:
        pass


class FileImporter:
    """文件导入处理器"""
    
    # 支持的文件扩展名
    SUPPORTED_EXTENSIONS = {'.csv', '.xlsx', '.xls'}
    
    # 列名映射（支持省份级数据格式）
    COLUMN_MAPPING = {
        # 省份数据格式的列名 - 支持多种格式
        '排水管道密度（km/km²）': 'drainage_network_density',
        '排水管网密度（km/km²）': 'drainage_network_density',
        '排水管网密度': 'drainage_network_density',
        '全年降水量（mm）': 'annual_precipitation',
        '全年降水量': 'annual_precipitation',
        '平均高程（m）': 'elevation',
        '平均高程值': 'elevation',
        '平均高程': 'elevation',
        '人口密度（万人/km²）': 'population_density',
        '人口密度': 'population_density',
        'GDP密度（亿元/km²）': 'gdp_per_area',
        'GDP密度': 'gdp_per_area',

        # 第一列的可能名称（城市名称列）
        'Unnamed: 0': 'location_name',  # pandas自动生成的列名
        '城市': 'location_name',
        '城市名称': 'location_name',
        '地区': 'location_name',
        '地区名称': 'location_name',  # 新增通用地区名称
        '区域': 'location_name',
        '市': 'location_name',
        '地级市': 'location_name',
        '省份': 'location_name',
        '河南省': 'location_name',  
        '湖北省': 'location_name',  
        '福建省': 'location_name', 
        '数据名称': 'data_name',
        '位置名称': 'location_name',

        # 动态生成的列名映射
        '数据列1': 'annual_precipitation',
        '数据列2': 'drainage_network_density',
        '数据列3': 'elevation',
        '数据列4': 'population_density',
        '数据列5': 'gdp_per_area'
    }
    
    # 必需的数值字段（新版本）
    REQUIRED_NUMERIC_FIELDS = [
        'annual_precipitation', 'drainage_network_density', 'elevation',
        'population_density', 'gdp_per_area'
    ]
    
    def __init__(self):
        """初始化文件导入器"""
        pass
    
    def validate_file(self, file_path: str) -> Tuple[bool, str]:
        """
        验证文件是否有效
        
        Args:
            file_path: 文件路径
            
        Returns:
            (是否有效, 错误信息)
        """
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return False, "文件不存在"
        
        # 检查文件扩展名
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in self.SUPPORTED_EXTENSIONS:
            return False, f"不支持的文件格式，请使用 {', '.join(self.SUPPORTED_EXTENSIONS)} 格式"
        
        # 检查文件大小（限制为10MB）
        file_size = os.path.getsize(file_path)
        if file_size > 10 * 1024 * 1024:
            return False, "文件大小不能超过10MB"
        
        return True, "文件有效"
    
    def read_file(self, file_path: str, encoding: str = 'utf-8') -> Tuple[bool, DataFrame, str]:
        """
        读取文件数据

        Args:
            file_path: 文件路径
            encoding: 文件编码

        Returns:
            (是否成功, DataFrame, 错误信息)
        """
        if not PANDAS_AVAILABLE:
            return False, None, "Pandas库不可用，无法读取文件"

        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.csv':
                # 尝试不同的编码
                encodings = [encoding, 'utf-8', 'gbk', 'gb2312']
                df = None
                
                for enc in encodings:
                    try:
                        df = pd.read_csv(file_path, encoding=enc)
                        break
                    except UnicodeDecodeError:
                        continue
                
                if df is None:
                    return False, None, "无法读取CSV文件，请检查文件编码"
                    
            elif file_ext in ['.xlsx', '.xls']:
                try:
                    # 读取Excel文件，支持省份级数据格式
                    # 先尝试不使用header读取，然后智能判断数据结构
                    df_raw = pd.read_excel(file_path, engine='openpyxl' if file_ext == '.xlsx' else 'xlrd', header=None)

                    print(f"Excel原始数据形状: {df_raw.shape}")
                    if len(df_raw) > 0:
                        print(f"第一行数据: {df_raw.iloc[0].tolist()}")
                        if len(df_raw) > 1:
                            print(f"第二行数据: {df_raw.iloc[1].tolist()}")

                    # 智能判断数据结构
                    if len(df_raw) >= 2:
                        # 检查第一行是否像是列标题
                        first_row = df_raw.iloc[0]
                        second_row = df_raw.iloc[1]

                        # 更严格的判断：第一行必须全部是文字且不包含数字，第二行必须包含数字
                        first_row_all_text = all(isinstance(val, str) and not str(val).replace('.', '').replace('-', '').isdigit()
                                               for val in first_row if pd.notna(val))
                        second_row_has_numbers = any(pd.notna(val) and (isinstance(val, (int, float)) or
                                                   (isinstance(val, str) and str(val).replace('.', '').replace('-', '').isdigit()))
                                                   for val in second_row)

                        # 检查第一行是否包含已知的列标题关键词
                        first_row_has_keywords = any(str(val) in ['全年降水量', '排水管网密度', '平均高程值', '人口密度', 'GDP密度']
                                                   for val in first_row if pd.notna(val))

                        if first_row_all_text and second_row_has_numbers and first_row_has_keywords:
                            # 第一行是标题
                            df = pd.read_excel(file_path, engine='openpyxl' if file_ext == '.xlsx' else 'xlrd', header=0)
                            print("使用第一行作为列标题")
                        else:
                            # 没有标题行，手动设置列名
                            df = df_raw.copy()

                            # 检查并跳过标题行
                            rows_to_skip = 0

                            # 检查第一行是否是省份标题行（第一列有值，其他列为空）
                            if len(df) > 0:
                                first_row = df.iloc[0]
                                if (pd.notna(first_row.iloc[0]) and
                                    pd.isna(first_row.iloc[1:]).all() and
                                    str(first_row.iloc[0]) in ['河南省', '省份']):
                                    rows_to_skip += 1
                                    print("跳过第一行省份标题")

                            # 检查第二行是否是列标题行
                            if len(df) > rows_to_skip:
                                check_row = df.iloc[rows_to_skip]
                                # 如果这一行包含已知的列标题关键词，则跳过
                                row_text = ' '.join([str(val) for val in check_row if pd.notna(val)])
                                if any(keyword in row_text for keyword in ['排水管道密度', '全年降水量', '平均高程', '人口密度', 'GDP密度']):
                                    rows_to_skip += 1
                                    print("跳过列标题行")

                            # 应用跳过的行数
                            if rows_to_skip > 0:
                                df = df.iloc[rows_to_skip:].reset_index(drop=True)
                                print(f"总共跳过了 {rows_to_skip} 行")

                            # 根据列数设置标准列名
                            if len(df.columns) == 6:
                                df.columns = ['地区名称', '全年降水量', '排水管网密度', '平均高程值', '人口密度', 'GDP密度']
                                print("设置标准6列列名")
                            else:
                                # 动态设置列名
                                col_names = ['地区名称'] + [f'数据列{i}' for i in range(1, len(df.columns))]
                                df.columns = col_names
                                print(f"设置动态列名: {col_names}")
                    else:
                        # 数据行太少，直接读取
                        df = pd.read_excel(file_path, engine='openpyxl' if file_ext == '.xlsx' else 'xlrd')
                except ImportError as e:
                    if 'openpyxl' in str(e):
                        return False, None, "缺少openpyxl库，无法读取xlsx文件。请联系管理员安装相关依赖。"
                    elif 'xlrd' in str(e):
                        return False, None, "缺少xlrd库，无法读取xls文件。请联系管理员安装相关依赖。"
                    else:
                        return False, None, f"Excel文件读取失败: {str(e)}"
                except Exception as e:
                    return False, None, f"Excel文件读取失败: {str(e)}"
            else:
                return False, None, f"不支持的文件格式: {file_ext}。支持的格式: {', '.join(self.SUPPORTED_EXTENSIONS)}"
            
            # 检查是否为空
            if df.empty:
                return False, None, "文件为空"
            
            return True, df, "读取成功"
            
        except Exception as e:
            return False, None, f"读取文件失败: {str(e)}"
    
    def map_columns(self, df: DataFrame) -> Tuple[bool, DataFrame, str]:
        """
        映射列名到标准字段名

        Args:
            df: 原始DataFrame

        Returns:
            (是否成功, 映射后的DataFrame, 错误信息)
        """
        if not PANDAS_AVAILABLE:
            return False, None, "Pandas库不可用，无法进行列映射"

        try:
            # 创建列名映射
            column_mapping = {}
            unmapped_columns = []

            print(f"原始列名: {list(df.columns)}")

            for col in df.columns:
                col_stripped = str(col).strip()
                if col_stripped in self.COLUMN_MAPPING:
                    column_mapping[col] = self.COLUMN_MAPPING[col_stripped]
                    print(f"映射列: '{col}' -> '{self.COLUMN_MAPPING[col_stripped]}'")
                else:
                    unmapped_columns.append(col_stripped)

            # 特殊处理：第一列总是作为城市名称（location_name）
            if len(df.columns) > 0:
                first_col = df.columns[0]

                # 如果第一列还没有映射，将其映射为location_name
                if first_col not in column_mapping:
                    column_mapping[first_col] = 'location_name'
                    print(f"将第一列 '{first_col}' 映射为城市名称")

                # 如果第一列映射为其他字段，但我们需要location_name，则重新映射
                elif column_mapping[first_col] != 'location_name' and 'location_name' not in column_mapping.values():
                    column_mapping[first_col] = 'location_name'
                    print(f"重新将第一列 '{first_col}' 映射为城市名称")

            print(f"列映射结果: {column_mapping}")

            # 检查是否有必需的列
            mapped_fields = set(column_mapping.values())
            missing_fields = []

            for field in self.REQUIRED_NUMERIC_FIELDS:
                if field not in mapped_fields:
                    missing_fields.append(field)

            if missing_fields:
                field_names = [self.get_field_chinese_name(field) for field in missing_fields]
                print(f"缺少必需字段: {missing_fields}")
                print(f"已映射字段: {mapped_fields}")
                print(f"未映射列: {unmapped_columns}")
                return False, None, f"文件缺少必需列: {', '.join(field_names)}"

            # 重命名列
            df_mapped = df.rename(columns=column_mapping)

            # 只保留映射的列
            valid_columns = list(column_mapping.values())
            df_mapped = df_mapped[valid_columns]

            # 如果没有data_name列，使用location_name作为data_name
            if 'data_name' not in df_mapped.columns and 'location_name' in df_mapped.columns:
                df_mapped['data_name'] = df_mapped['location_name']

            # 数据清理：处理数值列中的格式问题
            df_mapped = self.clean_numeric_data(df_mapped)

            print(f"最终列名: {list(df_mapped.columns)}")
            print(f"数据行数: {len(df_mapped)}")

            return True, df_mapped, "列映射成功"

        except Exception as e:
            return False, None, f"列映射失败: {str(e)}"

    def clean_numeric_data(self, df: DataFrame) -> DataFrame:
        """
        清理数值数据中的格式问题

        Args:
            df: 需要清理的DataFrame

        Returns:
            清理后的DataFrame
        """
        if not PANDAS_AVAILABLE:
            return df

        df_cleaned = df.copy()

        # 对数值字段进行清理
        for field in self.REQUIRED_NUMERIC_FIELDS:
            if field in df_cleaned.columns:
                # 转换为字符串，处理空格问题
                df_cleaned[field] = df_cleaned[field].astype(str)

                # 替换空格为小数点（处理类似 "458 3" -> "458.3" 的情况）
                df_cleaned[field] = df_cleaned[field].str.replace(' ', '.')

                # 移除其他非数字字符（保留数字、小数点、负号）
                df_cleaned[field] = df_cleaned[field].str.replace(r'[^\d\.\-]', '', regex=True)

                # 处理多个连续小数点的情况
                df_cleaned[field] = df_cleaned[field].str.replace(r'\.+', '.', regex=True)

                # 移除开头和结尾的小数点
                df_cleaned[field] = df_cleaned[field].str.strip('.')

                # 转换为数值类型
                df_cleaned[field] = pd.to_numeric(df_cleaned[field], errors='coerce')

        return df_cleaned

    def handle_missing_values(self, df: DataFrame) -> DataFrame:
        """
        处理缺失值

        Args:
            df: DataFrame

        Returns:
            处理后的DataFrame
        """
        if not PANDAS_AVAILABLE:
            return df

        df_handled = df.copy()

        # 对数值字段的空值填充为0
        for field in self.REQUIRED_NUMERIC_FIELDS:
            if field in df_handled.columns:
                # 填充NaN值为0.0
                df_handled[field] = df_handled[field].fillna(0.0)
                print(f"字段 {field} 的空值已填充为0")

        # 对文本字段的空值进行处理
        if 'location_name' in df_handled.columns:
            df_handled['location_name'] = df_handled['location_name'].fillna('未知地区')

        if 'data_name' in df_handled.columns:
            df_handled['data_name'] = df_handled['data_name'].fillna('未命名数据')

        return df_handled

    def validate_data(self, df: DataFrame) -> Tuple[bool, List[str]]:
        """
        验证数据有效性

        Args:
            df: DataFrame

        Returns:
            (是否有效, 错误信息列表)
        """
        if not PANDAS_AVAILABLE:
            return False, ["Pandas库不可用，无法验证数据"]

        errors = []

        # 检查数据行数
        if len(df) == 0:
            errors.append("文件中没有数据行")
            return False, errors

        if len(df) > 1000:
            errors.append("数据行数不能超过1000行")

        # 检查必需字段的数据
        for field in self.REQUIRED_NUMERIC_FIELDS:
            if field not in df.columns:
                continue

            # 检查空值（允许少量空值，因为我们会自动填充）
            null_count = df[field].isnull().sum()
            total_rows = len(df)
            null_percentage = null_count / total_rows if total_rows > 0 else 0

            # 只有当空值超过50%时才报错
            if null_percentage > 0.5:
                field_name = self.get_field_chinese_name(field)
                errors.append(f"{field_name}列有{null_count}个空值（占{null_percentage:.1%}），空值过多")

            # 检查数值类型
            non_numeric_rows = []
            for idx, value in df[field].items():
                if pd.notnull(value):
                    try:
                        float(value)
                    except (ValueError, TypeError):
                        non_numeric_rows.append(idx + 2)  # +2因为从第2行开始（包含表头）

            if non_numeric_rows:
                field_name = self.get_field_chinese_name(field)
                if len(non_numeric_rows) <= 5:
                    rows_str = ', '.join(map(str, non_numeric_rows))
                    errors.append(f"{field_name}列在第{rows_str}行包含非数值数据")
                else:
                    errors.append(f"{field_name}列有{len(non_numeric_rows)}行包含非数值数据")

        return len(errors) == 0, errors

    def _filter_invalid_rows(self, df: DataFrame) -> DataFrame:
        """
        过滤无效的数据行

        Args:
            df: 原始DataFrame

        Returns:
            过滤后的DataFrame
        """
        if not PANDAS_AVAILABLE:
            return df

        # 导入re模块用于正则表达式
        import re

        # 找到地区名称列
        location_column = None
        for col in df.columns:
            if any(keyword in str(col) for keyword in ['地区', '城市', '名称', '位置']):
                location_column = col
                break

        if location_column is None:
            # 如果没找到明确的地区列，使用第一列
            location_column = df.columns[0] if len(df.columns) > 0 else None

        if location_column is None:
            return df

        print(f"使用 '{location_column}' 列进行数据过滤")

        # 定义无效数据的模式和关键词
        invalid_patterns = [
            r'^\d{4}年.*省$',  # 匹配"2022年湖北省"这样的格式
            r'^未命名.*',      # 匹配"未命名数据"、"未知地区"等
            r'^Unnamed.*',     # 匹配"Unnamed: 0"等
            r'^省份$',         # 匹配单独的"省份"
            r'^地区$',         # 匹配单独的"地区"
            r'^城市$',         # 匹配单独的"城市"
            r'^数据名称$',     # 匹配单独的"数据名称"
            r'^位置名称$',     # 匹配单独的"位置名称"
        ]

        # 定义无效关键词（不区分大小写）
        invalid_keywords = [
            '未命名', '未知', 'unnamed', 'unknown', '数据名称', '位置名称',
            '省份', '地区', '城市', '测试', 'test'
        ]

        # 创建过滤条件
        valid_mask = pd.Series([True] * len(df))

        for i, location_name in enumerate(df[location_column]):
            if pd.notna(location_name):
                location_str = str(location_name).strip()
                location_lower = location_str.lower()

                # 检查是否匹配任何无效模式
                is_invalid = False
                for pattern in invalid_patterns:
                    if re.match(pattern, location_str):
                        valid_mask.iloc[i] = False
                        print(f"过滤掉无效行(模式匹配): {location_str}")
                        is_invalid = True
                        break

                # 如果没有被模式匹配过滤，检查关键词
                if not is_invalid:
                    for keyword in invalid_keywords:
                        if keyword in location_lower:
                            valid_mask.iloc[i] = False
                            print(f"过滤掉无效行(关键词匹配): {location_str}")
                            is_invalid = True
                            break

                # 额外检查：如果location_name包含年份信息，也过滤掉
                if not is_invalid and re.search(r'\d{4}年', location_str):
                    valid_mask.iloc[i] = False
                    print(f"过滤掉包含年份的行: {location_str}")

        # 应用过滤并重置索引
        filtered_df = df[valid_mask].reset_index(drop=True)
        return filtered_df

    def convert_to_dict_list(self, df: DataFrame, batch_id: str = None) -> List[Dict]:
        """
        将DataFrame转换为字典列表

        Args:
            df: DataFrame
            batch_id: 批次ID

        Returns:
            字典列表
        """
        if not PANDAS_AVAILABLE:
            return []

        data_list = []
        
        if not batch_id:
            batch_id = f"import_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        for _, row in df.iterrows():
            data_item = {
                'data_source': 'file_import',
                'import_batch_id': batch_id
            }
            
            # 转换数据
            for col in df.columns:
                value = row[col]
                
                if col in self.REQUIRED_NUMERIC_FIELDS:
                    # 数值字段
                    if pd.notnull(value):
                        try:
                            data_item[col] = float(value)
                        except (ValueError, TypeError):
                            data_item[col] = 0.0
                    else:
                        data_item[col] = 0.0
                else:
                    # 文本字段
                    data_item[col] = str(value) if pd.notnull(value) else None
            
            data_list.append(data_item)
        
        return data_list
    
    def process_file(self, file_path: str) -> Tuple[bool, List[Dict], str]:
        """
        处理文件的完整流程
        
        Args:
            file_path: 文件路径
            
        Returns:
            (是否成功, 数据列表, 错误信息)
        """
        # 验证文件
        is_valid, error_msg = self.validate_file(file_path)
        if not is_valid:
            return False, [], error_msg
        
        # 读取文件
        success, df, error_msg = self.read_file(file_path)
        if not success:
            return False, [], error_msg
        
        # 映射列名
        success, df_mapped, error_msg = self.map_columns(df)
        if not success:
            return False, [], error_msg
        
        # 在验证之前先处理空值
        df_mapped = self.handle_missing_values(df_mapped)

        # 过滤无效数据行
        df_filtered = self._filter_invalid_rows(df_mapped)
        if len(df_filtered) != len(df_mapped):
            filtered_count = len(df_mapped) - len(df_filtered)
            print(f"过滤掉 {filtered_count} 行无效数据")
            df_mapped = df_filtered

            # 如果过滤后没有数据了
            if len(df_mapped) == 0:
                return False, [], "过滤无效数据后没有有效数据行"

        # 验证数据
        is_valid, errors = self.validate_data(df_mapped)
        if not is_valid:
            return False, [], '; '.join(errors)

        # 转换为字典列表
        data_list = self.convert_to_dict_list(df_mapped)
        
        return True, data_list, f"成功处理{len(data_list)}条数据"
    
    def get_field_chinese_name(self, field: str) -> str:
        """获取字段的中文名称"""
        name_map = {
            'data_name': '数据名称',
            'location_name': '位置名称',
            'annual_precipitation': '全年降水量',
            'drainage_network_density': '排水管网密度',
            'elevation': '平均高程值',
            'population_density': '人口密度',
            'gdp_per_area': 'GDP密度'
        }
        return name_map.get(field, field)
   