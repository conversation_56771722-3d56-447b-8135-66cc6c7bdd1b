/**
 * 地图控件库 - 指北针和比例尺控件
 * 用于为Leaflet地图添加导航辅助控件
 */

/**
 * 指北针控件类
 * 创建一个简单的北向指示器控件
 */
L.Control.Compass = L.Control.extend({
    options: {
        position: 'topright',
        title: '指北针 - 北向指示器'
    },

    onAdd: function(map) {
        // 创建控件容器
        const container = L.DomUtil.create('div', 'leaflet-control-compass');
        
        // 设置容器属性
        container.title = this.options.title;
        container.setAttribute('aria-label', this.options.title);
        
        // 创建指北针内容
        container.innerHTML = `
            <div class="compass-container">
                <div class="compass-needle">
                    <div class="compass-north">N</div>
                    <div class="compass-arrow">↑</div>
                </div>
            </div>
        `;

        // 阻止地图事件冒泡
        L.DomEvent.disableClickPropagation(container);
        L.DomEvent.disableScrollPropagation(container);

        return container;
    },

    onRemove: function(map) {
        // 清理工作（如果需要）
    }
});

// 添加到L.control命名空间
L.control.compass = function(options) {
    return new L.Control.Compass(options);
};

/**
 * 比例尺控件配置函数
 * 返回配置好的比例尺控件
 */
function createScaleControl(options = {}) {
    const defaultOptions = {
        position: 'bottomleft',
        maxWidth: 150,
        metric: true,      // 显示公制单位（米/千米）
        imperial: false,   // 不显示英制单位
        updateWhenIdle: false  // 实时更新，不等待地图停止移动
    };

    const scaleControl = L.control.scale(Object.assign(defaultOptions, options));

    // 添加自定义事件监听
    scaleControl.onAdd = function(map) {
        const container = L.Control.Scale.prototype.onAdd.call(this, map);

        // 添加自定义类名用于样式控制
        L.DomUtil.addClass(container, 'custom-scale-control');

        // 监听缩放事件，确保比例尺及时更新
        map.on('zoomend moveend', () => {
            this._update();
        });

        return container;
    };

    return scaleControl;
}

/**
 * 高级比例尺控件 - 带有更多信息显示
 */
L.Control.AdvancedScale = L.Control.extend({
    options: {
        position: 'bottomleft',
        maxWidth: 150,
        showZoomLevel: true,
        showCoordinates: false
    },

    onAdd: function(map) {
        this._map = map;

        const container = L.DomUtil.create('div', 'leaflet-control-advanced-scale');

        // 创建比例尺容器
        this._scaleContainer = L.DomUtil.create('div', 'advanced-scale-bar', container);

        // 创建信息显示容器
        if (this.options.showZoomLevel || this.options.showCoordinates) {
            this._infoContainer = L.DomUtil.create('div', 'advanced-scale-info', container);
        }

        // 阻止地图事件冒泡
        L.DomEvent.disableClickPropagation(container);
        L.DomEvent.disableScrollPropagation(container);

        // 绑定事件
        map.on('zoomend moveend', this._update, this);

        // 初始更新
        this._update();

        return container;
    },

    onRemove: function(map) {
        map.off('zoomend moveend', this._update, this);
    },

    _update: function() {
        const map = this._map;
        const zoom = map.getZoom();
        const center = map.getCenter();

        // 更新比例尺
        this._updateScale();

        // 更新附加信息
        if (this._infoContainer) {
            let infoText = '';

            if (this.options.showZoomLevel) {
                infoText += `缩放级别: ${zoom}`;
            }

            if (this.options.showCoordinates) {
                if (infoText) infoText += ' | ';
                infoText += `坐标: ${center.lat.toFixed(4)}, ${center.lng.toFixed(4)}`;
            }

            this._infoContainer.innerHTML = infoText;
        }
    },

    _updateScale: function() {
        const map = this._map;
        const maxMeters = this._getMaxMeters();

        if (maxMeters) {
            this._updateMetric(maxMeters);
        }
    },

    _getMaxMeters: function() {
        const map = this._map;
        const crs = map.options.crs;
        const y = map.getSize().y / 2;

        const maxPixels = this.options.maxWidth;
        const distance = crs.distance(
            crs.pointToLatLng(L.point(0, y), map.getZoom()),
            crs.pointToLatLng(L.point(maxPixels, y), map.getZoom())
        );

        return distance;
    },

    _updateMetric: function(maxMeters) {
        const meters = this._getRoundNum(maxMeters);
        const label = meters < 1000 ? meters + ' 米' : (meters / 1000) + ' 千米';
        const ratio = meters / maxMeters;

        this._scaleContainer.style.width = Math.round(this.options.maxWidth * ratio) + 'px';
        this._scaleContainer.innerHTML = label;
    },

    _getRoundNum: function(num) {
        const pow10 = Math.pow(10, (Math.floor(num) + '').length - 1);
        let d = num / pow10;

        d = d >= 10 ? 10 :
            d >= 5 ? 5 :
            d >= 3 ? 3 :
            d >= 2 ? 2 : 1;

        return pow10 * d;
    }
});

// 添加到L.control命名空间
L.control.advancedScale = function(options) {
    return new L.Control.AdvancedScale(options);
};

/**
 * 地图控件初始化函数
 * 为地图添加指北针和比例尺控件
 */
function initMapControls(map, options = {}) {
    if (!map) {
        console.error('地图实例不存在，无法添加控件');
        return;
    }

    const settings = {
        compass: true,
        scale: true,
        compassPosition: 'topright',
        scalePosition: 'bottomleft',
        ...options
    };

    // 添加指北针控件
    if (settings.compass) {
        const compass = L.control.compass({
            position: settings.compassPosition
        });
        compass.addTo(map);
        console.log('指北针控件已添加');
    }

    // 添加比例尺控件
    if (settings.scale) {
        const scale = createScaleControl({
            position: settings.scalePosition
        });
        scale.addTo(map);
        console.log('比例尺控件已添加');
    }
}

/**
 * 移除地图控件
 */
function removeMapControls(map) {
    if (!map) return;

    // 移除所有自定义控件
    map.eachLayer(function(layer) {
        if (layer instanceof L.Control) {
            map.removeControl(layer);
        }
    });
}

/**
 * 为图片容器添加指北针和比例尺控件
 * 用于土地利用图片等静态图片显示
 */
function addImageMapControls(containerElement, options = {}) {
    if (!containerElement) {
        console.error('容器元素不存在，无法添加图片地图控件');
        return;
    }

    const settings = {
        compass: true,
        scale: true,
        scaleValue: '1000 km', // 默认比例尺值
        bounds: null, // 图片的地理边界 [south, west, north, east]
        imageWidth: null, // 图片显示宽度（像素）
        imageHeight: null, // 图片显示高度（像素）
        ...options
    };

    // 确保容器是相对定位
    const computedStyle = window.getComputedStyle(containerElement);
    if (computedStyle.position === 'static') {
        containerElement.style.position = 'relative';
    }

    // 移除现有的控件
    removeImageMapControls(containerElement);

    // 创建控件容器
    const controlsContainer = document.createElement('div');
    controlsContainer.className = 'image-map-controls';
    controlsContainer.setAttribute('data-image-controls', 'true');

    // 添加指北针
    if (settings.compass) {
        const compass = document.createElement('div');
        compass.className = 'image-map-compass';
        compass.title = '指北针 - 北向指示器';
        compass.innerHTML = `
            <div class="compass-container">
                <div class="compass-needle">
                    <div class="compass-north">N</div>
                    <div class="compass-arrow">↑</div>
                </div>
            </div>
        `;
        controlsContainer.appendChild(compass);
    }

    // 添加比例尺
    if (settings.scale) {
        const scale = document.createElement('div');
        scale.className = 'image-map-scale';
        scale.title = '比例尺';

        // 计算比例尺
        const scaleInfo = calculateImageScale(settings);

        scale.innerHTML = `
            <div class="image-scale-bar" id="imageScaleBar">${scaleInfo.scaleText}</div>
            <div class="image-scale-info" id="imageScaleInfo">${scaleInfo.infoText}</div>
        `;
        controlsContainer.appendChild(scale);
    }

    // 添加到容器
    containerElement.appendChild(controlsContainer);

    console.log('图片地图控件已添加');
    return controlsContainer;
}

/**
 * 移除图片地图控件
 */
function removeImageMapControls(containerElement) {
    if (!containerElement) return;

    const existingControls = containerElement.querySelector('[data-image-controls="true"]');
    if (existingControls) {
        existingControls.remove();
    }
}

/**
 * 计算图片比例尺信息
 */
function calculateImageScale(settings) {
    let scaleText = '1000 km';
    let infoText = '比例尺';

    try {
        if (settings.bounds && settings.imageWidth && settings.imageHeight) {
            // 计算实际地理距离和像素距离的比例
            const [south, west, north, east] = settings.bounds;

            // 计算地理宽度（经度差转换为公里）
            const geoWidth = Math.abs(east - west);
            const geoHeight = Math.abs(north - south);

            // 粗略计算：1度经度约等于111公里（在赤道附近）
            // 考虑纬度影响，使用平均纬度
            const avgLat = (north + south) / 2;
            const latFactor = Math.cos(avgLat * Math.PI / 180);

            const realWidthKm = geoWidth * 111 * latFactor;
            const realHeightKm = geoHeight * 111;

            // 使用较小的维度来计算比例尺，确保准确性
            const realDistanceKm = Math.min(realWidthKm, realHeightKm);
            const pixelDistance = Math.min(settings.imageWidth, settings.imageHeight);

            // 计算每像素代表的公里数
            const kmPerPixel = realDistanceKm / pixelDistance;

            // 生成合适的比例尺长度（100像素）
            const scalePixels = 100;
            const scaleKm = kmPerPixel * scalePixels;

            // 调整到合适的数值
            let displayKm;
            if (scaleKm >= 1000) {
                displayKm = Math.round(scaleKm / 1000) * 1000;
                scaleText = `${displayKm / 1000}000 km`;
            } else if (scaleKm >= 100) {
                displayKm = Math.round(scaleKm / 100) * 100;
                scaleText = `${displayKm} km`;
            } else if (scaleKm >= 10) {
                displayKm = Math.round(scaleKm / 10) * 10;
                scaleText = `${displayKm} km`;
            } else if (scaleKm >= 1) {
                displayKm = Math.round(scaleKm);
                scaleText = `${displayKm} km`;
            } else {
                displayKm = scaleKm;
                scaleText = `${(displayKm * 1000).toFixed(0)} m`;
            }

            infoText = `1:${Math.round(displayKm * 100000)}`;
        } else {
            // 使用默认值或传入的值
            scaleText = settings.scaleValue || '1000 km';
            infoText = '比例尺';
        }
    } catch (error) {
        console.warn('计算比例尺时出错:', error);
        scaleText = settings.scaleValue || '1000 km';
        infoText = '比例尺';
    }

    return { scaleText, infoText };
}

/**
 * 更新图片比例尺
 * 当图片缩放时调用此函数
 */
function updateImageScale(containerElement, zoomFactor = 1, settings = {}) {
    if (!containerElement) return;

    const scaleBar = containerElement.querySelector('#imageScaleBar');
    const scaleInfo = containerElement.querySelector('#imageScaleInfo');

    if (scaleBar && scaleInfo) {
        // 根据缩放因子调整比例尺
        const adjustedSettings = { ...settings };
        if (adjustedSettings.imageWidth) {
            adjustedSettings.imageWidth *= zoomFactor;
        }
        if (adjustedSettings.imageHeight) {
            adjustedSettings.imageHeight *= zoomFactor;
        }

        const scaleData = calculateImageScale(adjustedSettings);
        scaleBar.textContent = scaleData.scaleText;
        scaleInfo.textContent = `${scaleData.infoText} (${(zoomFactor * 100).toFixed(0)}%)`;
    }
}

// 导出函数供全局使用
window.initMapControls = initMapControls;
window.removeMapControls = removeMapControls;
window.createScaleControl = createScaleControl;
