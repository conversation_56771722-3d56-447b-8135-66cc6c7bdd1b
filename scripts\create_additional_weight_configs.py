#!/usr/bin/env python3
"""
创建额外的权重配置：均衡型和人口优先型
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models import RiskWeightConfig, User

def create_additional_configs():
    """创建额外的权重配置"""
    app = create_app()
    
    with app.app_context():
        try:
            print("开始创建额外的权重配置...")
            
            # 获取管理员用户
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                print("❌ 未找到管理员用户")
                return
            
            # 创建均衡型配置
            balanced_config = RiskWeightConfig.query.filter_by(config_name='均衡型权重配置').first()
            if not balanced_config:
                balanced_config = RiskWeightConfig(
                    config_name='均衡型权重配置',
                    description='经济与人口并重的权重配置方案，适合一般城市内涝管理',
                    rainfall_weight=0.633,  # w_R 降雨权重
                    drainage_weight=0.260,  # w_D 排水能力权重
                    elevation_weight=0.107,  # w_T 地形权重
                    gdp_weight=0.5,  # ω₄ GDP密度权重
                    population_weight=0.5,  # ω₅ 人口密度权重
                    impact_weight_type='balanced',  # 均衡型
                    is_default=False,
                    is_active=True,
                    created_by=admin_user.id
                )
                db.session.add(balanced_config)
                print("✓ 创建均衡型权重配置")
            
            # 创建人口优先型配置
            population_config = RiskWeightConfig.query.filter_by(config_name='人口优先型权重配置').first()
            if not population_config:
                population_config = RiskWeightConfig(
                    config_name='人口优先型权重配置',
                    description='人口密集地区的权重配置方案，强调对人员安全的保护',
                    rainfall_weight=0.633,  # w_R 降雨权重
                    drainage_weight=0.260,  # w_D 排水能力权重
                    elevation_weight=0.107,  # w_T 地形权重
                    gdp_weight=0.4,  # ω₄ GDP密度权重
                    population_weight=0.6,  # ω₅ 人口密度权重
                    impact_weight_type='population',  # 人口优先型
                    is_default=False,
                    is_active=True,
                    created_by=admin_user.id
                )
                db.session.add(population_config)
                print("✓ 创建人口优先型权重配置")
            
            db.session.commit()
            print("✓ 额外权重配置创建完成")
            
            # 验证创建结果
            print("\n验证创建结果:")
            for config in RiskWeightConfig.query.all():
                print(f"  配置: {config.config_name}")
                print(f"    权重类型: {config.get_impact_weight_type_name()}")
                print(f"    GDP权重: {config.gdp_weight}, 人口权重: {config.population_weight}")
                print(f"    是否默认: {config.is_default}")
                print()
            
        except Exception as e:
            print(f"❌ 创建失败: {str(e)}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    create_additional_configs()
