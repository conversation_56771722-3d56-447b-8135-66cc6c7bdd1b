{% extends "base.html" %}

{% block title %}用户登录 - {{ super() }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>用户登录
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-1"></i>用户名
                            </label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-1"></i>密码
                            </label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="captcha" class="form-label">
                                <i class="fas fa-shield-alt me-1"></i>验证码
                            </label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="text" class="form-control" id="captcha" name="captcha" required
                                           placeholder="请输入验证码" maxlength="4">
                                </div>
                                <div class="col-6">
                                    <img id="captcha-image" src="" alt="验证码" class="img-fluid border rounded"
                                         style="height: 38px; cursor: pointer;" onclick="refreshCaptcha()"
                                         title="点击刷新验证码">
                                </div>
                            </div>
                            <small class="text-muted">点击图片可刷新验证码</small>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                记住我
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>登录
                            </button>
                        </div>
                    </form>
                    
                    <hr>
                    
                    <div class="text-center">
                        <p class="mb-2">还没有账户？</p>
                        <a href="{{ url_for('auth.register') }}" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus me-2"></i>立即注册
                        </a>

                        <hr class="my-3">

                        <p class="mb-2">忘记密码？</p>
                        <a href="{{ url_for('auth.forgot_password') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-key me-2"></i>找回密码
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 刷新验证码
function refreshCaptcha() {
    fetch('/auth/captcha')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('captcha-image').src = data.image;
                document.getElementById('captcha').value = '';
            }
        })
        .catch(error => {
            console.error('刷新验证码失败:', error);
        });
}

$(document).ready(function() {
    // 页面加载时获取验证码
    refreshCaptcha();

    // 表单验证
    $('form').on('submit', function(e) {
        var username = $('#username').val().trim();
        var password = $('#password').val().trim();
        var captcha = $('#captcha').val().trim();

        if (!username || !password) {
            e.preventDefault();
            alert('请填写用户名和密码');
            return false;
        }

        if (!captcha) {
            e.preventDefault();
            alert('请输入验证码');
            return false;
        }
    });

    // 自动聚焦到用户名输入框
    $('#username').focus();
});
</script>
{% endblock %}
