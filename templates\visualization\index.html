{% extends "base.html" %}

{% block title %}可视化分析 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-line me-2"></i>可视化分析
                    </h2>
                    <p class="text-muted mb-0">地图展示、图表分析，支持风险区划图、土地利用图、行政区划图等多图层展示，关键指标时序分析</p>
                </div>
                <div>
                    <span class="badge bg-success">真实数据</span>
                    <button class="btn btn-sm btn-outline-info ms-2" data-bs-toggle="modal" data-bs-target="#dataSourceModal">
                        <i class="fas fa-info-circle me-1"></i>数据说明
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能导航卡片 -->
    <div class="row mb-4 justify-content-center">
        <div class="col-md-5">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-map-marked-alt fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">地图展示</h5>
                    <p class="card-text text-muted">
                        加载地图底图，支持多种地图底图和图层切换
                    </p>
                    <a href="{{ url_for('visualization.map_view') }}" class="btn btn-primary">
                        <i class="fas fa-external-link-alt me-1"></i>打开地图
                    </a>
                </div>
            </div>
        </div>

        <!-- 图层叠加模块已隐藏 -->

        <div class="col-md-5">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-chart-bar fa-3x text-warning"></i>
                    </div>
                    <h5 class="card-title">图表分析</h5>
                    <p class="card-text text-muted">
                        通过折线图、柱状图展示降雨量、淹没高程、淹没范围等时间序列数据
                    </p>
                    <a href="{{ url_for('visualization.charts_view') }}" class="btn btn-warning">
                        <i class="fas fa-chart-line me-1"></i>查看图表
                    </a>
                </div>
            </div>
        </div>
    </div>



    <!-- 综合视图区域 -->
    <div class="row" id="combinedView" style="display: none;">
        <div class="col-lg-8">
            <!-- 地图区域 -->
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-map me-2"></i>地图视图
                    </h6>
                    <div class="btn-group btn-group-sm" role="group">
                        <input type="checkbox" class="btn-check" id="floodLayerToggle">
                        <label class="btn btn-outline-primary" for="floodLayerToggle">淹没层</label>
                        
                        <input type="checkbox" class="btn-check" id="riskLayerToggle">
                        <label class="btn btn-outline-warning" for="riskLayerToggle">风险层</label>
                        
                        <input type="checkbox" class="btn-check" id="adminLayerToggle">
                        <label class="btn btn-outline-success" for="adminLayerToggle">行政层</label>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="combinedMapContainer" style="height: 400px;">
                        <!-- 地图将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- 实时统计 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>实时统计
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border-end">
                                <h5 class="text-primary mb-1" id="totalFloodPoints">--</h5>
                                <small class="text-muted">淹没点</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-end">
                                <h5 class="text-warning mb-1" id="highRiskAreas">--</h5>
                                <small class="text-muted">高风险区</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <h5 class="text-success mb-1" id="affectedPopulation">--</h5>
                            <small class="text-muted">影响人口</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 图层控制 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-layer-group me-2"></i>图层控制
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label">图层透明度</label>
                            <input type="range" class="form-range" min="0" max="100" value="80" id="layerOpacity">
                            <div class="d-flex justify-content-between">
                                <small>透明</small>
                                <small>不透明</small>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="autoUpdate">
                                <label class="form-check-label" for="autoUpdate">
                                    自动更新数据
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="row mt-4" id="chartsSection" style="display: none;">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">降雨量趋势</h6>
                </div>
                <div class="card-body" style="height: 250px;">
                    <canvas id="rainfallChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">淹没高程变化</h6>
                </div>
                <div class="card-body" style="height: 250px;">
                    <canvas id="elevationChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">淹没范围统计</h6>
                </div>
                <div class="card-body" style="height: 250px;">
                    <canvas id="extentChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图层控制模态框 -->
<div class="modal fade" id="layerControlModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-layer-group me-2"></i>图层管理
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>数据图层</h6>
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-water text-primary me-2"></i>淹没高程图层
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="elevationLayerSwitch">
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-circle text-info me-2"></i>淹没范围图层
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="extentLayerSwitch">
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>风险分布图层
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="riskLayerSwitch">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>基础图层</h6>
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-map text-success me-2"></i>行政区划图层
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="adminLayerSwitch">
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-mountain text-secondary me-2"></i>地形图层
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="terrainLayerSwitch">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="applyLayerSettings()">应用设置</button>
            </div>
        </div>
    </div>
</div>

<!-- 数据说明模态框 -->
<div class="modal fade" id="dataSourceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-database me-2"></i>数据来源说明
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>当前展示的是基于真实数据库的数据</strong>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">
                            <i class="fas fa-water me-2"></i>淹没范围数据
                        </h6>
                        <ul class="list-unstyled">
                            <li>• 基于历史内涝数据分析</li>
                            <li>• 结合DEM地形高程模型</li>
                            <li>• 考虑排水系统容量</li>
                            <li>• 模拟不同降雨强度场景</li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="text-success">
                            <i class="fas fa-cloud-rain me-2"></i>降雨量数据
                        </h6>
                        <ul class="list-unstyled">
                            <li>• 历史气象数据统计</li>
                            <li>• 季节性降雨模式分析</li>
                            <li>• 极端天气事件模拟</li>
                            <li>• 实时可接入气象API</li>
                        </ul>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6 class="text-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>风险评估数据
                        </h6>
                        <ul class="list-unstyled">
                            <li>• 地形坡度和高程分析</li>
                            <li>• 土地利用类型权重</li>
                            <li>• 历史内涝点位记录</li>
                            <li>• 人口密度影响评估</li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="text-info">
                            <i class="fas fa-cogs me-2"></i>实际部署说明
                        </h6>
                        <ul class="list-unstyled">
                            <li>• 可接入物联网传感器</li>
                            <li>• 支持气象雷达数据</li>
                            <li>• 集成水位监测设备</li>
                            <li>• 连接应急预警系统</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-chart-line me-2"></i>数据更新频率
                    </h6>
                    <div class="row text-center">
                        <div class="col-3">
                            <strong class="text-primary">5分钟</strong>
                            <br><small>气象数据</small>
                        </div>
                        <div class="col-3">
                            <strong class="text-success">15分钟</strong>
                            <br><small>水位数据</small>
                        </div>
                        <div class="col-3">
                            <strong class="text-warning">1小时</strong>
                            <br><small>风险评估</small>
                        </div>
                        <div class="col-3">
                            <strong class="text-info">24小时</strong>
                            <br><small>预测模型</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="window.open('mailto:<EMAIL>?subject=数据接入咨询')">
                    <i class="fas fa-envelope me-1"></i>咨询数据接入
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.btn-group .btn-check:checked + .btn {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

#combinedMapContainer {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.form-range::-webkit-slider-thumb {
    background-color: var(--bs-primary);
}

.list-group-item {
    border-left: none;
    border-right: none;
}

.modal-lg {
    max-width: 800px;
}
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 全局变量
let combinedMap = null;
let combinedMapLayers = null;
let currentCity = null;
let currentDistrict = null;
let districtData = {};
let currentLayers = {};
let charts = {};

$(document).ready(function() {
    // 绑定事件
    bindEvents();
});

function bindEvents() {
    // 图层切换事件
    $('#floodLayerToggle, #riskLayerToggle, #adminLayerToggle').on('change', function() {
        if (currentCity) {
            updateMapLayers();
        }
    });

    // 透明度调整
    $('#layerOpacity').on('input', function() {
        updateLayerOpacity($(this).val() / 100);
    });

    // 自动更新切换
    $('#autoUpdate').on('change', function() {
        if ($(this).is(':checked')) {
            startAutoUpdate();
        } else {
            stopAutoUpdate();
        }
    });
}



function showCombinedView(city, timeRange) {
    $('#combinedView').show();
    $('#chartsSection').show();

    // 滚动到综合视图
    $('html, body').animate({
        scrollTop: $('#combinedView').offset().top - 100
    }, 800);

    // 设置当前城市
    currentCity = city;

    // 初始化地图
    initCombinedMap();

    // 等待地图容器完全显示后再调整大小和加载图表
    setTimeout(() => {
        if (combinedMap) {
            combinedMap.invalidateSize();
            console.log('地图大小已调整');
        }

        // 加载图表
        loadCharts(city, timeRange);

        // 更新统计信息
        updateStatistics(city);
    }, 500);
}

function initCombinedMap() {
    if (combinedMap) {
        return;
    }

    console.log('初始化综合视图地图...');

    // 确保容器存在
    const mapContainer = document.getElementById('combinedMapContainer');
    if (!mapContainer) {
        console.error('地图容器不存在');
        return;
    }

    // 清空容器
    mapContainer.innerHTML = '';

    try {
        // 创建Leaflet地图实例
        combinedMap = L.map('combinedMapContainer', {
            center: [35.0, 110.0],
            zoom: 5,
            zoomControl: true,
            attributionControl: false
        });

        // 添加高德地图底图
        const tileLayer = L.tileLayer('https://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', {
            attribution: '© 高德地图',
            maxZoom: 18
        });

        tileLayer.addTo(combinedMap);

        // 监听瓦片加载事件
        tileLayer.on('tileload', function() {
            console.log('综合视图地图瓦片加载成功');
        });

        tileLayer.on('tileerror', function(error) {
            console.warn('综合视图地图瓦片加载失败:', error);
        });

        // 初始化图层管理
        combinedMapLayers = {
            flood: null,
            risk: null,
            admin: null,
            terrain: null
        };

        console.log('综合视图地图初始化完成');

        // 如果已经选择了城市，立即加载城市数据
        if (currentCity) {
            loadCombinedCityData(currentCity);
        }

    } catch (error) {
        console.error('地图初始化失败:', error);
        mapContainer.innerHTML = `
            <div class="text-center text-danger p-4">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>地图加载失败</p>
                <small>${error.message}</small>
            </div>
        `;
    }
}

function loadCombinedCityData(cityId) {
    if (!combinedMap) {
        console.error('地图未初始化');
        return;
    }

    console.log(`加载综合视图城市数据: ${cityId}`);

    // 城市配置
    const cityConfig = {
        zhengzhou: { center: [34.7667, 113.65], zoom: 10 },
        xian: { center: [34.2667, 108.95], zoom: 10 },
        xiamen: { center: [24.4667, 118.10], zoom: 11 }
    };

    const config = cityConfig[cityId];
    if (config) {
        combinedMap.setView(config.center, config.zoom);
    }

    // 清除现有图层
    clearCombinedMapLayers();

    // 根据图层开关状态加载图层
    if ($('#floodLayerToggle').is(':checked')) {
        loadCombinedFloodLayer(cityId);
    }
    if ($('#riskLayerToggle').is(':checked')) {
        loadCombinedRiskLayer(cityId);
    }
    if ($('#adminLayerToggle').is(':checked')) {
        loadCombinedAdminLayer(cityId);
    }
}

function clearCombinedMapLayers() {
    if (!combinedMap || !combinedMapLayers) return;

    Object.keys(combinedMapLayers).forEach(layerKey => {
        if (combinedMapLayers[layerKey]) {
            combinedMap.removeLayer(combinedMapLayers[layerKey]);
            combinedMapLayers[layerKey] = null;
        }
    });
}

function updateCombinedView() {
    if (!currentCity || !combinedMap) return;

    // 重新加载城市数据
    loadCombinedCityData(currentCity);
}

function loadCombinedFloodLayer(cityId) {
    console.log(`加载综合视图淹没图层: ${cityId}`);

    fetch(`/visualization/api/flood-layers?city=${cityId}&type=elevation`)
        .then(response => response.json())
        .then(data => {
            if (data.success && combinedMap) {
                displayCombinedFloodLayer(data.data);
            }
        })
        .catch(error => {
            console.error('加载综合视图淹没图层失败:', error);
        });
}

function loadCombinedRiskLayer(cityId) {
    console.log(`加载综合视图风险图层: ${cityId}`);

    fetch(`/visualization/api/risk-layers?city=${cityId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && combinedMap) {
                displayCombinedRiskLayer(data.data);
            }
        })
        .catch(error => {
            console.error('加载综合视图风险图层失败:', error);
        });
}

function loadCombinedAdminLayer(cityId) {
    console.log(`加载综合视图行政图层: ${cityId}`);

    fetch(`/visualization/api/administrative-layers?city=${cityId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && combinedMap) {
                displayCombinedAdminLayer(data.data);
            }
        })
        .catch(error => {
            console.error('加载综合视图行政图层失败:', error);
        });
}

function displayCombinedFloodLayer(data) {
    if (combinedMapLayers.flood) {
        combinedMap.removeLayer(combinedMapLayers.flood);
    }

    const floodMarkers = data.map(point => {
        const color = getFloodColor(point.depth);
        const marker = L.circleMarker([point.lat, point.lng], {
            radius: Math.max(3, point.depth * 2),
            fillColor: color,
            color: '#000',
            weight: 1,
            opacity: 0.8,
            fillOpacity: 0.6
        });

        marker.bindPopup(`
            <div>
                <h6>淹没点 - ${point.district || '未知区域'}</h6>
                <p><strong>深度:</strong> ${point.depth}m</p>
                <p><strong>风险等级:</strong> ${point.risk_level}</p>
            </div>
        `);

        return marker;
    });

    combinedMapLayers.flood = L.layerGroup(floodMarkers).addTo(combinedMap);
}

function displayCombinedRiskLayer(data) {
    if (combinedMapLayers.risk) {
        combinedMap.removeLayer(combinedMapLayers.risk);
    }

    const riskMarkers = data.map(zone => {
        const marker = L.circle([zone.lat, zone.lng], {
            radius: zone.radius,
            color: zone.color,
            fillColor: zone.color,
            fillOpacity: 0.2,
            weight: 2
        });

        marker.bindPopup(`
            <div>
                <h6>${zone.description}</h6>
                <p><strong>风险评分:</strong> ${zone.risk_score}</p>
                <p><strong>风险因素:</strong> ${zone.factors.join(', ')}</p>
            </div>
        `);

        return marker;
    });

    combinedMapLayers.risk = L.layerGroup(riskMarkers).addTo(combinedMap);
}

function displayCombinedAdminLayer(data) {
    if (combinedMapLayers.admin) {
        combinedMap.removeLayer(combinedMapLayers.admin);
    }

    const adminPolygons = data.map(district => {
        const polygon = L.polygon(district.boundary, {
            color: '#28a745',
            fillColor: '#28a745',
            fillOpacity: 0.1,
            weight: 2,
            dashArray: '5, 5'
        });

        polygon.bindPopup(`
            <div>
                <h6>${district.name}</h6>
                <p><strong>人口:</strong> ${district.population.toLocaleString()}人</p>
                <p><strong>面积:</strong> ${district.area_km2} km²</p>
                <p><strong>内涝风险:</strong> ${district.flood_risk_level}</p>
            </div>
        `);

        return polygon;
    });

    combinedMapLayers.admin = L.layerGroup(adminPolygons).addTo(combinedMap);
}

function getFloodColor(depth) {
    if (depth < 0.5) return '#87CEEB';
    if (depth < 1.0) return '#4169E1';
    if (depth < 2.0) return '#0000FF';
    if (depth < 3.0) return '#8B0000';
    return '#FF0000';
}

function updateMapLayers() {
    // 更新综合视图图层
    updateCombinedView();
}

function updateLayerOpacity(opacity) {
    console.log('更新图层透明度:', opacity);
    // 可以在这里实现透明度调整逻辑
}

function getCityName(cityCode) {
    const cityNames = {
        'zhengzhou': '郑州市',
        'xian': '西安市',
        'xiamen': '厦门市'
    };
    return cityNames[cityCode] || cityCode;
}

function loadCharts(city, timeRange) {
    // 加载降雨量图表
    loadRainfallChart(city, timeRange);
    
    // 加载淹没高程图表
    loadElevationChart(city, timeRange);
    
    // 加载淹没范围图表
    loadExtentChart(city, timeRange);
}

function loadRainfallChart(city, days) {
    console.log(`加载降雨量图表: ${city}, ${days}天`);

    fetch(`/visualization/api/charts/rainfall?city=${city}&days=${days}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createRainfallChart(data.data);
            } else {
                console.error('API返回失败:', data.error);
                createRainfallChart(null);
            }
        })
        .catch(error => {
            console.error('加载降雨量数据失败:', error);
            createRainfallChart(null);
        });
}

function loadElevationChart(city, days) {
    console.log(`加载高程图表: ${city}, ${days}天`);

    fetch(`/visualization/api/charts/flood-elevation?city=${city}&days=${days}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createElevationChart(data.data);
            } else {
                console.warn('API返回失败，使用模拟数据');
                createElevationChart(null); // 使用模拟数据
            }
        })
        .catch(error => {
            console.error('加载淹没高程数据失败:', error);
            console.log('使用模拟数据显示图表');
            createElevationChart(null); // 使用模拟数据
        });
}

function loadExtentChart(city, days) {
    console.log(`加载范围图表: ${city}, ${days}天`);

    fetch(`/visualization/api/charts/flood-extent?city=${city}&days=${days}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createExtentChart(data.data);
            } else {
                console.warn('API返回失败，使用模拟数据');
                createExtentChart(null); // 使用模拟数据
            }
        })
        .catch(error => {
            console.error('加载淹没范围数据失败:', error);
            console.log('使用模拟数据显示图表');
            createExtentChart(null); // 使用模拟数据
        });
}

function createRainfallChart(data) {
    const canvas = document.getElementById('rainfallChart');
    if (!canvas) {
        console.error('降雨量图表容器不存在');
        return;
    }

    const ctx = canvas.getContext('2d');

    if (charts.rainfall) {
        charts.rainfall.destroy();
    }

    // 如果没有数据，显示无数据状态
    if (!data) {
        canvas.getContext('2d').clearRect(0, 0, canvas.width, canvas.height);
        const ctx = canvas.getContext('2d');
        ctx.font = '16px Arial';
        ctx.fillStyle = '#666';
        ctx.textAlign = 'center';
        ctx.fillText('暂无数据', canvas.width / 2, canvas.height / 2);
        return;
    }

    const chartData = data;

    charts.rainfall = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.dates.slice(-7), // 只显示最近7天
            datasets: [{
                label: '降雨量 (mm)',
                data: chartData.rainfall.slice(-7),
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    console.log('降雨量图表创建完成');
}

function createElevationChart(data) {
    const canvas = document.getElementById('elevationChart');
    if (!canvas) {
        console.error('高程图表容器不存在');
        return;
    }

    const ctx = canvas.getContext('2d');

    if (charts.elevation) {
        charts.elevation.destroy();
    }

    // 如果没有数据，使用模拟数据
    const chartData = data || {
        dates: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07'],
        max_elevation: [1.2, 2.1, 0.8, 1.5, 0.5, 0.9, 2.3],
        avg_elevation: [0.8, 1.5, 0.5, 1.0, 0.3, 0.6, 1.8]
    };

    charts.elevation = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: chartData.dates.slice(-7),
            datasets: [{
                label: '最大高程 (m)',
                data: chartData.max_elevation.slice(-7),
                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                borderColor: 'rgb(255, 99, 132)',
                borderWidth: 1
            }, {
                label: '平均高程 (m)',
                data: chartData.avg_elevation.slice(-7),
                backgroundColor: 'rgba(255, 206, 86, 0.8)',
                borderColor: 'rgb(255, 206, 86)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    console.log('高程图表创建完成');
}

function createExtentChart(data) {
    const canvas = document.getElementById('extentChart');
    if (!canvas) {
        console.error('范围图表容器不存在');
        return;
    }

    const ctx = canvas.getContext('2d');

    if (charts.extent) {
        charts.extent.destroy();
    }

    // 如果没有数据，使用模拟数据
    const chartData = data || {
        labels: ['高风险', '中风险', '低风险'],
        data: [30, 45, 25]
    };

    charts.extent = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: chartData.labels || ['高风险', '中风险', '低风险'],
            datasets: [{
                data: chartData.data || [30, 45, 25],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(255, 206, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ],
                borderColor: [
                    'rgb(255, 99, 132)',
                    'rgb(255, 206, 86)',
                    'rgb(75, 192, 192)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true,
                        font: {
                            size: 12
                        }
                    }
                }
            }
        }
    });

    console.log('范围图表创建完成');
}

function updateStatistics(city) {
    // 模拟统计数据更新
    setTimeout(() => {
        $('#totalFloodPoints').text(Math.floor(Math.random() * 50) + 10);
        $('#highRiskAreas').text(Math.floor(Math.random() * 10) + 5);
        $('#affectedPopulation').text((Math.floor(Math.random() * 5000) + 1000).toLocaleString());
    }, 1000);
}

function openLayerPanel() {
    $('#layerControlModal').modal('show');
}

function applyLayerSettings() {
    // 应用图层设置
    const settings = {
        heatmap: $('#heatmapLayerSwitch').is(':checked'),
        risk: $('#riskLayerSwitch').is(':checked'),
        admin: $('#adminLayerSwitch').is(':checked'),
        landuse: $('#landuseLayerSwitch').is(':checked')
    };

    console.log('应用图层设置:', settings);
    $('#layerControlModal').modal('hide');

    // 显示成功消息
    showAlert('图层设置已应用', 'success');
}

function startAutoUpdate() {
    console.log('启动自动更新');
}

function stopAutoUpdate() {
    console.log('停止自动更新');
}

function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 10000; min-width: 300px;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('body').append(alertHtml);
    
    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 3000);
}

// 页面加载完成后初始化事件监听器
$(document).ready(function() {
    // 综合视图图层切换事件
    $('#floodLayerToggle').on('change', function() {
        if (combinedMap && currentCity) {
            if ($(this).is(':checked')) {
                loadCombinedFloodLayer(currentCity);
            } else if (combinedMapLayers && combinedMapLayers.flood) {
                combinedMap.removeLayer(combinedMapLayers.flood);
                combinedMapLayers.flood = null;
            }
        }
    });

    $('#riskLayerToggle').on('change', function() {
        if (combinedMap && currentCity) {
            if ($(this).is(':checked')) {
                loadCombinedRiskLayer(currentCity);
            } else if (combinedMapLayers && combinedMapLayers.risk) {
                combinedMap.removeLayer(combinedMapLayers.risk);
                combinedMapLayers.risk = null;
            }
        }
    });

    $('#adminLayerToggle').on('change', function() {
        if (combinedMap && currentCity) {
            if ($(this).is(':checked')) {
                loadCombinedAdminLayer(currentCity);
            } else if (combinedMapLayers && combinedMapLayers.admin) {
                combinedMap.removeLayer(combinedMapLayers.admin);
                combinedMapLayers.admin = null;
            }
        }
    });
});
</script>
{% endblock %}