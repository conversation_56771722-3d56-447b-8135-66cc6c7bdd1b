{% extends "base.html" %}

{% block title %}访问被拒绝 - {{ super() }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="error-page">
                <div class="error-code mb-4">
                    <h1 class="display-1 text-warning">403</h1>
                </div>
                <div class="error-message mb-4">
                    <h3 class="mb-3">访问被拒绝</h3>
                    <p class="text-muted">
                        抱歉，您没有权限访问此页面。
                        请联系管理员获取相应权限，或使用有权限的账户登录。
                    </p>
                </div>
                <div class="error-actions">
                    <a href="{{ url_for('main.index') }}" class="btn btn-primary me-3">
                        <i class="fas fa-home me-2"></i>返回首页
                    </a>
                    {% if not current_user.is_authenticated %}
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-sign-in-alt me-2"></i>重新登录
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.error-page {
    padding: 60px 0;
}

.error-code h1 {
    font-size: 8rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .error-code h1 {
        font-size: 6rem;
    }
}
</style>
{% endblock %}
