{% extends "base.html" %}

{% block title %}找回密码 - {{ super() }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-warning text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-key me-2"></i>找回密码
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        请输入您的用户名和注册邮箱，我们将帮助您重置密码。
                    </div>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-1"></i>用户名
                            </label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>注册邮箱
                            </label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="captcha" class="form-label">
                                <i class="fas fa-shield-alt me-1"></i>验证码
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="captcha" name="captcha" required>
                                <div class="input-group-text p-0">
                                    <img id="captcha-image" src="" alt="验证码" style="height: 38px; cursor: pointer;" onclick="refreshCaptcha()">
                                </div>
                            </div>
                            <small class="form-text text-muted">点击图片刷新验证码</small>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-search me-2"></i>验证身份
                            </button>
                        </div>
                    </form>
                    
                    <hr>
                    
                    <div class="text-center">
                        <p class="mb-2">想起密码了？</p>
                        <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>返回登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 刷新验证码
function refreshCaptcha() {
    fetch('/auth/captcha')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('captcha-image').src = data.image;
                document.getElementById('captcha').value = '';
            }
        })
        .catch(error => {
            console.error('刷新验证码失败:', error);
        });
}

$(document).ready(function() {
    // 页面加载时获取验证码
    refreshCaptcha();

    // 表单验证
    $('form').on('submit', function(e) {
        var username = $('#username').val().trim();
        var email = $('#email').val().trim();
        var captcha = $('#captcha').val().trim();

        if (!username || !email) {
            e.preventDefault();
            alert('请填写用户名和邮箱');
            return false;
        }

        if (!captcha) {
            e.preventDefault();
            alert('请输入验证码');
            return false;
        }
    });

    // 自动聚焦到用户名输入框
    $('#username').focus();
});
</script>
{% endblock %}
