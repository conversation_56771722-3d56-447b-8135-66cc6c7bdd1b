"""
土地利用数据自动化处理服务
实现从数据上传到地图展示的全自动化处理流程
"""

import os
import re
import logging
from typing import Optional, Dict, List, Tuple
from flask import current_app
from app.models import GISLayer, Dataset, db
from app.services.gis_data_service import GISDataService

logger = logging.getLogger(__name__)

class LanduseAutoProcessor:
    """土地利用数据自动化处理器"""
    
    # 支持的土地利用文件格式
    SUPPORTED_EXTENSIONS = ['.tif', '.tiff', '.geotiff']
    
    # 省份名称识别模式
    PROVINCE_PATTERNS = [
        r'(北京|天津|上海|重庆)(?:市)?',
        r'(河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾)(?:省)?',
        r'(内蒙古|广西|西藏|宁夏|新疆)(?:自治区)?',
        r'(香港|澳门)(?:特别行政区)?'
    ]
    
    # 土地利用类型关键词映射
    LANDUSE_KEYWORDS = {
        '耕地': ['耕地', 'farmland', 'cropland', 'agricultural', 'cultivated', '农田', '农地', '种植'],
        '林地': ['林地', 'forest', 'woodland', 'tree', '森林', '林业', '植被'],
        '草地': ['草地', 'grassland', 'pasture', 'meadow', '草原', '牧场'],
        '水域': ['水域', 'water', 'river', 'lake', 'wetland', '河流', '湖泊', '湿地'],
        '建设用地': ['建设', 'urban', 'built', 'construction', 'developed', '城市', '建筑'],
        '住宅用地': ['住宅', 'residential', '居住', '住房'],
        '商业用地': ['商业', 'commercial', '商务', '商店'],
        '工业用地': ['工业', 'industrial', '制造', '厂房'],
        '交通用地': ['交通', 'transport', 'road', 'railway', '道路', '铁路'],
        '未利用地': ['未利用', 'unused', 'barren', 'wasteland', '荒地', '裸地']
    }
    
    @classmethod
    def is_landuse_file(cls, filename: str) -> bool:
        """
        判断文件是否为土地利用数据文件
        
        Args:
            filename: 文件名
            
        Returns:
            是否为土地利用文件
        """
        filename_lower = filename.lower()
        
        # 检查文件扩展名
        if not any(filename_lower.endswith(ext) for ext in cls.SUPPORTED_EXTENSIONS):
            return False
        
        # 检查文件名中的土地利用关键词
        landuse_indicators = [
            'landuse', 'land_use', 'land-use', 'clcd', 'lulc',
            '土地利用', '土地覆盖', '土地分类', '用地', '地类'
        ]
        
        return any(indicator in filename_lower for indicator in landuse_indicators)
    
    @classmethod
    def extract_province_from_filename(cls, filename: str) -> Optional[str]:
        """
        从文件名中提取省份信息
        
        Args:
            filename: 文件名
            
        Returns:
            省份名称，如果未找到返回None
        """
        for pattern in cls.PROVINCE_PATTERNS:
            match = re.search(pattern, filename)
            if match:
                province = match.group(1)
                # 标准化省份名称
                if province in ['北京', '天津', '上海', '重庆']:
                    return f"{province}市"
                elif province in ['内蒙古', '广西', '西藏', '宁夏', '新疆']:
                    return f"{province}自治区"
                elif province in ['香港', '澳门']:
                    return f"{province}特别行政区"
                else:
                    return f"{province}省"
        
        return None
    
    @classmethod
    def detect_landuse_type_from_filename(cls, filename: str) -> str:
        """
        从文件名中检测土地利用类型
        
        Args:
            filename: 文件名
            
        Returns:
            土地利用类型
        """
        filename_lower = filename.lower()
        
        for landuse_type, keywords in cls.LANDUSE_KEYWORDS.items():
            if any(keyword in filename_lower for keyword in keywords):
                return landuse_type
        
        return '其他'
    
    @classmethod
    def process_uploaded_file(cls, file_path: str, original_filename: str, user_id: int = 1) -> Optional[GISLayer]:
        """
        处理上传的土地利用文件
        
        Args:
            file_path: 文件路径
            original_filename: 原始文件名
            user_id: 用户ID
            
        Returns:
            创建的GISLayer对象，失败返回None
        """
        try:
            logger.info(f"开始处理土地利用文件: {original_filename}")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return None
            
            # 检查是否为土地利用文件
            if not cls.is_landuse_file(original_filename):
                logger.info(f"文件不是土地利用数据: {original_filename}")
                return None
            
            # 创建文件信息
            file_info = {
                'name': original_filename,
                'path': file_path,
                'size': os.path.getsize(file_path)
            }
            
            # 使用GIS数据服务处理栅格文件
            layer = GISDataService.process_raster_file(file_info, os.path.dirname(file_path), user_id)
            
            if not layer:
                logger.error(f"GIS数据服务处理失败: {original_filename}")
                return None
            
            # 自动设置土地利用相关属性
            layer.data_category = 'landuse'
            
            # 提取省份信息
            province = cls.extract_province_from_filename(original_filename)
            if province:
                layer.province = province
                logger.info(f"检测到省份: {province}")
            
            # 检测土地利用类型
            landuse_type = cls.detect_landuse_type_from_filename(original_filename)
            
            # 更新图层名称
            if province and landuse_type != '其他':
                layer.name = f"{province}{landuse_type}数据_{original_filename}"
            elif province:
                layer.name = f"{province}土地利用数据_{original_filename}"
            else:
                layer.name = f"土地利用数据_{original_filename}"
            
            # 设置描述信息
            layer.description = f"自动处理的土地利用数据 - 原文件: {original_filename}"
            if province:
                layer.description += f" - 省份: {province}"
            if landuse_type != '其他':
                layer.description += f" - 类型: {landuse_type}"
            
            # 确保处理状态为已处理
            layer.processing_status = 'processed'
            
            # 保存到数据库
            db.session.add(layer)
            db.session.commit()
            
            logger.info(f"成功创建土地利用图层: {layer.name} (ID: {layer.id})")
            return layer
            
        except Exception as e:
            logger.error(f"处理土地利用文件失败: {e}")
            db.session.rollback()
            return None
    
    @classmethod
    def process_dataset_files(cls, dataset: Dataset, user_id: int = 1) -> List[GISLayer]:
        """
        处理数据集中的所有土地利用文件
        
        Args:
            dataset: 数据集对象
            user_id: 用户ID
            
        Returns:
            创建的GISLayer对象列表
        """
        created_layers = []
        
        try:
            if not dataset.file_path or not os.path.exists(dataset.file_path):
                logger.warning(f"数据集路径不存在: {dataset.file_path}")
                return created_layers
            
            # 遍历数据集目录中的所有文件
            for root, dirs, files in os.walk(dataset.file_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    # 处理土地利用文件
                    layer = cls.process_uploaded_file(file_path, file, user_id)
                    if layer:
                        created_layers.append(layer)
            
            logger.info(f"数据集 {dataset.name} 处理完成，创建了 {len(created_layers)} 个土地利用图层")
            
        except Exception as e:
            logger.error(f"处理数据集失败: {e}")
        
        return created_layers
    
    @classmethod
    def get_province_landuse_layers(cls, province_name: str) -> List[GISLayer]:
        """
        获取指定省份的所有土地利用图层
        
        Args:
            province_name: 省份名称
            
        Returns:
            土地利用图层列表
        """
        try:
            query = GISLayer.query.filter(
                GISLayer.data_category == 'landuse',
                GISLayer.processing_status == 'processed'
            )
            
            if province_name:
                query = query.filter(GISLayer.province == province_name)
            
            layers = query.order_by(GISLayer.created_at.desc()).all()
            
            # 过滤掉文件不存在的图层
            valid_layers = []
            for layer in layers:
                if layer.file_path and os.path.exists(layer.file_path):
                    valid_layers.append(layer)
                else:
                    logger.warning(f"图层文件不存在，跳过: {layer.name} - {layer.file_path}")
            
            return valid_layers
            
        except Exception as e:
            logger.error(f"获取省份土地利用图层失败: {e}")
            return []
    
    @classmethod
    def cleanup_invalid_layers(cls) -> int:
        """
        清理无效的土地利用图层（文件不存在的）
        
        Returns:
            清理的图层数量
        """
        try:
            layers = GISLayer.query.filter_by(data_category='landuse').all()
            cleaned_count = 0
            
            for layer in layers:
                if not layer.file_path or not os.path.exists(layer.file_path):
                    logger.info(f"清理无效图层: {layer.name}")
                    db.session.delete(layer)
                    cleaned_count += 1
            
            db.session.commit()
            logger.info(f"清理完成，删除了 {cleaned_count} 个无效图层")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理无效图层失败: {e}")
            db.session.rollback()
            return 0


# 创建全局实例
landuse_auto_processor = LanduseAutoProcessor()
