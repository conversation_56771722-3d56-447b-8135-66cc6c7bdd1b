/* 可视化模块专用样式 */

/* 图层管理面板样式 */
.layer-panel {
    position: relative;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    transition: background-color 0.3s ease;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.layer-panel-header {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.layer-panel-body {
    padding: 15px;
}

.layer-item {
    margin-bottom: 15px;
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #fff;
}

.layer-item:last-child {
    margin-bottom: 0;
}

.layer-header {
    display: flex;
    justify-content-between;
    align-items: center;
    margin-bottom: 10px;
}

.layer-name {
    font-weight: 600;
    color: #495057;
}

.layer-toggle {
    transform: scale(1.2);
}

.layer-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.opacity-control {
    flex: 1;
}

.opacity-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.opacity-slider {
    width: 100%;
}

.opacity-value {
    font-size: 0.85rem;
    color: #495057;
    font-weight: 500;
    min-width: 35px;
    text-align: right;
}

/* 热力图控制面板 */
.heatmap-controls {
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 6px;
    margin-top: 10px;
}

.heatmap-intensity {
    display: flex;
    align-items: center;
    gap: 10px;
}

.intensity-slider {
    flex: 1;
}

/* 图层顺序调整 */
.layer-order-controls {
    display: flex;
    gap: 5px;
}

.layer-order-btn {
    padding: 2px 6px;
    font-size: 0.75rem;
    border: 1px solid #dee2e6;
    background: #fff;
    color: #6c757d;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s;
}

.layer-order-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.layer-order-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 地图工具栏增强 */
.map-toolbar {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.toolbar-group {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 6px;
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 热力图图例 */
.heatmap-legend {
    position: absolute;
    bottom: 30px;
    left: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 6px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.legend-title {
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
}

.legend-gradient {
    width: 200px;
    height: 20px;
    background: linear-gradient(to right, 
        rgba(0, 0, 255, 0.6) 0%, 
        rgba(0, 255, 255, 0.6) 25%, 
        rgba(0, 255, 0, 0.6) 50%, 
        rgba(255, 255, 0, 0.6) 75%, 
        rgba(255, 0, 0, 0.6) 100%);
    border-radius: 3px;
    margin-bottom: 5px;
}

.legend-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .layer-panel {
        width: 280px;
        right: 5px;
        top: 5px;
    }
    
    .map-toolbar {
        left: 5px;
        top: 5px;
    }
    
    .heatmap-legend {
        left: 5px;
        bottom: 20px;
    }
    
    .legend-gradient {
        width: 150px;
    }
}

/* 图表分析页面样式 */
.chart-controls {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.indicator-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.indicator-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
}

.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
